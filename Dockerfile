# Use the official PHP image with Apache (PHP 8.2)
FROM php:8.2-apache

# Install system dependencies and PHP extensions
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    gnupg \
    ca-certificates \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip \
    && a2enmod rewrite \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Verify Node.js and npm installation
RUN node -v
RUN npm -v

# Set working directory to /var/task
WORKDIR /var/task

# Copy existing application directory contents
COPY . /var/task

# Set Apache DocumentRoot to /var/task/public
RUN sed -i 's|/var/www/html|/var/task/public|g' /etc/apache2/sites-available/000-default.conf

# Allow .htaccess Overrides and Permissions
RUN echo "<Directory /var/task/public>\n\
    AllowOverride All\n\
    Require all granted\n\
</Directory>" > /etc/apache2/conf-available/laravel.conf && \
    a2enconf laravel

# Ensure storage and bootstrap/cache directories exist
RUN mkdir -p storage/framework/cache/data storage/logs bootstrap/cache

# Modify .env file to set cache driver to array and environment to local (for development)
RUN if [ -f .env ]; then \
        sed -i 's/^CACHE_DRIVER=.*/CACHE_DRIVER=array/' .env && \
        sed -i 's/^APP_ENV=.*/APP_ENV=local/' .env; \
    else \
        echo "CACHE_DRIVER=array" >> .env && \
        echo "APP_ENV=local" >> .env; \
    fi

# Set temporary permissive permissions to allow composer and npm to write
RUN chmod -R 777 storage bootstrap/cache

# Reset permissions to more secure settings
RUN chown -R www-data:www-data /var/task \
    && chmod -R 755 storage bootstrap/cache

# Install Node.js dependencies
RUN npm install

# Expose port 80
EXPOSE 80

# Start Apache in the foreground
CMD ["apache2-foreground"]
