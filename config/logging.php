<?php

use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    'default' => env('LOG_CHANNEL', 'stack'),

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 4,
        ],

        'events' => [
            'driver' => 'daily',
            'path' => storage_path('logs/events.log'),
            'level' => 'debug',
            'days' => 4,
        ],

        'webhook' => [
            'driver' => 'daily',
            'path' => storage_path('logs/webhooks.log'),
            'level' => 'debug',
            'days' => 4,
        ],

        'mapping' => [
            'driver' => 'daily',
            'path' => storage_path('logs/mapping.log'),
            'level' => 'debug',
            'days' => 4,
        ],

        'shopify' => [
            'driver' => 'daily',
            'path' => storage_path('logs/shopify.log'),
            'level' => 'debug',
            'days' => 4,
        ],


        'images' => [
            'driver' => 'daily',
            'path' => storage_path('logs/s3_images.log'),
            'level' => 'debug',
            'days' => 3,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('APP_ENV') === 'local' || env('APP_ENV') === 'staging' ? env('LOG_SLACK_WEBHOOK_URL_STAGING') : env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Apimio Bot',
            'emoji' => ':tada:',
            'level' => 'info',
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => 'debug',
            'handler' => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],
    ],

];
