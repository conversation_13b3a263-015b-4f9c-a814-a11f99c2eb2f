id: 65357
name: test
environments:
  production:
    memory: 1024
    cli-memory: 512
    scheduler: false
    runtime: 'php-8.2:al2'
    database: apimio
#    queues:
#      - 'default'
#      - 'notifications'
    build:
      - 'composer install --no-dev'
      - 'php artisan event:cache'
      # - 'npm ci && npm run build && rm -rf node_modules'
    deploy:
      - 'php artisan migrate:fresh --seed --force'


#id: 62420
#name: apimio
#environments:
#  production:
#    memory: 1024 # MB of memory to allocate to the environment
#    queue-timeout: 500 # The maximum time in seconds a job may run
#    queue-memory: 1024 # MB of memory to allocate to the queue worker
#    scheduler: false
#    cli-memory: 512 # MB of memory to allocate to the CLI
#    database: apimio
#    runtime: 'php-8.2:al2'
#    build:
#      - 'composer install --optimize-autoloader --no-dev'
##      - 'npm install'
##      - 'npm run build'
#    deploy:
#      - 'php artisan migrate:fresh --seed --force'
