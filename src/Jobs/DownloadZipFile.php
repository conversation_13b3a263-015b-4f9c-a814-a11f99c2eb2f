<?php

namespace Apimio\Gallery\Jobs;


use App\Notifications\ApimioNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use ZipArchive;
use Illuminate\Support\Str;

class DownloadZipFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $email, $user, $folder;
    protected $s3FileUrls;

    public function __construct($s3FileUrls, $user, $folder, $email)
    {
        $this->s3FileUrls = $s3FileUrls;
        $this->user = $user;
        $this->folder = $folder;
        $this->email = $email;
    }

    public function handle()
    {
        info('queue start');
        $zip = new ZipArchive();
        $zipName = Str::random(16) . ".zip";
        if ($zip->open(public_path($zipName), ZipArchive::CREATE) === TRUE) {
            foreach ($this->s3FileUrls as $url) {
                $contents = Storage::disk('s3')->get('images/' . $url[1]);
                $zip->addFromString(('images/' . $url[1]), $contents);
            }
            $zip->close();
        }
        Storage::disk('s3')->put('downloads/' . $zipName, File::get($zipName));
        //get image url of s3 bucket
        $s3url = Storage::disk('s3')->url('downloads/' . $zipName);
        $url = route("file.downloadAll", ['url' => $s3url, 'name' => $this->folder->name]);
        $details = [
            'subject' => 'Your Zip file is ready to download',
            'greeting' => 'Hi ' . $this->user->fname,
            'body' =>
                "Your Zip folder with images is ready for download<br>"
                . "<br>Please click the Download button to start: <br>"
                . "<br> <a href='$url' type='button' style='text-decoration: none; padding: 0.8em; font-size: 0.8rem; border-radius: 0.35rem; font-weight: 700;
                                margin-left: 15em;  min-width: 6.438em; color:white; background-color: #2C4BFF'>Download</a>"
            ,
            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
            'user_id' => $this->user->id,
            'organization_id' => $this->folder->organization_id,
        ];

        //un-comment if you want to stop notification in email
        //$notifier->only_db_notify(true);
        $notifier = new ApimioNotification($details);
        $notifier->only_mail_notify(true);
        $filePath = public_path($zipName);
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        Notification::route('mail', $this->email)
            ->notify($notifier);
        return true;

    }
}
