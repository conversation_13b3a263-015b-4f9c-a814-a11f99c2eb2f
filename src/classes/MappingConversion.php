<?php

namespace Apimio\MappingConnectorPackage\classes;

use Apimio\MappingConnectorPackage\classes\formulas\ApplyTemplateOnItem;
use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use Apimio\MappingConnectorPackage\models\Template;

use App\Models\Product\Attribute;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;


class MappingConversion
{
    public $data;

    public function __construct()
    {
    }

    public function set_data($data)
    {
        $this->data = $data;
        return $this;
    }


    public function validation_for_mapping()
    {
        return Validator::make($this->data, [
            "nodes" => 'required',
        ]);
    }


    /**
     * method for fetching all the product variants
     *
     * @param string $variant_column_name which differentiate the selected column
     * @param string $variant_value product variants value use to distinguish
     * @param Collection $products pass the product collection from which we want to find their variants
     *
     *
     * @return  Collection|null return a collection if data exist or return null if param data is empty
     */
    function find_product_variants(string $variant_column_name, string $variant_value, &$products)
    {

        if (isset($variant_column_name) && isset($variant_value) && !empty($products)) {
            $variants_array = array_filter(
                $products,
                function ($collection_keys) use ($variant_value, &$products, $variant_column_name) {

                    if (isset($products[$collection_keys][$variant_column_name])) {
                        if ((string)$products[$collection_keys][$variant_column_name] === (string)$variant_value) {
                            $variant = '';
                            $variant = $products[$collection_keys];
                            unset($products[$collection_keys]);
                            return $variant;
                        }
                    }
                },
                ARRAY_FILTER_USE_KEY
            );
        } else {
            $variants_array = null;
        }

        return array_values($variants_array);
    }


    /**
     * generate variant options according to export type (shopify, magento, apimio etc)
     *
     * @param $variant_options_json
     * @param $export_type
     * @param $variants_count
     * @return array
     */
    public function make_variant_options($variant = [], $export_type = null, $variants_count = null)
    {

        $apimio_variants = $variant['attr_options_json'] ?? null;
        $default_csv_row = [];
        if (isset($export_type)) {
            $option_count = count($apimio_variants['options']);
            $option_counter = 0;

            for ($i = 0; $i < $option_count; $i++) {
                $flag_variants_option = true;
                if (isset($apimio_variants['attributes'][$i])) {
                    $attribute_obj = Attribute::find($apimio_variants['attributes'][$i]['id']);
                    $apimio_variants['attributes'][$i]['name'] = $attribute_obj?->name;
                }
                if (isset($apimio_variants['attributes'][$i]['name']) && isset($apimio_variants['options'][$i])) {

                    // shopify mapping
                    if ($export_type == 'shopify' && $i <= 2) {
                        $option_counter = $option_counter + 1;
                        if ($variants_count < 1) {
                            $default_csv_row['Option' . $option_counter . ' Name'] = $apimio_variants['attributes'][$i]['name'];
                        }
                        $default_csv_row['Option' . $option_counter . ' Value'] = $apimio_variants['options'][$i];
                    } // magento mapping
                    elseif ($export_type == 'magento') {
                        $additional_attributes[] = $apimio_variants['attributes'][$i]['name'] . "=" . $apimio_variants['options'][$i];

                        $options_name_label[] = $apimio_variants['attributes'][$i]['name'] . "=" . $apimio_variants['attributes'][$i]['name'];
                    } //others mapping
                    else {
                        $default_csv_row[$apimio_variants['attributes'][$i]['name']] = $apimio_variants['options'][$i];
                    }
                }
            }
            if (isset($additional_attributes)) {
                $default_csv_row['additional_attributes'] = implode(",", array_values($additional_attributes));
            }

            //  fetch children of magento configurable parent product
            if ($export_type == 'magento') {
                if (isset($variant['sku']) && isset($default_csv_row)) {
                    $default_csv_row['configurable_variations'] = "sku = " . $variant['sku'] . ',' . $default_csv_row['additional_attributes'];
                    if (!empty($options_name_label)) {
                        $default_csv_row['configurable_variation_labels'] = $options_name_label;
                    }
                }
            }
        }
        return $default_csv_row;
    }


    function column_mapped_for_handle($array, $type = 'csv')
    {
        $sku_key_array = null;
        $mapping_conversion_obj = new MappingConversion();
        foreach ($array as $key => $input_fields) {
            if (is_iterable($input_fields)) {

                // then check TO columns
                if ($type == 'csv'){
                    if (isset($input_fields['to']) && is_iterable($input_fields['to'])) {
                        foreach ($input_fields['to'] as $val) {
                            if (isset($val) && $val == "Default,handle") {
                                $sku_key_array = $key;
                            }
                        }
                    }
                }
                // then check FROM columns
                else{
                    if (isset($input_fields['from']) && is_iterable($input_fields['from'])) {
                        foreach ($input_fields['from'] as $val) {
                            if (isset($val) && $val == "Default,handle") {
                                $sku_key_array = $key;
                            }
                        }
                    }
                }
            }
        }

        if ($sku_key_array === null) {
            return null;
        }

        if ($type == 'csv'){
            return $mapping_conversion_obj->get_explode_attribute_name($array[$sku_key_array]['from'][0]);
        }
        else{
            return $mapping_conversion_obj->get_explode_attribute_name($array[$sku_key_array]['to'][0]);
        }

    }

    function merge_keys_in_order($final_csv_heading = [], $temp_array = []){
        // Initialize a new array with the values of the first array
        $newArray = $final_csv_heading;

        // Iterate through the keys from the second array
        foreach ($temp_array as $key) {
            if (!in_array($key, $final_csv_heading)) {
                // Determine a suitable insertion point based on a heuristic of key similarity
                $baseKeyFound = false;
                foreach ($final_csv_heading as $existingKey) {
                    // Check if the existing key is a base of the new key
                    if (str_starts_with($key, $existingKey) && strlen($key) > strlen($existingKey)) {
                        $newArray = $this->array_insert_after($newArray, $existingKey, [$key]);
                        $baseKeyFound = true;
                        break;
                    }
                }
                // If no related base key, add to the end
                if (!$baseKeyFound) {
                    $newArray[] = $key;
                }
            }
        }

        return $newArray;
    }

    // Helper function to insert elements into an array after a specific key
    function array_insert_after($array, $key, $new)
    {
        $index = array_search($key, $array);
        $pos = false === $index ? count($array) : $index + 1;
        return array_merge(array_slice($array, 0, $pos), $new, array_slice($array, $pos));
    }


    /**
     * @param Collection $collection
     * @param callable $error_callback
     * @param callable $success_callback
     * @return mixed
     */
    public function convert_data_v2(Collection $collection, $error_callback, $success_callback)
    {

        $validation = $this->validation_for_mapping();
        if ($validation->fails()) {
            $error_callback($validation->errors());
        }

        $apply_formula = new ApplyTemplateOnItem();
        $import_export_obj = new ImportExport();
        $final_conversion = array();
        $final_csv_heading = array();
        $attributes_with_values = array();
        $group_data = collect();
        

//        $this->data['export_type'] = 'other';
        if (isset($collection['nodes'])) {
            foreach ($collection['nodes'] as $product) {
                $product_with_units = $product;
//                    if (isset($product_with_units['variants'])) {
//                        unset($product_with_units['variants']);
//                    }
//                    if (isset($product_with_units['hidden'])) {
//                        unset($product_with_units['hidden']);
//                    }

//                    dump($product);


                $product_conversion['parent'] = array();

                //conversion for parent data
                if (isset($product['parent'])) {

                    // assign handle value if is empty or not set
                    $defaultParent = $product['parent']->firstWhere('family_name', 'Default');
                    $handle = isset($defaultParent['attributes']['handle']) ? ($defaultParent['attributes']['handle'] ?? null) : null;

                    // fetch handle mapped column name in csv
                    $apimio_handle_column_name = isset($this->data['nodes']['data']) ? $this->column_mapped_for_handle($this->data['nodes']['data'] , 'export') : null;
                    if (!$apimio_handle_column_name){
                        $apimio_handle_column_name = "handle";
                    }

                    foreach ($product['parent'] as $family) {
                        try {
                            if (!empty($family)) {
                                if (isset($this->data['template_method_type']) && $this->data['template_method_type'] === "export") {
                                    $temp_array = $apply_formula->ConvertItemWithFormula($family, $this->data['nodes']['data'], false, $product_with_units ?? collect(),$this->data['template_method_type']);
                                    $product_conversion['parent'] = array_merge(
                                        $temp_array,
                                        array_filter($product_conversion['parent'])
                                    );
                                } else {
                                    $product_conversion['parent'] = array_merge_recursive(
                                        $product_conversion['parent'],
                                        $apply_formula->ConvertItemWithFormula($family, $this->data['nodes']['data'], false, $product_with_units ?? collect(),$this->data['template_method_type'])
                                    );
                                }
                            } else {
                                Log::channel('mapping')->error("No product found and continue foreach loop");
                                continue;
                            }
                        } catch (\Exception $e) {
                            Log::channel('mapping')->error($e->getMessage());
                        }
                    }


                    if (!isset($product_conversion['parent'][$apimio_handle_column_name])) {
                        $product_conversion['parent'][$apimio_handle_column_name] = $handle;
                    }




                    // fetch default attributes for export
                    if (isset($this->data['template_method_type']) && $this->data['template_method_type'] === "export") {
                        $default_csv_row = $import_export_obj->fetch_template_default_values($this->data['export_type'] ?? '');

                        //default attributes for shopify
                        if ($this->data['export_type'] == 'shopify') {
                            $default_csv_row = array_merge($default_csv_row, $import_export_obj->fetch_template_default_values($this->data['export_type'], TRUE));
                        } elseif ($this->data['export_type'] == 'magento') {
                            $default_csv_row['product_online'] = $this->data['request_data']['product_status'] ?? 0;
                        }
                        // merging default heading attribute for export csv according to export type
                        $product_conversion['parent'] = array_merge($product_conversion['parent'], $default_csv_row);

                        // fetching heading for export csv
//                        $final_csv_heading = array_merge($final_csv_heading, array_keys($product_conversion['parent']));

                        $final_csv_heading = $this->merge_keys_in_order($final_csv_heading, array_keys($product_conversion['parent']));
                    }


                    // conversion for variants data
                    $variants_array = array();
                    if (isset($product['variants'])) {
                        foreach ($product['variants'] as $variants) {
                            $temp_variant = collect([
                                'family_name' => 'Variant',
                                'attributes' => $variants
                            ]);
                            $custom_variant = $apply_formula->ConvertItemWithFormula($temp_variant, $this->data['nodes']['data'], true, $product_with_units ?? collect(), $this->data['template_method_type']);

                            if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "shopify") {
                                $custom_variant['store_connect_id'] = $variants['store_connect_id'] ?? null;
                                $custom_variant['store_connect_image_id'] = $variants['store_connect_image_id'] ?? null;
                                $custom_variant['variant_id'] = $variants['variant_id'] ?? null;
                            }
                            // for export make variant options
                            if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export") {
                                $variant_default_csv_row = $this->make_variant_options($variants ?? null, $this->data['export_type'], count($variants_array));
                                $custom_variant = array_merge($custom_variant, $variant_default_csv_row);
                                $custom_variant = $import_export_obj->variant_filter_attributes(
                                    $custom_variant,
                                    $product_conversion['parent'],
                                    $this->data['export_type'],
                                    count($variants_array)
                                );

                                if (!isset($custom_variant[$apimio_handle_column_name])) {
                                    $custom_variant[$apimio_handle_column_name] = $handle;
                                }

                                // fetching heading for export csv
//                                $final_csv_heading = array_merge($final_csv_heading, array_keys($custom_variant));

                                $final_csv_heading = $this->merge_keys_in_order($final_csv_heading, array_keys($custom_variant));
                            }


                            $variants_array[] = $custom_variant;
                        }
                        $product_conversion['variants'] = array_filter($variants_array);
                        unset($variants_array);
                    }

                    // saving hidden-data
                    if (isset($product['hidden'])) {
                        $product_conversion['hidden'] = $product['hidden'];
                    }
                }


                if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export") {
                    if (isset($this->data['export_type'])) {

                        // merge parent data with first variant
                        if (!empty($product_conversion['variants']) && isset($product_conversion['parent'])) {
                            $firstVariant = &$product_conversion['variants'][0];
                            foreach ($product_conversion['parent'] as $key => $value) {
                                if (array_key_exists($key, $firstVariant) && $value !== null) {
                                    $firstVariant[$key] = $value;
                                }
                                 else if (!array_key_exists($key, $firstVariant)) {
                                     $firstVariant[$key] = $value;
                                 }
                            }
                            $product_conversion['variants'][0] = $firstVariant;
                            $final_conversion = $product_conversion['variants'];
                        }


                        //shopify export
                        if ($this->data['export_type'] == 'shopify') {

                            $media_files = [
                                'handle' => null,
                                'image_src' => []
                            ];

                            if (isset($product_conversion['parent']['Image Src']) && $product_conversion['parent']['Image Src'] != null) {
                                $media_files['handle'] = $product_conversion['parent']['Handle'] ?? null;
                                $media_files['image_src'] = explode(',', $product_conversion['parent']['Image Src']);
                            }

                            if (empty($product_conversion['variants']) && !empty($product_conversion['parent'])) {
                                $product_conversion['parent']['Option1 Name'] = 'Title';
                                $product_conversion['parent']['Option1 Value'] = 'Default Title';
                                $product_conversion['parent']['Variant Image'] = !empty($media_files['image_src']) ? $media_files['image_src'][0] : null;
                                $final_conversion[] = $product_conversion['parent'];
                            }

                            //media files assignment
                            if (!empty($media_files['image_src'])) {
                                foreach ($media_files['image_src'] as $image_position => $image_src) {
                                    if (isset($final_conversion[$image_position])) {
                                        $final_conversion[$image_position]['Image Src'] = $image_src;
                                        $final_conversion[$image_position]['Image Position'] = strval($image_position + 1);
                                    } else {
                                        $temp_image_row = [
                                            'Handle' => $media_files['handle'],
                                            'Image Src' => $image_src,
                                            'Image Position' => strval($image_position + 1),
                                        ];
                                        $final_conversion[] = $temp_image_row;
                                    }
                                }
                            }
                        } // magento export
                        else if ($this->data['export_type'] == 'magento') {
                            if (isset($product_conversion['parent']['configurable_variations'])) {
                                $product_conversion['parent']['configurable_variations'] = implode(' | ', $product_conversion['parent']['configurable_variations']);
                            }
                            if (isset($product_conversion['parent']['configurable_variation_labels'])) {
                                $product_conversion['parent']['configurable_variation_labels'] = implode(' , ', $product_conversion['parent']['configurable_variation_labels']);
                            }

                            $final_conversion = $product_conversion['parent'] ?? [];
                            if (isset($product_conversion['variants'])) {
                                $final_conversion = array_merge($final_conversion, $product_conversion['variants']);
                            }
                        }
                    }
                } else {
                    $final_conversion[] = $product_conversion;
                }
                // Create a collection for $group_data
                $group_data = collect([
                    'nodes' => collect($final_conversion),
                    'final_csv_heading' => $final_csv_heading,
                    'incorrect_csv_product_rows' => [],
                ]);
                unset($product_conversion);
            }


        } else {
            // below code is for import csv mapping to apimio
            if (count($collection) > 0) {
                $all_nodes = $this->data['nodes'];
                $import_flow = 'group';
                $import_action = $this->data['request_data']['import_action'] ?? 1;

                // fetch handle mapped column name in csv
                $csv_handle_column_name = isset($all_nodes['data']) ? $this->column_mapped_for_handle($all_nodes['data']) : null;

                // Clean column name (trim spaces)
                $csv_handle_column_name = trim($csv_handle_column_name);

                if (!$csv_handle_column_name && $import_action == "3") {
                    $validation->errors()->add('Handle', 'No handle column found in csv.');
                    $error_callback($validation->errors());
                }

                if (in_array($import_action , ["1","3"])){
                    if ($csv_handle_column_name){
                        // Separate rows into two collections: nullRows and filterRows
                        list($filterRows, $nullRows) = $collection->partition(function ($item) use ($csv_handle_column_name) {
                            if ($item[$csv_handle_column_name] == null) {
                                $item->prepend('Handle field column name ( ' . $csv_handle_column_name . ' ) value is empty.!', 'ERRORS/WARNINGS');
                                return false;
                            }
                            return true;
                        });

                        // Group the data by the selected Handle value
//                        $groupedData = $filterRows->groupBy($csv_handle_column_name);
                        $groupedData = $filterRows->groupBy(function ($item) use ($csv_handle_column_name) {
                            // Ensure grouping key is trimmed and not empty
                            return trim($item[$csv_handle_column_name]) ?: 'NO_GROUP';
                        });



                        if ($groupedData->keys()->contains('NO_GROUP') && $groupedData->count() == 1) {
                            // If invalid groups were found (like empty keys), return the original collection
                            $groupedData = $filterRows;
                        }


                    }else if ($import_action == "1"){
                        $import_flow = 'single';
                        $groupedData = $collection;
                    }
                }
                else {

                    $import_flow = 'single';
                    $groupedData = $collection;
                }

                // Create a collection for $group_data
                $group_data = collect([
                    'nodes' => collect([
                        'products' => $groupedData
                    ]),
                    'incorrect_csv_product_rows' => $nullRows ?? [],
                    'import_flow' => $import_flow
                ]);

            } else {
                $validation->errors()->add('file', 'No product found');
                return $validation;
            }
        }


        if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export") {

            if (isset($this->data['export_type'])) {
                if ($this->data['export_type'] == 'shopify') {
                    $custom_heading = $this->fetch_custom_heading($this->data['nodes']['data']);
                    $final_csv_heading = $import_export_obj->get_shopify_product_attributes_name($custom_heading);
                } else if ($this->data['export_type'] == 'magento') {
                    $final_csv_heading = $import_export_obj->get_magento_product_attributes_name();
                }
            }

            $group_data['final_csv_heading'] = $final_csv_heading;
        }

        return $success_callback($group_data);
    }


    /**
     * Get the custom heading name from mapped nodes
     * @param $array
     * @return array
     */
    function fetch_custom_heading($array): array
    {
        $custom_heading = [];

        foreach ($array as $item) {
            foreach ($item["from"] as $from) {
                if (str_contains($from, ' -> ')) {
                    // If the 'from' contains '->', add the corresponding 'to' value to the array
                    $custom_heading = array_merge($custom_heading, $item["to"]);
                }
            }
        }
        return $custom_heading;
    }


    /**
     * @param Collection $collection
     * @param callable $error_callback
     * @param callable $success_callback
     * @return mixed
     */
    public function convert_data(Collection $collection, $error_callback, $success_callback)
    {
        $apply_formula = new ApplyTemplateOnItem();
        $import_export_obj = new ImportExport();
        $final_conversion = array();
        $final_csv_heading = array();
        $attributes_with_values = array();

        $validation = $this->validation_for_mapping();
        if ($validation->fails()) {
            $error_callback($validation->errors());
        }
        if (isset($collection['nodes'])) {
            foreach ($collection['nodes'] as $product) {

                $product_with_units = $product;
                if (isset($product_with_units['variants'])) {
                    unset($product_with_units['variants']);
                }
                if (isset($product_with_units['hidden'])) {
                    unset($product_with_units['hidden']);
                }


                $product_conversion['parent'] = array();

                //conversion for parent data
                if (isset($product['parent'])) {
                    foreach ($product['parent'] as $family) {
                        try {

                            if (!empty($family)) {
                                if (isset($this->data['template_method_type']) && $this->data['template_method_type'] === "export") {
                                    $temp_array = $apply_formula->ConvertItemWithFormula($family, $this->data['nodes']['data'], false, $product_with_units);

                                    $product_conversion['parent'] = array_merge(
                                        $temp_array,
                                        array_filter($product_conversion['parent'])
                                    );
                                } else {
                                    $product_conversion['parent'] = array_merge_recursive(
                                        $product_conversion['parent'],
                                        $apply_formula->ConvertItemWithFormula($family, $this->data['nodes']['data'], false, $this->data['template_method_type'] != 'shopify' ? $product_with_units : [])
                                    );
                                }
                            } else {
                                Log::channel('mapping')->error("No product found and continue foreach loop");
                                continue;
                            }
                        } catch (\Exception $e) {
                            Log::channel('mapping')->error($e->getMessage());
                        }
                    }


                    // fetch default attributes for export
                    if (isset($this->data['template_method_type']) && $this->data['template_method_type'] === "export") {
                        $default_csv_row = $import_export_obj->fetch_template_default_values($this->data['export_type']);

                        //default attributes for shopify
                        if ($this->data['export_type'] == 'shopify') {
                            $default_csv_row = array_merge($default_csv_row, $import_export_obj->fetch_template_default_values($this->data['export_type'], TRUE));
                        } elseif ($this->data['export_type'] == 'magento') {
                            $default_csv_row['product_online'] = $this->data['request_data']['product_status'] ?? 0;
                        }
                        // merging default heading attribute for export csv according to export type
                        $product_conversion['parent'] = array_merge($product_conversion['parent'], $default_csv_row);

                        // fetching heading for export csv
                        $final_csv_heading = array_merge($final_csv_heading, array_keys($product_conversion['parent']));
                    }


                    // conversion for variants data
                    $variants_array = array();
                    if (isset($product['variants'])) {
                        foreach ($product['variants'] as $variants) {

                            $custom_variant = $apply_formula->ConvertItemWithFormula($variants, $this->data['nodes']['data'], true, [], $this->data['template_method_type']);

                            // for export make variant options
                            if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export") {
                                $variant_default_csv_row = $this->make_variant_options($variants ?? null, $this->data['export_type'], count($variants_array));
                                $custom_variant = array_merge($custom_variant, $variant_default_csv_row);
                                $custom_variant = $import_export_obj->variant_filter_attributes(
                                    $custom_variant,
                                    $product_conversion['parent'],
                                    $this->data['export_type'],
                                    count($variants_array)
                                );

                                // fetching heading for export csv
                                $final_csv_heading = array_merge($final_csv_heading, array_keys($custom_variant));
                            }

                            $variants_array[] = $custom_variant;
                        }
                        $product_conversion['variants'] = array_filter($variants_array);
                        unset($variants_array);
                    }

                    // saving hidden-data
                    if (isset($product['hidden'])) {
                        $product_conversion['hidden'] = $product['hidden'];
                    }
                }


                // if you want to de-attach families with attributes
                //                if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export"){
                //                    $without_family_variants = array();
                //                    foreach ($product_conversion as $type_key => $product){
                //
                //                        // all families except hidden family data
                //                        if ($type_key != "hidden"){
                //                            if (is_array($product)){
                //                                foreach ($product as $family_key => $family_attributes){
                //                                    $without_family_variants = array_merge($without_family_variants,$family_attributes);
                //                                }
                //                            }
                //                        }
                //
                //                    }
                //                    $product_conversion = $without_family_variants;
                //                }


                if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export") {
                    if (isset($this->data['export_type'])) {

                        //shopify export
                        if ($this->data['export_type'] == 'shopify') {

                            $media_files = [
                                'handle' => null,
                                'image_src' => []
                            ];

                            if (isset($product_conversion['parent']['Variant Image']) && $product_conversion['parent']['Variant Image'] != null) {
                                $media_files['handle'] = $product_conversion['parent']['Handle'] ?? null;
                                $media_files['image_src'] = explode(',', $product_conversion['parent']['Variant Image']);
                            }
                            if (!empty($product_conversion['variants'])) {
                                $final_conversion = $product_conversion['variants'];
                                $final_conversion[0] = array_merge($product_conversion['parent'] ?? [], $final_conversion[0]);
                            } else if (!empty($product_conversion['parent'])) {
                                $product_conversion['parent']['Option1 Name'] = 'Title';
                                $product_conversion['parent']['Option1 Value'] = 'Default Title';
                                $product_conversion['parent']['Variant Image'] = !empty($media_files['image_src']) ? $media_files['image_src'][0] : null;
                                $final_conversion[] = $product_conversion['parent'];
                            }

                            //media files assignment
                            if (!empty($media_files['image_src'])) {
                                foreach ($media_files['image_src'] as $image_position => $image_src) {
                                    if (isset($final_conversion[$image_position])) {
                                        $final_conversion[$image_position]['Image Src'] = $image_src;
                                        $final_conversion[$image_position]['Image Position'] = strval($image_position + 1);
                                    } else {
                                        $temp_image_row = [
                                            'Handle' => $media_files['handle'],
                                            'Image Src' => $image_src,
                                            'Image Position' => strval($image_position + 1),
                                        ];
                                        $final_conversion[] = $temp_image_row;
                                    }
                                }
                            }
                        } // magento export
                        else if ($this->data['export_type'] == 'magento') {
                            if (isset($product_conversion['parent']['configurable_variations'])) {
                                $product_conversion['parent']['configurable_variations'] = implode(' | ', $product_conversion['parent']['configurable_variations']);
                            }
                            if (isset($product_conversion['parent']['configurable_variation_labels'])) {
                                $product_conversion['parent']['configurable_variation_labels'] = implode(' , ', $product_conversion['parent']['configurable_variation_labels']);
                            }

                            $final_conversion[] = $product_conversion['parent'] ?? [];
                            if (isset($product_conversion['variants'])) {
                                $final_conversion = array_merge($final_conversion, $product_conversion['variants']);
                            }
                        } else {
                            $final_conversion[] = $product_conversion['parent'] ?? [];
                            if (isset($product_conversion['variants'])) {
                                $final_conversion = array_merge($final_conversion, $product_conversion['variants']);
                            }
                        }
                    }
                } else {
                    $final_conversion[] = $product_conversion;
                }

                unset($product_conversion);
            }
        } else {
            // below code is for import csv mapping to apimio
            if (count($collection) > 0) {

                $all_nodes = $this->data['nodes'];

                // fetch handle mapped column name in csv
                $csv_handle_column_name = isset($all_nodes['data']) ? $this->column_mapped_for_handle($all_nodes['data']) : null;

                if (!$csv_handle_column_name) {
                    $validation->errors()->add('Handle', 'No handle column found in csv.');
                    $error_callback($validation->errors());
                }

                // Separate rows into two collections: nullRows and filterRows
                list($nullRows, $filterRows) = $collection->partition(function ($item) use ($csv_handle_column_name) {
                    return $item[$csv_handle_column_name] === null;
                });

                // Group the data by the selected Handle value
                $groupedData = $filterRows->groupBy($csv_handle_column_name);

                $incorrect_csv_product_rows = array();
                $sku_key_for_incorrect_rows = null;

                //fetch sku mapped key if exist in import csv.
                // if (!isset($sku_key_for_incorrect_rows) && $sku_key_for_incorrect_rows == null) {
                //     foreach (array_column($this->data['nodes']['data'], 'to') as $keys_column => $keys_value) {
                //         foreach ($keys_value as $sub_value) {
                //             $checker = $this->get_explode_attribute_name($sub_value);
                //             if ($checker == 'sku') {

                //                 //below line is use to rearrange the keys of mapping rows data
                //                 $this->data['nodes']['data'] = array_values($this->data['nodes']['data']);

                //                 if (isset($this->data['nodes']['data'][$keys_column]['from'][0])) {
                //                     $sku_key_for_incorrect_rows = $this->get_explode_attribute_name($this->data['nodes']['data'][$keys_column]['from'][0]);
                //                     break 2;
                //                 }
                //             }
                //         }
                //     }
                // }


                // get distinct value of import csv variants
                // if (!empty($collection) && empty($attributes_with_values) && isset($this->data['nodes']['variant']['variant_options'])) {

                //     $attributes_with_values = $this->get_distinct_import_attributes_with_value($collection);
                // }


                // fetch mapping converted value for import csv
                $sku_unique_checker = [];
                while (count($collection) > 0) {
                    $product = reset($collection);
                    $product_conversion = array();

                    // Finding variants from import csv products
                    unset($product_with_variants);
                    $product_with_variants = array();
                    if (isset($this->data['nodes']['variant']["variant_options"])) {
                        $variant_options = array_filter($this->data['nodes']['variant']["variant_options"]);
                    } else {
                        $variant_options = array();
                    }

                    if (isset($this->data['nodes']['variant']["id"]) && !empty($variant_options)) {
                        $variant_id = explode(',', $this->data['nodes']['variant']["id"]);
                        if (isset($variant_id[1])) {
                            $variant_id = $variant_id[1];
                        } else {
                            $variant_id = $this->data['nodes']['variant']["id"];
                        }
                        $product_with_variants = $this->find_product_variants($variant_id, $product[$variant_id], $collection);
                    } else {
                        $product_with_variants[] = $product;
                        array_shift($collection);
                    }

                    foreach ($product_with_variants as $pro_v_key => $product_variant) {

                        if (!empty($product_variant)) {

                            //fetch those products who's sku is empty or not mapped.
                            $product_variant_keys = is_array($product_variant) ? array_keys($product_variant) : [];

                            if ($sku_key_for_incorrect_rows == null) {
                                $incorrect_csv_product_rows[] = array_merge([
                                    'ERRORS/WARNINGS' => "SKU field does not selected in mapped data.!"
                                ], $product_variant);
                                continue;
                            } else if (in_array($sku_key_for_incorrect_rows, $product_variant_keys)) {
                                if (empty($product_variant[$sku_key_for_incorrect_rows])) {
                                    $incorrect_csv_product_rows[] = array_merge([
                                        'ERRORS/WARNINGS' => "SKU field value is empty.!"
                                    ], $product_variant);
                                    continue;
                                } else {
                                    if (in_array($product_variant[$sku_key_for_incorrect_rows], $sku_unique_checker)) {
                                        $incorrect_csv_product_rows[] = array_merge([
                                            'ERRORS/WARNINGS' => "Duplicate SKU field value found.!"
                                        ], $product_variant);
                                        continue;
                                    } else {
                                        $sku_unique_checker[] = $product_variant[$sku_key_for_incorrect_rows];
                                    }
                                }
                            }

                            $without_family_variants = array();
                            $converted_variants = $apply_formula->ConvertItemWithFormula(['attributes' => $product_variant], $this->data['nodes']['data']);

                            if (!isset($product_conversion['parent'])) {
                                $product_conversion['parent'] = $converted_variants;
                            } else {
                                foreach ($converted_variants as $converted_variant) {
                                    $without_family_variants = array_merge($without_family_variants, $converted_variant);
                                }
                                foreach ($variant_options as $var_opt_key => $variant_option) {
                                    $without_family_option = explode(',', $variant_option);
                                    if (!isset($without_family_option[1])) {
                                        $without_family_option[1] = $without_family_option[0];
                                    }

                                    $var_opt_key++;
                                    $without_family_variants['option_name_' . $var_opt_key] = $without_family_option[1];
                                    $without_family_variants['option_value_' . $var_opt_key] = $product_variant[$without_family_option[1]];
                                }
                                $product_conversion['variants'][] = $without_family_variants;
                            }
                        } else {
                            Log::channel('mapping')->error("No product found and continue foreach loop");
                            continue;
                        }
                    }

                    if (!empty($product_conversion)) {
                        $final_conversion['products'][] = $product_conversion;
                    }
                }

                if (!empty($attributes_with_values)) {
                    $final_conversion['attributes_with_values'] = $attributes_with_values;
                }
            } else {
                $validation->errors()->add('file', 'No product found');
                return $validation;
            }


            $converted_array_with_variant_options['incorrect_csv_product_rows'] = $incorrect_csv_product_rows;
        } // import else end bracket


        $converted_array_with_variant_options['nodes'] = $final_conversion;

        // assign selected options for import variants
        if (isset($this->data['nodes']['variant']['variant_options'])) {
            $version_selection = $this->data['nodes']['variant'];
            $ver_id = explode(',', $version_selection['id']);
            $converted_array_with_variant_options['variants']['unique_column'][$ver_id[0]] = isset($ver_id[1]) ? $ver_id[1] : null;
            if (isset($version_selection['variant_options'])) {
                foreach ($version_selection['variant_options'] as $var_opt) {
                    if (isset($var_opt)) {
                        $option_selection = explode(',', $var_opt);
                        if (isset($option_selection[1])) {
                            $converted_array_with_variant_options['variants']['options'][$option_selection[0]][] = $option_selection[1];
                        }
                    }
                }
            }
        }
        // this code helps us to delete file of json
        //        if(file_exists(Storage::disk('public')->path($this->data['file_path']))){
        //            unlink(Storage::disk('public')->path($this->data['file_path']));
        //        }

        if (isset($this->data['template_method_type']) && $this->data['template_method_type'] == "export") {

            if (isset($this->data['export_type'])) {
                if ($this->data['export_type'] == 'shopify') {
                    $final_csv_heading = $import_export_obj->get_shopify_product_attributes_name();
                } else if ($this->data['export_type'] == 'magento') {
                    $final_csv_heading = $import_export_obj->get_magento_product_attributes_name();
                }
            }

            $converted_array_with_variant_options['final_csv_heading'] = $final_csv_heading;
        }

        return $success_callback($converted_array_with_variant_options);
    }


    function get_explode_family_name($value)
    {
        $split_array = array();
        if (isset($value)) {
            $split_array = explode(',', $value);
        }
        return $split_array[0] ?? $split_array;
    }

    function get_explode_attribute_name($value)
    {

        $split_array = array();
        if (isset($value)) {
            $split_array = explode(',', $value);
        }

        return $split_array[1] ?? $split_array[0];
    }

    function get_explode_attribute_with_family($value)
    {
        $split_array = array();
        if (isset($value)) {
            $split_array = explode(',', $value);
        }
        return $split_array;
    }


    public function get_distinct_variant_options(array $option, callable $error_callback, callable $success_callback)
    {
        if (is_array($option)) {
            $unique_values = array_unique($option);
            $success_callback(array_filter($unique_values));
        } else {
            $error_callback('Please provide array data');
        }
    }


    function get_distinct_import_attributes_with_value($collection)
    {
        $attributes_with_values = array();

        if (isset($this->data['nodes']['variant']['variant_options'])) {
            $variant_option_count = count($this->data['nodes']['variant']['variant_options']);
        } else {
            return false;
        }


        for ($i = 0; $i < $variant_option_count; $i++) {
            $option_values = array();
            $option_name = null;
            if ($this->data['nodes']['variant']['variant_options'][$i] == null) {
                continue;
            }
            $option_name = $this->get_explode_attribute_name($this->data['nodes']['variant']['variant_options'][$i]);

            if ($option_name == null) {
                $option_name = $this->data['nodes']['variant']['variant_options'][$i];
            }


            $option_values = array_column($collection, $option_name);

            $this->get_distinct_variant_options($option_values, function ($error) {
                Log::channel('mapping')->error($error);
            }, function ($success) use ($i, &$attributes_with_values, $option_name) {
                // if attribute not has null values
                if (!empty($success)) {
                    $attributes_with_values[] = [
                        'name' => $option_name,
                        'options' => array_values($success)
                    ];
                }
            });
        }

        return $attributes_with_values;
    }


    public function temp_mapping_view($data)
    {
        $template = new Template();
        if (isset($data['data_required']['output_type']) && $data['data_required']['output_type'] == 'magento') {
            $data['output_array'] = $template->magento_mapping_array();
        }


        // need to remove when export and shopify flow working fine
        // if (isset($data['input_array_data']['nodes']) && !empty($data['input_array_data']['nodes'])) {
        //     $data['input_array']['array_name'] = $data['input_array_data']['array_name'];
        //     $product = $data['input_array_data']['nodes'][0];

        //     // fetch parent heading keys
        //     if (isset($product) && isset($product['parent'])) {
        //         foreach ($product['parent'] as $parent) {
        //             if (isset($parent['attributes'])) {
        //                 $fetch_keys = $this->getKeysMultidimensional($parent['attributes']);
        //             } else {
        //                 $fetch_keys = array();
        //             }

        //             if (isset($parent['family_name'])) {
        //                 if (!isset($data['input_array']['nodes'][$parent['family_name']])) {
        //                     $data['input_array']['nodes'][$parent['family_name']] = array();
        //                 }
        //                 $data['input_array']['nodes'][$parent['family_name']] = array_merge($data['input_array']['nodes'][$parent['family_name']], $fetch_keys);
        //             } else {
        //                 if (!isset($data['input_array']['Default'])) {
        //                     $data['input_array']['nodes']['Default'] = array();
        //                 }
        //                 $data['input_array']['nodes']['Default'] = array_merge($data['input_array']['Default'], $fetch_keys);
        //             }
        //         }

        //         // fetch variants heading keys
        //         if (isset($data['input_array_data']['nodes'][0]['variants'])) {
        //             $product_variants = $data['input_array_data']['nodes'][0]['variants'];
        //             $fetch_keys = $this->getKeysMultidimensional($product_variants);
        //             if (!isset($data['input_array']['Variants'])) {
        //                 $data['input_array']['nodes']['Variants'] = array();
        //             }
        //             $data['input_array']['nodes']['Variants'] = array_merge($data['input_array']['nodes']['Variants'], $fetch_keys);
        //         }
        //     }

        //     unset($data['input_array_data']);
        // }


        return $data;
    }


    function getKeysMultidimensional(array $array)
    {
        $keys = array();
        foreach ($array as $att_key => $value) {
            if (is_array($value)) {
                foreach ($value as $sub_key => $sub_val) {
                    if (is_array($sub_val)) {
                        $keys = array_merge($keys, array_keys($sub_val));
                    } else {
                        array_push($keys, $sub_key);
                    }
                }
            } else {
                array_push($keys, $att_key);
            }
        }

        return array_unique($keys);
    }
}
