@extends('layouts.app_new')
@push('header_scripts')
@viteReactRefresh
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css">

    <style>
        .line {
            width: 80%;
            top: 24px;
            position: absolute;
            border-bottom: 1px solid #e9e6e6;
        }


        input[type="radio"]:checked + label { /*select all parent divs where radio button is checked*/
            border: 1px solid #2c4bff78;
            background: #f2f2f387;
        }

        .custom-options-template {
            border: 1px solid #E2E2E3;
            border-radius: 8px;
            min-width: 250px;
            max-width: 250px;

        }

        .import_input_template {
            position: absolute;
            top: 25%;
            right: 16px;
        }

        .card-template {
            height: 80px;
            min-height: 80px;
            max-height: 200px;
            width: 226px;
            max-width: 226px;
            min-width: 226px;
        }



        .insert_element {
            padding: 3px 10px !important;
            height: auto;
            font-size: small;
        }

        .insert_element_head {
            padding: 10px 10px !important;
        }

        .insert_element_main {
            border-radius: 5px;

        }

        .insert_element_content {
            padding: 0px 10px;
        }

        .insert_element_title {
            border: 1px solid #E5E5E5;
            background: #F8F8F8;
            font-size: small;
            height: auto;
            padding: 2px;
        }

        .short_code_div {
            border: 1px solid #E5E5E5;
            min-height: 100px;
            max-height: 200px;
            width: 100%;
            overflow-y: auto;
            font-size: smaller;
        }

        .element_tags {
                font-weight: 600;
                background: #e3e3e3;
                color: #000000;
                padding: 3px;
                border-radius: 4px;
                margin: 2px 2px;
                display: inline-block;
                border: 1px solid #929293;
        }

        [data-placeholder]:empty:before {
            content: attr(data-placeholder);
            color: #888;
            font-size: 12px;
        }

        .mapping_nav_sticky {
            padding: 20px;
            z-index: 2;
            position: fixed;
            top: 0;
            right: 0px;
            width: calc(100% - 236px);
            background-color: #fff;
            border-bottom: 1px solid #e9e6e6 !important;
            transition: all .3s ease;
            will-change: top;
        }



        @media (max-width: 992px) {
            .mapping_footer {
                width: calc(100% - 54px);
            }

            .mapping_nav_sticky {
                width: calc(100% - 0px);
            }
        }


        #add_row .align-self-center {
            max-width: fit-content;
        }

        /*for invalid rows selection*/
        #add_row .invalid {
            border: 1px solid #FFCE3D;
            border-radius: 4px;
            background-color: #FFFAF5;
        }

        #add_row .valid {
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        #add_row .invalid .row_success {
            display: none;
        }

        #add_row .invalid .row_warning {
            display: block;
        }

        /*for valid rows selection*/
        #add_row .valid .row_success {
            display: block;
        }

        #add_row .valid .row_warning {
            display: none;
        }


        /*// for remove row with animation*/
        @keyframes minimize {
            0% {
                max-height: 10rem;
                padding-top: 1rem;
                padding-bottom: 1rem;
                border-width: 1px;
                overflow: hidden;
            }
            100% {
                max-height: 0;
                padding-top: 0;
                padding-bottom: 0;
                border-width: 0;
                background: rgba(255, 99, 71, 0.4);
                overflow: hidden;
            }
        }


        .minimize {
            animation-duration: 0.5s;
            animation-iteration: 1;
            animation-fill-mode: backwards;
        }


        .minimize {
            animation-name: minimize;
        }

    </style>

    {{--datatables styling--}}
    <style>
        table.dataTable tbody tr{
            height: 55px;

        }
        table.dataTable tbody tr:hover{
            cursor: pointer;
        }

        .temp-action-btn{
            font-size: 20px;
            padding-left: 10px ;
        }

        .temp-folder-icon{
            font-size: 25px;
            color: #2c4bff;
        }

        .btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled:focus{
            background-color: red !important;
        }

    </style>
@endpush

@push('footer_scripts')
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>

    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
@endpush


