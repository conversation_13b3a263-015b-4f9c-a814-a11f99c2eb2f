@extends('mapping::layouts.app')
@section('titles','Mapping Fields')
@section('content')
    <x-products.page-title name="Mapping Products" description="{{trans('Mapping convertion of your all products')}}" links="false"
                           button="false" buttonname="null"/>
    <form id="mainform" method="POST"
          action="{{ route(isset($data['data_required']['redirect_url_route']) ? $data['data_required']['redirect_url_route'] : null) }}" class="formStyle">
        @csrf
        <input type="hidden" name="file_path" value="{{$data['file_path']}}">
        <input type="hidden" name="organization_id" value="{{$data['data_required']['organization_id']}}">
        <input type="hidden" name="template_method_type" value="{{$data['data_required']['template_method_type']}}">

        <div class="border-radius shadow-none" >
            <ul class="nav nav-pills mb-3 border-bottom" id="myTab" role="tablist">
                <li class="nav-item">
                    <a class="nav-link text-dark active Roboto bold" id="product-tab" data-bs-toggle="tab" href="#product"
                       role="tab" aria-controls="profile" aria-selected="false">Products</a>
                </li>
                @if(isset($data['data_required']['template_method_type']) && $data['data_required']['template_method_type'] == 'import')
                    <li class="nav-item">
                        <a class="nav-link text-dark Roboto bold" id="variant-tab" data-bs-toggle="tab" href="#variant" role="tab"
                           aria-controls="contact" aria-selected="false">{{trans('products_import_step3.variants')}}</a>
                    </li>
                    <li class="ms-auto">
                        <div>
                            <button id="mapping_add_attribute" type="button" class="form-control px-5 btn btn-outline-primary">
                                Create Apimio Attribute
                            </button>
                        </div>
                    </li>
                @endif


            </ul>

            <div class="tab-content" id="myTabContent">

                <!--products tab panel-->
                <div class="tab-pane fade show active" id="product" role="tabpanel" aria-labelledby="profile-tab" style="padding-bottom: 200px">
                    <div id="row_container" class="card-body px-2">
                        <div style="">
                            {{--dynamic rows fetch from ajax call--}}
                            <div id="add_row_empty" style="display: none">
                                <div class="d-flex flex-column ">
                                    <div class="p-2 mt-5 mx-auto">
                                        <img src="{{URL::asset('/mapping-fields/icons/<EMAIL>')}}" class="img-fluid"
                                             width="150px" alt="empty page">
                                    </div>
                                    <div class="p-2 mx-auto">
                                        <p>
                                            You have not added any mapped row yet.
                                        </p>
                                    </div>
                                </div>

                            </div>
                            <div id="add_row" style="display: none"></div>
                            <div id="mapping_loader">
                                <div class="d-flex justify-content-center">
                                    <div class="spinner-border m-5 " role="status">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-center">

                                    <p>Please wait while your mapping in progress <span id="mapping_counter" class="font-weight-bold"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="">
                        <div class="d-flex justify-content-start">
                            <div class="p-2">
                                <button id="add_operation" type="button" class="btn btn-primary-tertiary">
                                    <div class="media">
                                        <img src="{{asset('media/new-flow/add another.png')}}" class="me-1" alt="">
                                        <div class="media-body">
                                            <h5 class="m-0 p-2 Roboto regular">{{trans('products_import_step3.add_operation_btn')}}</h5>
                                        </div>
                                    </div>

                                </button>
                            </div>

                        </div>
                    </div>
                </div>

                <!--variants tab panel-->
                @if(isset($data['data_required']['template_method_type']) && $data['data_required']['template_method_type'] == 'import')
                    <div class="tab-pane fade show" id="variant" role="tabpanel" aria-labelledby="contact-tab">
                        <div>
                            <div class="row">
                                <div class="col-lg-3">
                                    <div class="p-2">
                                        <div class="form-group">
                                            <h4 for="" class="mb-2">Variants Group Identifier</h4>
                                            <select name="nodes[variant][id]"
                                                    class="form-control apimio-column">
                                                <option value="" class="Poppins regular text-color">Select Field</option>
                                                @foreach($data['input_array']['nodes'] as $input_key => $input_vals)
                                                    @if($input_key != 'Default')
                                                        <optgroup label='{{(isset($input_key)) ?  $input_key : 'Others'}}'>
                                                            @endif
                                                            @foreach($input_vals as $input_val)
                                                                @php
                                                                    if(isset($input_key)){
                                                                        $family_input_value = $input_key.','.$input_val;
                                                                    }
                                                                    else{
                                                                       $family_input_value = $input_val;
                                                                    }
                                                                @endphp
                                                                @if(isset($data['template_attributes']['template']['variant']['id']))
                                                                    <option
                                                                        value="{{$family_input_value}}" {{($data['template_attributes']['template']['variant']['id'] == $family_input_value) ? 'selected' : null}} >{{$input_val}}</option>
                                                                @else
                                                                    <option value="{{$family_input_value}}">{{$input_val}}</option>
                                                                @endif
                                                            @endforeach
                                                            @if($input_key != 'Default')
                                                        </optgroup>
                                                    @endif
                                                @endforeach
                                            </select>
                                        </div>
                                        @error('variant')
                                        <div class="text-danger">
                                            {{$message}}
                                        </div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="p-2">
                                    <h4 for="" class="mb-2">Variant Options</h4>
                                    <div class="mb-3">
                                        <div id="var_options" class="row p-0">

                                        </div>

                                    </div>
                                    <div>
                                        <button type="button" id="add-variant-option-btn" onclick="add_variant_option()"
                                                class="btn btn-primary mt-40">
                                            Add New Option
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!--Mapping Popup Modal -->
            <x-component-mapping-popup :templateAttributes="$data['template_attributes']" :dataRequired="$data['data_required']"/>

            <div class="mapping_footer d-flex flex-md-row flex-sm-column justify-content-evenly">
                <div class="">
                    <div class="row">
                        <div class="col-lg-12 ">
                            <div class="d-flex flex-row-reverse mt-4">
                                <div class="p-2" style="width: auto">
                                    <button id="save_template_btn" type="button"
                                            class="form-control btn btn-primary px-3" disabled>
                                        Save template
                                    </button>
                                </div>
                                <div class="p-2">
                                    <button type="button" data-bs-toggle="tooltip" data-bs-placement="top"
                                            title="Default 4 fields will remain, and the rest will be removed automatically."
                                            onclick="clear_rows()" id="cancel-btn"
                                            class="px-5 btn btn-outline-primary" disabled>
                                        Reset

                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
                <div class="">
                    <div class="row">
                        <div class="col-lg-12 ">
                            <div class="d-flex flex-row-reverse mt-4">
                                @if(isset($data['data_required']['template_method_type']) && $data['data_required']['template_method_type'] != 'shopify')
                                    <div class="p-2 d-flex align-items-center" style="width: 159px" data-bs-toggle="tooltip" data-bs-placement="top"
                                         title="If this button is disabled then please make sure all your unmapped fields are mapped or you can mark the side check box.">
                                        <button id="pro_imp_btn" disabled type="button"
                                                class="form-control btn btn-primary ripplelink">
                                            {{trans('products_import_step1.next_btn')}}
                                        </button>
                                    </div>
                                @endif
                                <div class="p-2">
                                    <p id="sku_warning" class="text-danger mb-0" style="display: none"><i class="bi bi-exclamation-triangle"></i> SKU
                                        column not identified</p>
                                    <p class="mb-0 clr-grey">You have <span id="invalid_row_count"></span> unmapped columns
                                    </p>
                                    <p class="mb-0 d-flex align-items-center clr-grey"><input type="checkbox"
                                                                                              name="ignore_unmapped"
                                                                                              id="ignore_unmapped"
                                                                                              class="me-2"> Don’t proceed data
                                        in unmapped columns</p>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </form>

    <!--VLookup Modal -->
    <x-component-vlookup :organization="$data['data_required']['organization_id']"/>


    @if(isset($data['data_required']['template_method_type']) && $data['data_required']['template_method_type'] == 'import')

        <!--Add attribute Modal -->
        <x-component-mapping-add-attribute :data="$data"/>
    @endif


    @push('footer_scripts')

        <!--  short code Plugin   -->
        <script src="{{ asset('mapping-fields/js/jquery.insert-at-cursor.min.js') }}"></script>

        <script>
            $.initCursor('#example');

            function insert_element(e) {
                var element_value = $(e).data('val');
                var short_code_div = $(e).closest('.form-group').find('.short_code_div');
                short_code_div.insertAtCursor(`<span contenteditable='false' class='element_tags'>${element_value}</span>`);
                get_short_code_value(short_code_div);
            }

            function reset_element(e) {

                var short_code_div = $(e).closest('.form-group').find('.short_code_div');
                short_code_div.empty();
                get_short_code_value(short_code_div);

            }


            function get_short_code_value(e) {
                var hidden_field_short_code = $(e).closest('.form-group').find('.short_code_hidden_field');
                var content = $(e).html();
                hidden_field_short_code.val(btoa(content));
                parent_row = $(e).parents('.row .justify-content-around');
                row_obj_checker(parent_row);
            }
        </script>


        <script>

            let global_row_count = 0;

            let row_node = '';

            let row_node_flag = false;

            let template_option = null;

            let input_row_selector = "";

            let auto_selector_on = true;

            var ajax_result = "";

            var total_mapping_rows_count = 0;
            var total_mapped_rows_count = 0;

            var promises = [];


            let data = Object.values(JSON.parse(`{!! json_encode([ 0 => $data]) !!}`))[0];

            console.log(data);

            /* LARAVEL META CSRF REQUIREMENT */
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            function add_variant_option() {
                // Ajax Call
                var options_count = $("#var_options").find(".col-lg-3").length;
                if (options_count >= 2) {
                    $('#add-variant-option-btn').hide();
                }

                $.ajax({
                    url: "{{ route('mapping.add.variant.option') }}",
                    method: "POST",
                    async: false,
                    data: {
                        input_array: JSON.stringify(data.input_array),
                        template_option: (template_option != null) ? template_option : null,

                    },
                    success: function (data) {
                        $('#var_options').append(data.output);
                    },
                    error: function (error) {
                        console.log(error);
                    }
                });
            }


            function delete_row(e) {
                var delete_parent = $(e).parents(':eq(4)');

                delete_parent.addClass(delete_parent.data('animation-class'));
                delete_parent.bind('oanimationend animationend webkitAnimationEnd', function () {
                    delete_parent.remove();
                    invalid_row_count();
                });


            }

            function add_row_empty(e) {
                var mapping_row_count = $("#add_row").find(".row").length;
                if (mapping_row_count <= 0) {
                    $("#add_row_empty").show();
                    $("#save_template_btn").prop('disabled', true);
                } else {
                    $("#add_row_empty").hide();
                    $("#save_template_btn").prop('disabled', false);

                }
            }


            function invalid_row_count() {
                let sku = 'Default,sku';
                let sku_flag = false;
                add_row_empty();
                $(".right_array").filter(function () {
                    if ($(this).val() == sku) {
                        sku_flag = true;
                        $(this).parents('.invalid , .valid').find('.left_array').prop('required',true);
                    }
                    else{
                        $(this).parents('.invalid , .valid').find('.left_array').prop('required',false);
                    }
                });

                let count = $("#add_row .invalid").length;
                $("#invalid_row_count").html(count);

                if ("{{$data['data_required']['template_method_type']}}" === "import") {
                    if (sku_flag) {
                        $("#sku_warning").hide();
                        $('#pro_imp_btn').prop("disabled", false);
                        $("#save_template_btn").prop('disabled', false);
                    } else {
                        $("#sku_warning").show();
                        $('#pro_imp_btn').prop("disabled", true);
                        $("#save_template_btn").prop('disabled', true);
                    }
                } else {
                    sku_flag = true;
                }

                if (count == 0 && sku_flag) {
                    $('#pro_imp_btn').prop("disabled", false);
                } else {
                    if (!$("#ignore_unmapped").is(':checked')) {
                        $('#pro_imp_btn').prop("disabled", true);
                    }

                }

            }


            function clear_rows() {
                input_row_selector = '';
                global_row_count = 0;
                $("#mapping_counter").html("( 0% )");
                total_mapped_rows_count = 0;
                total_mapping_rows_count = 4;
                row_node = '';
                $("#add_row").hide();
                $("#mapping_loader").show();
                $("#add_row").html("");
                for (let i = 0; i < 4; i++) {
                    row_node_flag = true;
                    $("#add_operation").click();
                }
                row_node_flag = false;
                setTimeout(function () {
                    $("#add_row").show();
                    $("#mapping_loader").hide();
                }, 1000);
            }


            $("#ignore_unmapped").change(function () {
                if (this.checked) {
                    $('#pro_imp_btn').removeAttr("disabled");
                    invalid_row_count();
                } else {
                    let count = $("#add_row .invalid").length;
                    if (count != 0) {
                        $('#pro_imp_btn').attr("disabled", true);
                    }
                }
            })


            function delete_opt(e) {
                ($(e).parents(':eq(2)').remove());
                $('#add-variant-option-btn').show();
            }


            $('#add_operation').on('click', function () {
                create_row().then(function (data) {
                    field_row_mapping(data);
                });
            });

            function create_row() {

                // Ajax Call
                let row_count = global_row_count;
                global_row_count++;
                ajax_result = $.ajax({
                    url: "{{ route('add.row') }}",
                    method: "POST",
                    data: {
                        input_array: JSON.stringify(data.input_array),
                        output_array: JSON.stringify(data.output_array),
                        formula: 'assign',
                        row_count: row_count,
                        row_node: (row_node_flag == true) ? row_node : null,
                        input_row_selector: input_row_selector,
                        method_type: data.data_required.template_method_type

                    },
                    success: function (data) {
                        // change mapping count on loader
                        percentage = Math.ceil((++total_mapped_rows_count/(total_mapping_rows_count))*100);
                        $("#mapping_counter").html("( " + percentage + "% )");
                        return data;
                    },
                    error: function (error) {
                        console.log(error);
                    }
                });

                return ajax_result;

            }


            function field_row_mapping(data_output) {
                $.each(data_output.output, function(index, data) {
                    var parent_row = $("<div></div>");
                    parent_row.addClass("row my-2 py-2 justify-content-around");
                    parent_row.attr("data-animation-class", "minimize");
                    parent_row.append(data);
                    parent_row = row_obj_checker(parent_row);
                    $('#add_row').append(parent_row);
                    invalid_row_count();
                });
            }


            function row_obj_checker(parent_row) {
                row_inputs = parent_row.find('select,.left_array');

                checker_flag = true;
                $.each(row_inputs, function (index, item) {
                    if (!$(item).val()) {
                        checker_flag = false;
                    }
                });

                if (!checker_flag) {
                    parent_row.removeClass("valid");
                    parent_row.addClass("invalid");
                } else {
                    parent_row.removeClass("invalid");
                    parent_row.addClass("valid");
                }


                invalid_row_count();

                return parent_row;
            }


            $(document).on('change', '.formula_field', function () {
                let self = this;
                let parent_row = $(self).parents('.row .justify-content-around');
                var formula_row = parent_row.find(".assign_formula");

                // FETCHER
                var formula_field = parent_row.find(".formula_field");
                var right_selection = parent_row.find(".right_array");
                var left_selection = parent_row.find(".left_array");
                var with_field = parent_row.find(".with_field");
                var replace_field = parent_row.find(".replace_field");

                row_node = {from: [], to: []};

                // formula
                row_node.with_formula = parent_row.find(".formula_field").val();

                // from values
                if (formula_field.val() != "short_code") {
                    $.each(left_selection, function (index, val) {
                        row_node.from.push($(val).val());
                    })
                } else {
                    row_node.from.push("");
                }

                // to values
                $.each(right_selection, function (index, val) {
                    row_node.to.push($(val).val());
                })

                // with field
                if (with_field.val() !== "undefined") {
                    row_node.with = with_field.val();
                }

                // formula field
                if (replace_field.val() !== "undefined") {
                    row_node.replace = replace_field.val();
                }


                var row_count = formula_row.data('count');
                var formula = $(this).val();

                // Ajax Call
                $.ajax({
                    url: "{{ route('fetch.formula.fields') }}",
                    method: "POST",
                    data: {
                        input_array: JSON.stringify(data.input_array),
                        output_array: JSON.stringify(data.output_array),
                        formula: formula,
                        row_node: row_node ?? null,
                        row_count: row_count,
                        method_type: data.data_required.template_method_type


                    },
                    success: function (data) {
                        formula_row.replaceWith(data.output);

                        var left_column_div = left_selection.parents('.left_div_selection');
                        left_column_div.replaceWith(data.output_left);
                        left_selection.trigger('change');
                        parent_row = $(self).parents('.row .justify-content-around');
                        row_obj_checker(parent_row);
                        invalid_row_count();
                    },
                    error: function (error) {
                        console.log(error);
                    }
                });


            });


            $(document).on('change', '#add_row .left_array', function () {
                let self = this;

                let check_flag = true;
                if ($(this).val() == '') {
                    check_flag = false;
                }


                left_label = $("option:selected", this).parent().attr('label');


                right_arrays = $(self).parents(':eq(2)').find(".right_array");


                if (!check_flag) {
                    $("option:selected", this).prop("selected", false);
                    right_labels = right_arrays.find('optgroup');
                    $.each(right_labels, function (index, item) {
                        $(item).find("option:selected").prop("selected", false);
                        $(item).prop('disabled', true);


                    });
                    return false;
                }

                $.each(right_arrays, function (index, item) {
                    right_labels = $(item).parent().find('optgroup');
                    selected_right = $(item).find("option:selected").parent().attr('label');
                    $.each(right_labels, function (lindex, litem) {

                        if (left_label && left_label === 'Variants') {
                            if (selected_right !== 'Variants') {
                                $("option:selected", this).prop("selected", false);
                            }
                            if ($(litem).attr('label') !== 'Variants') {
                                $(litem).prop('disabled', true);
                            } else {
                                $(litem).prop('disabled', false);
                            }
                        } else {
                            if (selected_right === 'Variants') {
                                $("option:selected", this).prop("selected", false);
                            }
                            if ($(litem).attr('label') === 'Variants') {
                                $(litem).prop('disabled', true);
                            } else {
                                $(litem).prop('disabled', false);
                            }
                        }

                    });

                });

            });


            $(document).on('change', '#add_row select', function () {
                parent_row = $(this).parents('.row .justify-content-around');
                parent_row = row_obj_checker(parent_row);
            });


            function initialize_mapping() {
                $("#add_row").show();
                $("#save_template_btn").prop('disabled', false);
                $("#cancel-btn").prop('disabled', false);
                $("#mapping_loader").hide();

                invalid_row_count();
                $("#mapping_counter").html("( 0% )");
                input_row_selector = "";
            }


            function total_mapping_rows(rows) {
                total_mapping_rows_count = 0;
                $.each(rows, function( index, value ) {
                    total_mapping_rows_count += value.length;
                });

                return total_mapping_rows_count;
            }

            function isIterable(obj) {
                return obj != null && typeof obj[Symbol.iterator] === 'function';
            }

            function chunkArray(array, chunkSize) {
                var result = [];
                for (let i = 0; i < array.length; i += chunkSize) {
                    result.push(array.slice(i, i + chunkSize));
                }
                return result;
            }


            // $(document).ready(function () {

            window.addEventListener('load', (event) => {

                var total_input_attributes = data.input_array.nodes;

                total_mapping_rows_count = total_mapping_rows(total_input_attributes);

                // check if template options exist then create option fields
                @if(isset($data['template_attributes']['template']['variant']['variant_options']))
                const variant_options = data['template_attributes']['template']['variant']['variant_options'];
                $.each(variant_options, function (index, val) {
                    template_option = val;
                    add_variant_option();
                });
                @endif


                // check if template data selected or not
                const templateData = data?.['template_attributes']?.['template']?.['data'];
                if(templateData !== undefined) {
                    auto_selector_on = templateData.length === 0;
                }
                else {
                    auto_selector_on = true;
                }

                // check for create template row or auto selector nodes row
                if (auto_selector_on) {

                    var chunkedAttributes = [];

                    $.each(data['input_array']['nodes'], function (family, family_attributes) {
                        if (Array.isArray(family_attributes)) {
                            for (var i = 0; i < family_attributes.length; i += 30) {
                                var chunk = family_attributes.slice(i, i + 30);
                                chunkedAttributes.push({[family]: chunk});
                            }
                        }
                    });
                    total_mapping_rows_count = chunkedAttributes.length;
                    $.each(chunkedAttributes, function (_, attribute) {
                        input_row_selector = attribute;
                        if (input_row_selector !== '' && input_row_selector != null) {
                            promises.push(create_row());
                        }
                        if (Object.values(attribute)?.[0] !== undefined) {
                            global_row_count += Object.values(attribute)[0].length - 1;
                        }
                    });
                }else {
                    let rows = 0;

                    if (data['template_attributes']['template']['data'].length === 0) {
                        rows = 4;
                        total_mapping_rows_count = rows;
                        all_row_nodes = [];
                    }
                    else {
                        var all_row_nodes = Object.values(data['template_attributes']['template']['data']);
                        all_row_nodes = chunkArray(all_row_nodes, 30);
                        rows = all_row_nodes.length;
                        total_mapping_rows_count = rows;

                    }

                    while (rows > 0) {
                        row_node_flag = true;

                        if (Object.keys(all_row_nodes).length > 0) {
                            row_node = all_row_nodes.shift();
                        }
                        rows--;
                        promises.push(create_row());
                        if (Object.values(row_node) !== undefined) {
                            global_row_count += Object.values(row_node).length - 1;
                        }
                    }
                    row_node_flag = false;
                }
                Promise.all(promises)
                    .then(responseList => {
                        $.each(responseList, function (index, data) {
                            field_row_mapping(data);
                        });
                        initialize_mapping();
                    })

                $("#pro_imp_btn").click(function (event) {
                    event.preventDefault();
                    passValidation = true;

                    $("#template_checkbox_div").show();
                    $("#pro_imp_start_btn").show();

                    passValidation = required_row_checker(passValidation);
                    if (passValidation) {
                        $('#mapping_product').modal('show');
                    }
                });

                $("#save_template_btn").click(function (event) {
                    event.preventDefault();
                    passValidation = true;

                    passValidation = required_row_checker(passValidation);

                    if (passValidation) {
                        $('#mapping_product').modal('show');
                    }
                    if (!$('input[name="temp_status"]').is(':checked')) {
                        $('input[name="temp_status"]').trigger('click');
                    }

                    $("#template_checkbox_div").hide();
                    $("#pro_imp_start_btn").hide();

                })



                $("input[name=temp_name]").on('input',function (param) {
                    default_templates = [
                        'Shopify Default',
                        'Magento Default'
                    ];
                    if(typeof(data.data_required.template_method_type) != "undefined"){
                        if(data.data_required.template_method_type == "export"){
                            if(jQuery.inArray($(this).val(), default_templates) !== -1){
                                $("#mapping_submit_popup").find('input , button').prop('disabled',true);
                                $("input[name=temp_status]").prop('disabled',true);
                                $("#save_temp_error").html("Template name already exist in this organization");
                                $("#save_temp_error").show();

                            }
                            else{
                                $("input[name=temp_status]").prop('disabled',false);
                                $("#mapping_submit_popup").find('input,button').prop('disabled',false);
                                $("#save_temp_error").hide();
                            }
                        }
                    }
                })


                function required_row_checker() {
                    $($("#add_row").find("select[required]").get().reverse()).each(function (i, obj) {

                        if (!obj.checkValidity()) {

                            obj.reportValidity();

                            passValidation = false;

                            $("#product-tab").click();

                            return;
                        }
                    });
                    $($("#variant").find("select[required]").get().reverse()).each(function (i, obj) {

                        if (!obj.checkValidity()) {

                            obj.reportValidity();

                            passValidation = false;

                            // $("#variant-tab").click();

                            return;
                        }
                    });

                    return passValidation;
                }


                $('select[name="nodes[variant][id]"]').on('change', function () {
                    if (!$(this).val()) {
                        $("#var_options").empty();
                        $('#add-variant-option-btn').show();
                        return false;
                    }
                    options_count = $("#var_options").find(".col-lg-3").length;
                    if (options_count == 0) {
                        add_variant_option();
                        $('select[name="nodes[variant][variant_options][]"]').attr("required", true);
                        $('#var_options').find('.btn-delete').addClass('d-none');

                    }

                });


            });
        </script>


        <script>
            $(window).scroll(function (e) {

                var $el = $('#myTab');
                var distance = $el.offset().top,
                    $window = $(window);
                var isPositionFixed = ($el.css('position') == 'fixed');
                if ($(this).scrollTop() > distance && !isPositionFixed) {
                    if(!$el.hasClass("mapping_nav_sticky")){
                        $el.addClass("mapping_nav_sticky");
                    }
                    return false;
                }
                if ($(this).scrollTop() < 100 && isPositionFixed) {
                    if($el.hasClass("mapping_nav_sticky")){
                        $el.removeClass("mapping_nav_sticky");
                    }
                }
            });
        </script>


        {{--trigger create apimio column--}}
        <script>
            $(document).ready(function () {
                $("#mapping_add_attribute").on('click', function (event) {
                    event.preventDefault();
                    $("#create_column").modal('show');

                })
            })
        </script>


        <script>

            const form = $("#mapping_create_new_attribute_form");


            $(document).ready(function () {

                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });


                $('#create_column').on('hidden.bs.modal', function () {
                    form.trigger("reset");
                    $("#mapping_create_attribute_exist").hide();
                })

                $("#mapping_create_new_attribute_form input").on('focus', function () {
                    $("#mapping_create_attribute_exist").hide();
                })


                $('#mapping_create_new_attribute_btn').on('click', function (e) {
                    e.preventDefault();
                    $("#mapping_create_attribute_exist").hide();
                    let passValidation = true;
                    $(form.find("select[required],input[required]").get().reverse()).each(function (index, obj) {
                        if (!obj.checkValidity()) {
                            obj.reportValidity();
                            passValidation = false;
                            return;
                        }
                    })
                    if (passValidation) {
                        $.ajax({
                            url: '{{route('attributes.store')}}',
                            type: "POST",
                            data: form.serialize(),
                            beforeSend: function () {
                                $("#mapping_create_new_attribute_btn").html('<span class="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>');
                            },
                            complete:function () {
                                $("#mapping_create_new_attribute_btn").html('Create');
                            },
                            error: function (response) {
                                let error_message = $("#mapping_create_attribute_exist");
                                let errors = JSON.parse(response.responseText).errors;

                                // when attribute already exist in existing family
                                if (errors['duplicate_attribute_ids'] !== "undefined") {
                                    error_message.html("This attribute is already connected");
                                    error_message.show();
                                }

                            },
                            success: function (response) {
                                // re-write data of input and output dropdown
                                data = response.data;

                                // re-new families in create new attribute dropdown
                                $('#mapping_create_new_attribute_form').trigger('reset');
                                let all_families = response.data.apimio_attributes_required.all_families;
                                let family_datalist = $("#attribute_family_list");
                                family_datalist.empty();
                                all_families.forEach(function (item) {
                                    var datalist_option = $("<option></option>");
                                    datalist_option.val(item['name']);
                                    datalist_option.html(item['name']);
                                    family_datalist.append(datalist_option);
                                });

                                $("#mapping_create_new_attribute_main").hide();
                                $("#mapping_create_new_attribute_success").show();
                                setTimeout(function () {
                                    $("#create_column").modal('hide');
                                    $("#mapping_create_new_attribute_main").show();
                                    $("#mapping_create_new_attribute_success").hide();
                                }, 1000);

                                // refresh rows where we can add new added attribute in all dropdowns
                                row_refresh();


                            }
                        });
                    }
                });


                // saving or update for template
                $('#template-btn').on('click' , function (event){
                    event.preventDefault();
                    $("#save_temp_error").hide();


                    var self = $(this);
                    var previousText = self.data('btn_name');
                    let passValidation = true;
                    $($("#mapping_product").find("select[required],input[required]").get().reverse()).each(function (index, obj) {
                        if (!obj.checkValidity()) {
                            obj.reportValidity();
                            passValidation = false;
                            return;
                        }
                    })

                    if (passValidation) {
                        $.ajax({
                            url: '{{route('template.save')}}',
                            type: "POST",
                            data: $('#mainform').serialize(),
                            beforeSend: function () {
                                self.html('<span class="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>');
                                self.prop('disabled',true);
                            },
                            error: function (response) {
                                var response_data = response.responseJSON;
                                $("#save_temp_error").html(response_data.message);
                                $("#save_temp_error").show();

                            },
                            success: function (response) {
                                var response_data = response.data;
                                $("#mapping_product").modal('hide');
                                $("input[name=temp_id]").val(response_data.id);
                                $("input[name=temp_name]").val(response_data.name);
                                $("input[name=temp_name]").prop('readonly',true);
                                toastr.success(response.message);
                            },
                            complete:function () {
                                self.html(previousText);
                                self.prop('disabled',false);
                            },
                        });
                    }

                })


                function row_refresh() {
                    $("#mapping_loader").show();
                    $("#add_row").hide();
                    let formula_fields = $("#add_row").find(".formula_field");
                    formula_fields.each(function (index, formula_field) {
                        $(formula_field).trigger("change");
                    });
                    $("#mapping_loader").hide();
                    $("#add_row").show();
                }
            })
        </script>

    @endpush
@endsection

