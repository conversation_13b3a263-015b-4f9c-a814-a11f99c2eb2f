@extends('gallery::layouts.app')
@section('titles','Gallery')
@section('content')
    @php
        $variable = 'value2';
    @endphp
    @push("header_scripts")
        {{--        <link rel="stylesheet" href="{{asset('css/gallery/css/updated_gallery.css')}}"  type="text/css"/>--}}
        <link rel="stylesheet" href="{{asset('css/gallery/css/checkbox.css')}}" type="text/css"/>
        <link rel="stylesheet" href="{{asset('gallery_assets/scss/screen.css')}}" type="text/css"/>
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.0/dropzone.min.css">

        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.0/dropzone.js"></script>
    @endpush
    <style>
        .download-btn {
            display: none !important
        }

        #folder-main2 {
            display: none;
        }

        #hide-folders {
            display: none;
        }
        .media-badges-score{
            padding: 8px 20px !important;
        }

    </style>
    <div class="d-flex justify-content-between asset_header" style="margin-bottom: -50px;">
        <div class="mb-2">

            <h2 class="m-0">Media</h2>
            <p class=" asset_description"> Digital asset management (DAM) helps you get more value from creative digital
                assets like images by making them easy to organize, access and distribute.</p>
        </div>
        <div class=" btn_section">
            <x-gallery-asset-button></x-gallery-asset-button>
            <x-gallery-folder-create id="create_folder" header="Create Folder" title="Folder Name" btnText="Save"
                                     formUrl="{{route('gallery.store')}}"></x-gallery-folder-create>

        </div>
    </div>

    {{--    media search  start--}}
    {{--    Folder Component--}}
    <h2 class="mt-4 mb-1" style="margin-top:48px !important;">Folders</h2>
    <div class="collection " id="folder-main">
        @foreach($data['folders']->slice(0,12) as $folder)
            <x-gallery-folder name="{{$folder->name}}" id="{{$folder->id}}" link="{{$folder->link}}"/>
        @endforeach

    </div>
    <div class="collection " id="folder-main2">
        @foreach($data['folders'] as $folder)
            <x-gallery-folder name="{{$folder->name}}" id="{{$folder->id}}" link="{{$folder->link}}"/>
        @endforeach

    </div>
    @if(count($data['folders'])>12)
        <button type="button"
                class="mt-1 all-folders"
                id="show-folders"
                style="float: right; color: #6C757D; text-decoration-line: underline; font-weight: 700; border: hidden; background-color:transparent ">
            Show all ({{count($data['folders'])}})
        </button>
    @endif
    <button type="button"
            class="mt-1"
            id="hide-folders"
            style="float: right; color: #6C757D; text-decoration-line: underline; font-weight: 700; border: hidden; background-color:transparent ">
        Show less
    </button>

    {{--Storage Bar--}}
    <div>
        <h2 class="mt-3">Storage</h2>
        <div class="progress mb-1" style="width: 100%; height: 10px">
            <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                 style="width:{{($data['plan']->sum/(isset($data['plan']->subplan->storage) ? $data['plan']->subplan->storage : 2))*100}}%"></div>

        </div>
        <p class="mb-4 storage-text ml-1">{{$data['plan']->sum}} GB
            of {{(isset($data['plan']->subplan->storage) ? $data['plan']->subplan->storage : 2)}} GB.</p>


        {{--    Scoring component--}}

        <div>
            <x-gallery-scoring-graph-by-product></x-gallery-scoring-graph-by-product>
        </div>
        <div class="d-flex justify-content-between mt-3">
            @if( $data['files']->total()>0)

{{--                <div>--}}


{{--                    <h2 class="mb-1">Images</h2>--}}
{{--                    <p class="page-subheading mb-4 total_img">Total Images: {{ $data['files']->total()}}</p>--}}
{{--                </div>--}}

                <div>
                    <h2 class="mb-1">Images</h2>

                    <div class="d-flex gap-3 align-items-baseline">
                        <!-- Conditional content based on image_type parameter -->
                        @php
                            $imageType = request()->query('image_type'); // Get the image_type parameter from the URL
                        @endphp

                        @if ($imageType)
                            @if ($imageType === 'approve')
                                <p class="page-subheading mb-4 total_img">Good Images: {{ $data['files']->total() ?? 0 }} / {{ $totalfiles ?? $data['files']->total() }}</p>
                            @elseif ($imageType === 'warning')
                                <p class="page-subheading mb-4 total_img">Average Images: {{ $data['files']->total() ?? 0 }} / {{ $totalfiles ?? $data['files']->total() }}</p>
                            @elseif ($imageType === 'error')
                                <p class="page-subheading mb-4 total_img">Bad Images: {{ $data['files']->total() ?? 0 }} / {{ $totalfiles ?? $data['files']->total() }}</p>
                            @endif
                            <a href="javascript:void(0)" onclick="resetFilters()">See All</a>
                        @else
                            <!-- If no image_type parameter, show total images without "See All" link -->
                            <p class="page-subheading mb-4 total_img">Total Images: {{ $data['files']->total() }}</p>
                        @endif
                    </div>
                </div>

                <ul class="nav nav-tabs" role="tablist" style="float: right; align-self: center">
                    <li class="nav-item nav-pills">
                        <a class="nav-link active"
                           data-bs-toggle="tab"
                           href="#tabs-1">
                            <img src="https://svgshare.com/i/po3.svg" alt="L" style="height: 14px">
                        </a>
                    </li>
                    <li class="nav-item nav-pills">
                        <a class="nav-link"
                           data-bs-toggle="tab"
                           href="#tabs-2">
                            <img src="https://svgur.com/i/po4.svg" alt="B" style="height: 14px">
                        </a>
                    </li>
                </ul>
        </div>
        @endif

        @if( $data['files']->total()==0)
            <p class="Roboto mx-auto text-center mt-5 " style="font-weight: 400; margin-top: 150px !important;">
                No files found.
            </p>
        @endif
        @if( $data['files']->total()>0)


            <div class="tab-content">
                <div class="tab-pane active" id="tabs-1">
                    <div>
                        <x-gallery-asset-table :files="$data['files']"></x-gallery-asset-table>
                    </div>
                </div>

                <div class="tab-pane" id="tabs-2">
                    <x-gallery-asset-card :files="$data['files']"></x-gallery-asset-card>
                </div>
            </div>
        @endif



        {!! $data['files']->appends(request()->query())->links() !!}

        <x-gallery-image-uploader></x-gallery-image-uploader>
        <x-gallery-rename-folder></x-gallery-rename-folder>
        <x-gallery-unlink-product></x-gallery-unlink-product>
        @endsection
        @push('footer_scripts')
            <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

            @error('folder_name')

            <script>
                $("#create_folder").modal('show')
            </script>
            @enderror


            <script>

                $(document).ready(function () {
                    $('#modal_aside').modal('show');
                });
                //Show all folders function
                $(".all-folders").on('click', function () {
                    $("#folder-main").css("display", "none")
                    $("#show-folders").css("display", "none")
                    $("#folder-main2").css("display", "grid")
                    $("#hide-folders").css("display", "block")
                })
                $("#hide-folders").click(function () {
                    $("#folder-main").css("display", "grid")
                    $("#show-folders").css("display", "block")
                    $("#folder-main2").css("display", "none")
                    $("#hide-folders").css("display", "none")
                });

                let input = document.querySelector(".share_link")
                let folder_count = {!! json_encode(count($data['folders'])) !!};
                let tooltip_name = document.querySelector("#folder-count")
                let tooltip = document.querySelector(".folder-card")
                const height_info = document.querySelector(".height_info")
                const size_info = document.querySelector(".size_info")
                const width_info = document.querySelector(".width_info")
                const type_info = document.querySelector(".type_info")
                const img_info = document.querySelector(".img_info");
                let card_info = document.querySelectorAll(".card_info")
                let file_data = document.querySelector(".file_id")
                let table_img_data = {!! json_encode($data['files']) !!};

                //empty page condition
                if (table_img_data.data.length === 0) {
                    $(".tab-content").hide()
                    $(".total_img").hide()
                    $(".tabs").hide()
                }
                // Loop through each div element with the class tooltip_name

                const new_name = document.querySelectorAll(".tooltip_name")
                let folder_name = {!! json_encode($data['folders']) !!};
                for (let i = 0; i <= new_name.length; i++) {

                    let tooltip_id = new_name[i];
                    if (folder_name[i].name.length > 19) {
                        $(tooltip_id).attr('data-bs-original-title', folder_name[i].name)
                    } else {
                        $(tooltip_id).attr('data-bs-original-title', '')
                    }
                }
                let download_link = document.querySelector(".download_img")


                // $("#folder-main2").hide()

                //Asset sidebar js


                // $(".card_info").click(function() {
                //     load_data(this, ".card_info")
                // });

                function initialize(element, isProgrammaticClose, isTriggered) {

                    // $("#modal_aside").show()
                    var file_id = $(element).data("id");
                    let file_ids = file_id
                    // $("#sidebar").html('<div class="loader"></div>');
                    $.ajax({
                        url: "{{ url('file/details/') }}/" + file_id,
                        type: "GET",
                        data: {
                            file_id: file_id
                        },
                        beforeSend: function () {
                            // show loader
                        },
                        complete: function (data) {
                            // hide loader
                            $(document).ready(function () {
                                $('#example').select2({
                                    templateResult: function (data, container) {
                                        if (!data.id) {
                                            return data.text;
                                        }
                                        let $element = $(data.element);
                                        let image_url = $element.data('image');
                                        let $wrapper = $('<span><img src="' + image_url + '" class="img-thumbnail" /> ' + data.text + '</span>');
                                        return $wrapper;
                                    },
                                    multiple: true,
                                    dropdownParent: $('#modal_aside'),
                                    placeholder: 'Search by SKU or name'

                                });
                                $('#example').val('').trigger('change');
                            });

                        },
                        success: function (data) {
                            // your success code here

                            // Initialize flag to false
                            if ($("#sidebar_modal").length > 0) {
                                // element with ID "elementId" exists
                                $("#sidebar_modal").remove();
                            }
                            if (isTriggered) {


                                $("body").get(0).insertAdjacentHTML("afterbegin", data);
                                $('#modal_aside').on('shown.bs.modal', function () {
                                    $('a[href="#profile"]').tab('show');
                                }).modal('show');
                            } else {
                                $("body").get(0).insertAdjacentHTML("afterbegin", data);
                                $("#modal_aside").modal("show");
                            }
                            // $('#example').select2({
                            // });
                            const modalElement = document.getElementById('modal_aside');
                            if (isProgrammaticClose) { // Check if modal is not being closed programmatically
                                modalElement.addEventListener('hidden.bs.modal', function (event) {
                                    // Check if the modal was closed due to a click outside the modal
                                    if (event.target === modalElement) {
                                        isTriggered = false; // Set isTriggered variable to false

                                    }
                                });
                            }
                            $(document).ready(function () {
                                $('[data-toggle="tooltip"]').tooltip();
                            });

                            $('.btn-copy').on('click', function (e) {
                                let copyText = document.getElementById("copy-input");
                                navigator.clipboard.writeText(copyText.value);
                                copyText.style.backgroundColor='blue';
                                copyText.style.color='white';
                                copyText.style.width='80%';
                                $('.btn-copy').css('min-width','60px');
                                $('.btn-copy').text('copied!');

                                setTimeout(()=>{
                                    copyText.style.backgroundColor='';
                                    copyText.style.color='';
                                    $('.btn-copy').html('<i class="fa fa-clipboard" style="color: black;" aria-hidden="true"></i>');
                                    copyText.style.width='87%';
                                    $('.btn-copy').css('min-width','40px');

                                },2000)
                                e.preventDefault()
                            });

                            let selectedIds = [];
                            $('#example').on('select2:select', function (e) {
                                var data = e.params.data;
                                let selectedOptionValue = data.id;
                                selectedIds.push(selectedOptionValue);
                            });

                            $('#example').on('select2:unselect', function (e) {
                                var data = e.params.data;
                                let selectedOptionValue = data.id;
                                let index = selectedIds.indexOf(selectedOptionValue);
                                selectedIds.splice(index, 1);
                            });


                            $('#submit_btn').click(function () {
                                file_id = file_ids

                                let data = null;
                                let file = null;
                                // if(selectedIds > 0) {
                                $.ajax({
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    url: '{{ route('assign.file') }}',
                                    method: 'POST',
                                    data: {
                                        product_id: selectedIds,
                                        file_id
                                    },
                                    success: function (response) {

                                        $("#modal_aside").modal("hide");
                                        isTriggered = true;
                                        isProgrammaticClose = false;

                                        initialize(element, isProgrammaticClose, isTriggered)
                                        //  $('#example').val(null).trigger('change');
                                        //  for (let i = 0; i < selectedIds.length; i++) {
                                        //      $('#example').find(`option[value="${selectedIds[i]}"]`).remove();
                                        //  }
                                        //
                                        //
                                        // data = JSON.parse(response)
                                        //  let cardDisplay = document.querySelector('.card-container');
                                        //  let html = '';
                                        //
                                        //  for (let i = 0; i < data.length; i++)
                                        //  {
                                        //       file = data[i];
                                        //      html += ` <div class="card link-card py-3 px-2 mt-1" style="margin-left: 3px; margin-right: 3px; box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;  ">
                                        //          <div class="d-flex justify-content-between custom-margin ">
                                        //              <h5 class="info-typography head-pad link-card-text ms-2 fs-16 fw-600">SKU</h5>
                                        //              <p class="asset-details-typo link-card-text me-4 fs-14" id="product_name"> ${file.sku}</p>
                                        //          </div>
                                        //
                                        //          <div class="d-flex justify-content-between">
                                        //              <h5 class="info-typography link-card-text head- ms-2 fs-16 fw-600">Product Name</h5>
                                        //              <p class="asset-details-typo link-card-text me-4  fs-14" id="pro_id" data_id="${file.id}"> ${file.id}</p>
                                        //          </div>
                                        //          <div>
                                        //              <button type="button" class="btn btn-outline-danger   px-3 ms-2 unlink_btn" id="btn_unlink" data-bs-toggle="modal" data-bs-target="#unlink_product" style="opacity: 1;">Unlink
                                        //                  product
                                        //              </button>
                                        //          </div>
                                        //      </div>`
                                        //      // const buttons = document.querySelectorAll('#btn_unlink');
                                        //
                                        //  }
                                        //  cardDisplay.innerHTML = html;

                                    },
                                    complete: function () {
                                        // Use variables after exiting AJAX call
                                        data = null
                                        file = null
                                        selectedIds = []
                                        file_id = null

                                        // Refresh the select2 dropdown to update the UI
                                        // $('#example').select2('refresh');
                                    }
                                });
                                // }
                            });
                            let selectedFolder
                            $("#folder_change").change(function () {
                                var selectedFolder = $(this).val();
                            });
                            const buttons = document.querySelectorAll('#btn_unlink');
                            let selectedID = '';
                            let card;
                            let IDS = [];
                            // let selected = []
                            buttons.forEach(button => {
                                button.addEventListener('click', function () {
                                    card = this.closest('.card');
                                    const pTag = card.querySelector('#pro_id');
                                    // const sku_name = card.querySelector('#product_name');
                                    // const new_name = sku_name.textContent;
                                    IDS = pTag.getAttribute('data_id');
                                    // IDS = selected;
                                });
                            });

                            $('#unlink').click(function () {
                                file_id = file_ids
                                // selectedID = IDS
                                if (IDS) {
                                    $.ajax({
                                        headers: {
                                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                        },
                                        url: "{{ url('unassign/file/') }}/" + IDS,
                                        type: "post",
                                        data: {
                                            product_id: IDS,
                                            file_id
                                        },
                                        success: function (response) {
                                            // location.reload()
                                            $("#modal_aside").modal("hide");

                                            isTriggered = true;
                                            isProgrammaticClose = false;
                                            initialize(element, isProgrammaticClose, isTriggered)
                                            // $('#modal_aside').on('shown.bs.modal', function() {
                                            //     $('#profile-tab').tab('show');
                                            // })
                                            $("#unlink_product").modal("hide");
                                            // card.style.display = 'none'
                                            // const productId = IDS
                                            // console.log(productId)
                                            // $('#example').append($('<option>', {
                                            //     value: IDS
                                            // }));

                                        },
                                        complete: function () {
                                            // Use variables after exiting AJAX call

                                            // $('a[href="#profile"]').tab('show');
                                            IDS = null;
                                            file_id = null;
                                            file_ids = null;
                                            // Refresh the select2 dropdown to update the UI
                                            // $('#example').select2('refresh');
                                        }
                                    });
                                }
                            });

                            let image_name = $("#image_name")

                            //TODO::we need to make folder folder optional
                            $('#save_details').click(function () {
                                let selectedFolder = $("#folder_change").val();
                                let inputValue = $("#image_name").val()
                                $.ajax({
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    url: "{{ route('update.file') }}",
                                    type: "post",
                                    data: {
                                        folder_id: selectedFolder,
                                        file_id: file_id,
                                        image_name: inputValue
                                    },
                                    success: function (response) {
                                        location.reload()


                                    }
                                });
                            });
                        },
                        error: function (error) {
                            // your error code here
                            $("#sidebar").html("<p>Something went wrong. Please refresh and try again.</p>");
                        }
                    });


                }

            </script>
            <script>


                let card_img = document.querySelector(".card-img-top")
                let image_modal = document.querySelector(".image-upload")
                let image_modal_close = document.querySelector(".image-move")

                function open_side() {
                    $("#modal_aside").modal('toggle');
                }

                //    Css Conditional Rendering

                //Image uploading modal
                function close_image_modal() {
                    image_modal.style.display = 'none'
                }

                function open_image_modal() {
                    image_modal.style.display = 'block'
                }

                //    Divider
                let main = document.getElementById('folder-main')
                document.getElementById('divider-bar').addEventListener('click', function () {

                    (main.style.height === '103px' || main.style.height === '')
                        ? main.style.height = 'auto'
                        : main.style.height = '103px';

                }, false);


            </script>
    @endpush


    {{--// $("#folder-main").append(`<x-gallery-folder id="${id} name="${value?.name}"  link="${value.link}"/>`)--}}
    {{--$("#folder-main").append(`<x-gallery-folder name="${value.name}" id="${value.id}"/>`);--}}
    {{--// const obj = $.parseJSON(data);--}}
    {{--// console.log(obj);--}}
    {{--// $("#folder-main").append('<span>like</span>');--}}
