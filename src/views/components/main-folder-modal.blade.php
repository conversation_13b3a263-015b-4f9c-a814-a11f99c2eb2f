<div class="card d-flex folder-card  folder-card-modal input-width-modal align-self-center" >
    <div class="p-2 folder">
        <a href="#" class="card-content">
            <em class="fa-regular fa-folder mr-3 folder-icon">

            </em>
            Default Folder</a>
        <div class="dropdown btn-dot">

            <a data-toggle="dropdown" class="drop_btn" style="color: #5B5B5B">

                <em class="fa-solid fa-chevron-right folder-arrow "  onclick="open_sub_folder()"> </em>
            </a>
        </div>
    </div>
</div>
<script>
    let sub_icon = document.querySelector(".folder-arrow")

    function open_sub_folder() {
        folder_div.setAttribute ('style', 'display: none !important;');

        sub_folder_div.setAttribute ('style', 'display: block !important; margin-left: 8px;');
        go_back.setAttribute('style', 'display: block !important;')
    }
function open_main_folder() {
    folder_div.setAttribute ('style', 'display: block !important; margin-left: 8px;');

    sub_folder_div.setAttribute ('style', 'display: none !important; margin-left: 8px;');
    go_back.setAttribute('style', 'display: none !important;')
}
</script>
