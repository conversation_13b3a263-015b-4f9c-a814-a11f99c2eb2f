<!-- The Modal -->
<div class="modal fade" id="{{$id}}" tabindex="-1" aria-labelledby="example-{{$id}}" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <div>
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel" style="font-size: 20px">
                    {{$header}}
                </h5>
                <small>{{$message}}</small>
                </div>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post"  action="{{ $formUrl }}" id="rename-form">
                    @csrf

                    @isset($folderId)
                        <input type="hidden" name="id" value="{{$folderId}}">
                    @endisset

                    <div class="form-group mb-5">
                        <label class="mb-1">{{$title}}</label>
                        <input type="text" class="form-control @error("folder_name") is-invalid @enderror" id="folder-name" name="folder_name" value="{{isset($folderName) ? $folderName : old('folder_name')}}"
                               required>
                        @error("folder_name")
                        <span class="text-danger">{{$message}}</span>
                        @enderror
                        <input type="hidden" class="form-control" id="link" name="parent_link" value="{{$link}}" required>
                        <input type="hidden" class="form-control" id="folder_id" name="folder_id" value="{{isset($parentFolderId) ? $parentFolderId : null}}">
                        <span class="text-danger" role="alert">
                            <small></small>
                        </span>
                    </div>
                    <div class="form-group " style="float: right !important; " >
                        <button type="submit" class="btn btn-primary btn-width ripplelink ">{{$btnText}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

