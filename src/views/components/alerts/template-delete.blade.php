<div class="modal fade" id="template_delete_alert"
      tabindex="-1" aria-labelledby="deletemodaltemplate" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="exampleModalLabel">Delete Template</h3>
                <button type="button" data-dismiss="modal" aria-label="Close" class="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-template-delete-modal" >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body pb-2 pt-3">
                <form action="{{route('mapping_delete_template')}}" id="delete_template_form">
                    <input type="hidden" name="template_delete" value="">
                    <div class="d-flex justify-content-center align-items-center flex-column">
                        <i class="fas fa-3x text-danger fa-folder-minus"></i>
                        <span style="font-size: 18px; font-weight: 700" class="mb-2"><span id="template_delete_name"></span></span>
                        <span class="w-75 text-center mb-3" style="line-height: 20px">Do you really want to delete this template ? This process cannot be undone.</span>
                    </div>
                    <hr class="mb-0" style="width: 100%;border-top: 1px solid rgba(0,0,0,.1);">
                    <div class="form-group d-flex justify-content-between pt-3 mb-2">
                        <button type="button" class="btn btn-secondary close-template-delete-modal" data-dismiss="modal" aria-label="Close">
                            No, Cancel
                        </button>
                        <button type="button" id="template_delete_btn" class="btn btn-danger mb-0 ripplelink " id="delete_template_btn">Yes, Delete</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')

<script>
$(".close-template-delete-modal").click(function(){
    $("#template_delete_alert").modal('hide');
})

</script>
@endpush
