<!-- The Modal -->
<style>
    .filepond--item {
        width: 100%;
        margin-left: -25px !important;
    }
</style>
<link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet"/>
<link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css"
      rel="stylesheet">

<div class="modal fade image-upload" id="asset_upload_image" tabindex="-1" style="overflow: auto !important; " aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content image-upload-width">
            <div class="modal-header">
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel" style="font-size: 20px">
                    Upload Image
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post">

                    <div class="form-group mb-5">
                        <div>
                            <input
                                value="" type="image"
                                name="file[]" class="filepond"
                                id="file" data-allow-reorder="true"
                                data-max-file-size="3MB"
                                alt=""/>
                        </div>
                        <span class="text-danger" role="alert">
                        </span>
                    </div>
                    <div class="mt-5">
                        <button type="submit" class="btn btn-primary btn-width  upload-btn  ripplelink" style="margin-top: 60px"> Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{{-- Move To Modal--}}
<!-- The Modal -->

<br><br>

<script src="https://unpkg.com/filepond@^4/dist/filepond.js"></script>
<script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.js"></script>

<script
    src="https://unpkg.com/filepond-plugin-image-exif-orientation/dist/filepond-plugin-image-exif-orientation.js"></script>
<script src="https://unpkg.com/filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.js"></script>
<script src="https://unpkg.com/filepond/dist/filepond.js"></script>

<script>
    const inputElements = document.querySelector("#file");

    FilePond.registerPlugin(FilePondPluginImagePreview, FilePondPluginImageExifOrientation), FilePond.registerPlugin(FilePondPluginFileValidateType);
    // Create a FilePond instance
    const pond1 = FilePond.create(inputElements);
    FilePond.setOptions(
        {
            styleProgressIndicatorPosition: 'center',
            styleButtonProcessItemPosition: 'center',
            acceptedFileTypes: ['image/png', 'image/jpg', 'image/jpeg', 'image/svg+xml', 'image/gif', 'image/webp', 'image/bmp', 'image/x-icon']

        }
    )


</script>
