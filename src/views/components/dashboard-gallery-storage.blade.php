<div class="col-12 col-xxl-6 mt-4 d-flex  align-content-stretch">
    <div class="border rounded-3 shadow-sm px-4 py-4 w-100">
        <h3 class="fw-700 text-uppercase mb-0">Storage Usage</h3>
        <p class="mb-0 fs-14">You are currently on {{ isset($storage['plan']->subplan->name) ?  $storage['plan']->subplan->name : 'Free Plan'}}.
            <a href="{{route('billing')}}" class="text-decoration-none text-primary fw-700">Upgrade Plan</a></p>
        <div class="d-flex flex-column flex-sm-row justify-content-between mt-4 ms-xxl-4">
            <div class="field-score-chart d-inline-flex align-self-center position-relative">
                <canvas id="storage-usage"></canvas>
                {{--  <h2 class="mb-0" style="position: absolute;top: 39%; left: 35%;z-index:-1;">70%</h2>--}}
            </div>
            <div
                class="d-flex flex-column align-items-start align-items-md-center  justify-content-around ms-4">
                <span class="score-label score-label-publish mt-4 mt-md-0"></span>
                <span class="score-label score-label-grey mt-4 mt-md-0"></span>

            </div>
            <div
                class="d-flex flex-column justify-content-sm-start justify-content-md-center mt-4 mt-md-0 ms-4 flex-grow-1">
                <h4 class="mb-0 fw-700">Used Storage </h4>
                <p class="mb-0 ">{{$storage['plan']->sum}} GB used storage out of {{ isset($storage['plan']->subplan->storage) ? $storage['plan']->subplan->storage : 2}} GB</p>
                <h4 class="mb-0 fw-700 mt-1">Remaining Storage</h4>
                <p class="mb-0">{{(isset($storage['plan']->subplan->storage) ? $storage['plan']->subplan->storage : 2) - $storage['plan']->sum}} GB remaining storage out of {{isset($storage['plan']->subplan->storage) ? $storage['plan']->subplan->storage : 2}} GB</p>
            </div>
            <div class="d-flex flex-column align-items-start align-items-md-center justify-content-center  mt-1 mt-md-0 ms-4">
                <p class="fs-32 text-primary fw-700 mb-1">{{isset($storage['plan']->subplan->storage) ? $storage['plan']->subplan->storage : 2}}<span class="fs-18">GB</span></p>
                <p class="fs-16 mb-0">Total Storage</p>
            </div>
        </div>
    </div>
</div>
@push('footer_scripts')
    <script>
        const storageUsage = document.getElementById('storage-usage');

        new Chart(storageUsage, {
            type: 'doughnut',
            data: {
                labels: [
                    'Storage used',
                    'Storage remaining',
                ],
                datasets: [{
                    data: [
                        {{$storage['plan']->sum}},
                        {{(isset($storage['plan']->subplan->storage) ? $storage['plan']->subplan->storage : 2) - $storage['plan']->sum}}
                    ],
                    backgroundColor: [
                        '#2C4BFF',
                        '#F2F2F2',
                    ],
                    hoverOffset: 4
                }]
            },
            options: {
                cutout: 35,
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            },
        });
    </script>
@endpush
