<div id="table-scroll" class="table-scroll" style="display: none">
    <div class="table-wrap">
        <table class="main-table table table-hover table-borderless" id="main-table">
            <thead class="thead-light" style="height: 60px;">
            <tr class="custom-border-bottom-css">

                <th scope="col" class="fixed-side Roboto bold text-dark text-center border-radius-left "
                    style="width: 75px;min-width: 110px">
                    IMAGE
                </th>
                {{--                <th scope="col" class="fixed-side Roboto bold text-dark text-center"--}}
                {{--                    style="width: 50px;min-width: 110px; padding-left: 34px;">--}}
                {{--                    IMAGE NAME--}}
                {{--                </th>--}}
                <th scope="col" class="fixed-side Roboto bold text-dark text-center"
                    style="width: 50px;min-width: 110px; padding-left: 34px;">
                    IMAGE QUALITY
                </th>
                <th scope="col" class="Roboto bold text-dark text-center" style="width: 50px;min-width: 110px">
                    TYPE
                </th>
                <th scope="col" class="Roboto bold text-dark disabled-sorting text-center" style="width: 110px;min-width: 110px">
                    DIMENSIONS
                </th>

                <th scope="col" class="Roboto bold text-dark disabled-sorting text-center" style="width: 90px;min-width: 90px">
                    SIZE
                </th>
                @if(\Illuminate\Support\Facades\Auth::check())
                    <th scope="col" class="fixed-side Roboto bold text-dark text-center border-radius-right"
                        style="width: 113px;min-width: 110px">
                        ACTION
                    </th>
                @endif
            </tr>
            </thead>
            @foreach($files as $file)
                <tbody>
                <tr data-product-id="567" class="cursor-pointer border-radius product_row">
                    <td  data-id="{{ $file->id }}" class="fixed-side Roboto regular mt-3 text-center border-radius-left  column-clickable "  @if(!\Illuminate\Support\Facades\Auth::check()) data-bs-toggle="modal" data-bs-target="#modal_aside_left" @endif >
                        <div class="img-div-height " >
                            <img data-id="{{ $file->id }}" class="img-width" src="{{$file->link}}" alt="img">
                        </div>
                    </td>
                    {{--                    <td class="Roboto regular mt-3 text-center">--}}
                    {{--                        {{$file->name}}--}}
                    {{--                    </td>--}}

                    <td data-id="{{ $file->id }}" class="fixed-side Roboto regular mt-3 text-center " style="  padding-left: 30px;">
                        <img

                            @if($file->imageQualityScoreByImage($file)['approve'] == 100)
                            src="{{asset('img/icon_good.png')}}"
                            @elseif($file->imageQualityScoreByImage($file)['warning'] == 100)
                            src="{{asset('img/icon_Fair.png')}}"
                            @elseif($file->imageQualityScoreByImage($file)['error'] == 100)
                            src="{{asset('img/icon_bad.png')}}"
                            @endif
                            alt="" style="width: 25px" data-html="true"  data-toggle="popover" data-trigger="hover"
                            data-placement="right"
                            data-content="
                            1. Image should be JPG, WebP.
                             @if(in_array($file->ext,['JPG','WebP','jpg','png'])) ✔ @else ❌ @endif
                                <br/>
                           2. Image should be less than 200kb.
                              @if($file->size <=200 && $file->size >0) ✔ @else ❌ @endif
                                </br>
                           3. Image should be greater then or equal to 1080 x 1080 in dimension.
                            @if(($file->width >= 1080 ) && ($file->height >= 1080)) ✔ @else  ❌ @endif"
                            alt="">

                    </td>

                    <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-center ">
                        {{$file->ext}}
                    </td>
                    <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-center ">
                        {{$file->width}} X {{$file->height}}
                    </td>
                    <td  data-id="{{ $file->id }}" class="Roboto mt-3 text-center ">
                        <span class="badge badge-pill badge-light">
                            {{$file->size}} kb's
                        </span>
                    </td>
                    @if(\Illuminate\Support\Facades\Auth::check())
                        <td class="Roboto regular text-center border-radius-right">
                            <a class="btn-delete"
                               role="button"
                               data-toggle="modal"
                               data-target="#delete_asset-{{$file->id}}">
                                <img src="https://app.getapimio.com/media/retailer-dashboard/delete.png" alt="del">
                            </a>
                        </td>
                    @endif
                </tr>
                </tbody>
            @endforeach
        </table>
    </div>
</div>
<table class="table">
    <thead>
    <tr>
        <th scope="col">IMAGE</th>
        <th scope="col"> IMAGE QUALITY</th>
        <th scope="col">TYPE</th>
        <th scope="col">SIZE</th>
        <th scope="col">DIMENSION</th>
        <th scope="col" class="text-end">ACTION</th>

    </tr>
    </thead>

    @foreach($files as $file)
        <tbody>
        <tr>
            <td  class="column-clickable" style="width: 200px">
                            <div class="img-div-height my-1">
                            @if($file->bucket_url == null)
            <div class="background-image-css product-image-link" data-src="{{ isset($file->link) ? $file->link : asset('img/apimio_default.jpg') }}"></div>
                            @else
            <div class="background-image-css product-image-link" data-src="{{ isset($file->bucket_url) ? $file->bucket_url : asset('img/apimio_default.jpg') }}"></div>

                            @endif
        </div>
                        </td>
            <td data-id="{{ $file->id }}" class="column-clickable" style="width: 200px">
                @if ($file->width == null || $file->height == null || $file->size == null || $file->ext == null || $file->type == null)
                                            <img class="media-image" src="{{asset('img/processing_image.png')}}" alt="" style="width: 25px"
                                            data-bs-html="true"
                                            data-trigger="hover"
                                            data-toggle="tooltip"
                                            data-bs-placement="right" data-bs-original-title="@if($file->width == null || $file->height == null || $file->size == null || $file->ext == null || $file->type == null)
                                                        Image Processing.
                                                        @else
                                                        '{{trans('products_media.image_extension')}} 
                                                        @if(in_array($file->ext,['webp','jpg'])) ✔ @else ❌ @endif
                                                        <br/>
                                                        {{trans('products_media.image_size')}}
                                                        @if($file->size <=200 && $file->size >0) ✔ @else ❌ @endif
                                                        </br>
                                                        {{trans('media.image_dimension')}}
                                                        @if(($file->width >= 1080 ) && ($file->height >= 1080)) ✔ @else ❌ @endif'
                                                        @endif" alt="">
                                        @else
                                        <img class="media-image"
                                            @if($file->imageQualityScoreByImage($file)['approve'] == 100)
                                            src="{{asset('img/icon_good.png')}}"
                                            @elseif($file->imageQualityScoreByImage($file)['warning'] == 100)
                                            src="{{asset('img/icon_Fair.png')}}"
                                            @elseif($file->imageQualityScoreByImage($file)['error'] == 100)
                                            src="{{asset('img/icon_bad.png')}}"
                                            @endif
                                            alt="" style="width: 25px"
                                            data-bs-html="true"
                                            data-trigger="hover"
                                            data-toggle="tooltip"
                                            data-bs-placement="right"
                                            data-bs-original-title="
                                        1. Image should be JPG, WebP.
                                                    @if(in_array($file->ext,['webp','jpg'])) ✔ @else ❌ @endif
                                                <br/>
                                        2. Image should be less than 200kb.
                                                @if($file->size <=200 && $file->size >0) ✔ @else ❌ @endif
                                                </br>
                                        3. Image should be 1080 x 1080 in dimension.
                                                @if(($file->width >= 1080 ) && ($file->height >= 1080)) ✔ @else  ❌ @endif"
                                            alt="">
                                        @endif
                

            </td>

             <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-start column-clickable" style="width: 200px">
                            @if($file->ext && $file->size && $file->width && $file->height)
                            {{ $file->ext }}
                            @else
                            -
                            @endif
                        </td>
                        <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-start column-clickable" style="width: 200px">
                            @if($file->ext && $file->size && $file->width && $file->height)
                            {{ $file->size . ' KB' }}
                            @else
                            -
                            @endif
                        </td>
                        <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-start column-clickable" style="width: 200px">
                            @if($file->ext && $file->size && $file->width && $file->height)
                            {{ $file->width. ' X ' .$file->height . ' px' }}
                            @else
                            -
                            @endif
                        </td>
            <td class="text-end" style="width: 200px">
                <a href="#" data-id="" data-retailer-name=""
                   data-bs-toggle="modal"  data-bs-target="#delete_asset-{{$file->id}}" class="btn-delete text-decoration-none">
                    <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                </a>
            </td>
        </tr>


        </tbody>
        <x-gallery-delete-asset id="delete_asset-{{$file->id}}" fileid="{{$file->id}}" Link="{{route('file.delete',$file->id)}}"></x-gallery-delete-asset>

    @endforeach

</table>


@push("footer_scripts")
    <script>
        let img_div = document.querySelector(".table-img")
        $("#unlink[type='submit']").click(function () {
            if(!($(this).hasClass("search"))){
                var previous_text = $(this).html();
                $(this).attr("disabled", true);
                $(this).closest("form").submit();
                $(this).html("Please wait..");
                let _this = this;
                setTimeout(function()
                    {
                        $(_this).prop("disabled",false);
                        $(_this).html(previous_text);
                    },
                    1000);
            }

        })
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });

        $(document).ready(function(){
            // TODO: Fahad please use the selector as whole table row but not the last column.
            let isClickable = true;
            $(".column-clickable").click(function() {
                if (isClickable) {
                    isClickable = false;
                    let isProgrammaticClose = true;
                    var isTriggered = false;

                    initialize(this, isProgrammaticClose, isTriggered);

                    setTimeout(function() {
                        isClickable = true;
                    }, 2000); // 5000 milliseconds = 5 seconds
                }
            });
        });
    </script>

    <script>
    // Function to check and set background image for each file div
    function checkAndSetBackgroundImages() {
        // Select all divs with the class "background-image-css"
        var divs = document.querySelectorAll('.product-image-link');
        var defaultImg = '{{ asset("img/apimio_default.jpg") }}'; // Path to your default image
        var closeCircleImg = '{{ asset("img/close-circle.png") }}'; // Path to the close circle image

        // Loop through each div
        divs.forEach(function(div, index) {
            // Get the data-src attribute value which contains the image URL
            var imageUrl = div.getAttribute('data-src');

            var img = new Image();
            img.onload = function() {
                // If image exists, set it as background
                div.style.backgroundImage = 'url(' + imageUrl + ')';
                // Set the corresponding image in the second column
                updateSecondColumnImage(index, true);
            };
            img.onerror = function() {
                // If image doesn't exist, set default image as background
                div.style.backgroundImage = 'url(' + defaultImg + ')';
                // Update the second column image to close circle if default image is used
                updateSecondColumnImage(index, false);
                // Update popover message for invalid or missing URLs
                updatePopoverContent(index, false);
            };
            img.src = imageUrl; // Attempt to load the image
        });
    }

    function updateSecondColumnImage(index, hasValidImage) {
        var secondColumnImages = document.querySelectorAll('.media-image');
        if (secondColumnImages[index]) {
            secondColumnImages[index].src = hasValidImage ? secondColumnImages[index].src : '{{ asset("img/close-circle.png") }}';
        }
    }

    function updatePopoverContent(index, hasValidImage) {
        var popovers = document.querySelectorAll('.media-section-popover');
        if (popovers[index]) {
            var message = hasValidImage ? 'Image is valid' : 'Image not found.<br> The URL is invalid or missing.';
            popovers[index].setAttribute('data-bs-content', message);
        }
    }

    // Call the function once the document is loaded
    document.addEventListener('DOMContentLoaded', function() {
        checkAndSetBackgroundImages();
    });
</script>
@endpush
