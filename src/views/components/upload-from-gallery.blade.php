<!-- The Modal -->
@php
$array_id = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
@endphp
@push("header_scripts")
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/imagehover.css/2.0.0/css/imagehover.min.css">
<style>
.pagination nav{
    width:100%;
    overflow:auto
}
.media_modal_width{margin:0 auto;}
    </style>
@endpush
<div class="modal fade " id="upload_from_gallery" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered media_modal_width">
        <div class="modal-content ">
            <div class="modal-header">
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel" style="font-size: 20px">
                    Select From Gallery
                </h5>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body d-flex flex-column" style="position: relative;margin-bottom: 44px; ">

                <div class="d-flex  mb-3">


                    <div style="width:28%" class="d-flex">
                        <div style="width: 100%;"> 
                          <!-- <div class="input-group mb-1 input-width ">
                               <input type="text" class="form-control" value="{{request()->has('q') ? request()->get('q') : ''}}" name="q"
                                     placeholder="Search"
                                      aria-label="{{($placeholder ?? '')}}" aria-describedby="search">
                                <div class="input-group-append">
                                   <button class="btn btn-dark search" type="submit" id="search">
                                       <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">
                                   </button>
                              </div>
                          </div> -->
                             <div style="height: 609px; overflow-y: scroll"> 
                            <div class="All-images p-2">
                                 <a id="backToGallery" class="p-2"  style="text-decoration:none; color:black;"><i class="icon fa-regular fa-images" ></i> All Folders</a> 
                                </div> 
                                 <h2 class="mt-4" style="font-weight: 500; margin-bottom: 5px; font-family: Poppins, sans-serif"> Folders: </h2>
                                <div class="firstTree" id="firstTree"></div> 
                             </div> 
                         </div>
                        <div>
                            <div class="vl"></div>
                        </div>
                    </div> 
                    <div style="width: 100%">
                        <div class="d-flex justify-content-between " style="margin-left: 11px">
                            <div class="checkbox-wrapper-46 mb-2" id="select_all">
                                <input class="inp-cbx" id="cbx-46" type="checkbox"/>
                                <label class="cbx" for="cbx-46">
                                    <span>
                                    <svg width="12px" height="10px" viewbox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Select all</span>
                                </label>
                            </div>
                            <div>
                            <span>
                                Total Selected:
                            </span>
                                <span id="product_counter">
                                0
                            </span>
                            </div>
                        </div>
                        <div class="image_gallery" >
                            @foreach($files as $file)
                                <label class="label_css" for="mycheck-{{$file->id}}" style="position: relative; overflow: hidden;  max-height: 198.5px;">
                                    <div class="checkbox-wrapper-31  ">
                                        <input id="mycheck-{{$file->id}}" type="checkbox"/>
                                        <svg viewBox="0 0 35.6 35.6">
                                            <circle class="background" cx="17.8" cy="17.8" r="17.8"></circle>
                                            <circle class="stroke" cx="17.8" cy="17.8" r="14.37"></circle>
                                            <polyline class="check" points="11.78 18.12 15.55 22.23 25.17 12.87"></polyline>
                                        </svg>
                                    </div>

                                    <img src="{{$file->link}}" class="mb-1 rounded img-modal img_hover ">
                                    <div class="img_caption rounded-bottom d-flex justify-content-between">
                                        <div class="d-flex flex-column">
                                            <span class=" rounded-bottom" style="line-height: 1.2; ">{{isset(explode('.', $file->name)[2]) ? (strlen(explode('.', $file->name)[2]) < 4 ? explode('.', $file->name)[1] : explode('.', $file->name)[2]) : explode('.', $file->name)[0] }}</span>
                                            @if(count($file->folders) > 0)
                                                <small class="">{{$file->folders[0]->name}}</small>
                                            @endif
                                        </div>
                                        <div>
                                            <span class="">{{$file->height}}x{{$file->width}}</span>
                                        </div>
                                    </div>
                                </label>
                            @endforeach

                        </div>
                        <div>
                            <br>
                        </div>
                        <div class="pagination pagination-gallery">
                            {{ $files->links() }}
                        </div>
                        <div class="pagination pagination-folder d-none">
                            
                        </div>
                    </div>
                </div>

                <div class="modal-footer"
                     style="position: absolute; width: 100%; bottom: -41px; left: 0; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; padding-top: 18px; border-top: 1px solid #8C8C8C;">
                    <button  class="btn btn btn-dark-tertiary shadow modal_close" style="width: 120px" data-bs-dismiss="modal" aria-label="Close"> Cancel</button>
                    <button type="submit" class="btn btn-primary float-right ripplelink shadow" id="assign_products" style="width: 127px; float: right"> Assign
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
    .media_modal_width {
        max-width: 73%;
        min-width: 70%;
        min-height: 100%;
        margin-top: 0;
    }

    .vl {
        border-left: 1px solid #8C8C8C;
        height: 681px;
        margin-right: 17px;
        margin-left: 25px;
    }

    .img-modal {
        min-width: 100%;
        max-width: 100%;

        min-height: 198.5px;
        max-height: 198.5px;
        object-fit: cover;

    }

    .image_gallery {
        display: grid;
        grid-template: repeat(3, 205px) / repeat(4, auto);
        grid-gap: 10px;
        justify-content: center;
        grid-column: 2 / span 3;
        grid-row: 3 / span 1;
        max-height: 645px;
        overflow: auto;
    }

    .centering {
        display: flex;
        align-self: end;
    }
    .img_caption {
        font-size: .9em;
        position: absolute;
        display: block;
        width: 100%;
        bottom: 0;
        padding: 0.6rem 0.8rem;
        color: #fff;
        background: linear-gradient(to left, rgba(85, 67, 70, 0.65), rgba(69, 80, 91, 0.65)) !important;


        opacity: 1;
        cursor: pointer;
    }

    .img_caption_info {
        position: absolute;
        font-size: .9em;

    }

    .img_hover {
        transform: scale(1);
        transition: .3s ease-in-out;
        cursor: pointer;

    }

    .img_hover:hover {
        transform: scale(1.2);

    }

    .label_css {
        border-radius: 0.25rem !important;

    }

    .label_css:hover {
        border-radius: 0.25rem !important;
    }

    .img_hover:hover label {
        border-radius: 0.25rem !important;

    }

    .label_css {
        margin-bottom: 0;
        font-weight: 400;
    }

    {{--    Folder Tree CSS--}}
    .jsTree {
        width: 100%;
    }

    .jsTree .itemParent { /* div. more down under */
        transition: all 0.3s ease-in;
        padding: 6px 0px;
        display: flex;
    }

    .jsTree .itemParent:hover {
        background-color: rgb(209, 209, 209);
    }

    .jsTree .itemParent .contenteditable {
        margin: 0px;
        flex-grow: 1;
    }

    .jsTree .itemParent p {
        margin: 0px 6px;
        max-width: 300px;
        padding: 2px 0px;
    }

    .jsTree .itemParent .afterIcon {
        display: inline-block;
        flex-shrink: 0;
        width: 15px;
        height: 19px;
        margin: 0px 11px;
        margin-top: 5px;
        background-size: 12px 12px;
        background-repeat: no-repeat;
        background-position: center center;
        cursor: pointer;
        transition: opacity 0.3s ease-out;
        opacity: 1;
        background: url({{asset('img/gallery/icons/arrow-down.svg')}});

    }

    .jsTree .itemParent .afterIcon:hover .afterIcon {
        opacity: 1;
    }

    .jsTree .itemParent .afterIcon.arrowRotate {
        transform: rotate(-90deg);
    }

    .jsTree .childGroup { /* ul */
        padding: 0px 0px 0px 12px;
        margin: 0;
    }

    .jsTree .item { /* li */
        list-style: none;
        padding: 0;
        margin: 0;
        transition: all 0.3s ease-in;
    }

    .color {
        background: rgba(204, 204, 204, 0.8);
    }

    .jsTree .itemParent .preIcon {
        display: inline-block;
        flex-shrink: 0;
        width: 19px;
        height: 19px;
        margin: 0px 4px;
        background-size: 14px 14px !important;
        background-repeat: no-repeat !important;
        background-position: center center !important;
        font-size: 18px;
        margin-top: 4px
    }

    .jsTree .itemParent .preIcon.arrowDown {
        cursor: pointer;
        {{--background: url({{asset('img/tree-images/folder-solid.svg')}});--}}
           transition: transform 0.3s ease-out;
    }

    .jsTree .itemParent .preIcon.arrowDown.arrowRotate {
        transform: rotate(-90deg);
    }


    .jsTreeContextMenu {
        width: -webkit-max-content;
        width: -moz-max-content;
        width: max-content;
        display: none;
        position: fixed;
        border-radius: 1px;
        overflow: hidden;
        background: white;
        border: 1px solid rgb(16, 111, 171);
        box-sizing: border-box;
    }

    .jsTreeContextMenu p {
        margin: 0;
        padding: 4px 8px;
        transition: all 0.3s ease-in;
        background: white;
    }

    .jsTreeContextMenu p:hover {
        background: #eee;
    }

    /*# sourceMappingURL=tree_editor.css.map */
    .firstTree > * {
        font-family: Poppins, sans-serif;
    }
    .image_gallery {
        grid-template: repeat(3, auto) / repeat(4, auto) !important;
    }
    .jsTree .itemParent,
    .All-images{
    cursor: pointer;
    }


</style>
@push('footer_scripts')

            <script type="text/javascript">

                /**
                * Created by Francis Pogulis
                * <EMAIL>
                * Created for a simple, yet functional UI
                *
                * Obviosly improvements should be made
                * jQuery is required
                */
                class DinampTreeEditor {
                    mainNode;     //selector
                    uuid;
                    contextMenu;  //jQuery node
                    selectedItem; //jQuery node
                    options;      //{}

                    //TODO add max capacity and tags, stackabel=bool
                    constructor(mainNode) {

                        this.uuid = this.newuuid();
                        if (!$(mainNode).hasClass("jsTree"))
                            $(mainNode).addClass("jsTree");//make sure styles are accurate

                        $(mainNode).attr("ui-uuid", this.uuid);
                        this.mainNode = mainNode + "[ui-uuid='" + this.uuid + "']";

                        //TODO translte contextmenu items
                        this.contextMenu = $("<div class='jsTreeContextMenu' ui-uuid='" + this.uuid + "'><p>Delete</p></div>");//<p>Move up</p><p>Move down</p>
                        this.contextMenu.insertAfter($(this.mainNode));

                        //one-off listeners:

                        let jsTree = this;
                        $(document).on("mousedown", function (e) {

                            if (!$(e.target).hasClass("afterIcon") && !$(e.target).hasClass("jsTreeContextMenu") && !($(e.target).parents(".jsTreeContextMenu").length > 0)) {
                                jsTree.contextMenu.hide();
                            }
                        });

                        //initial options:
                        this.options = {
                            checkboxes: false,
                            radios: false,
                            editable: true
                        };
                        //this.rebindListeners();
                    }

                    setData(data) {
                        $(this.mainNode).empty();
                        data.forEach(element => this.addElement(element, $(this.mainNode)));
                        //TODO Optimize this line here
                        this.rebindListeners();
                        return this;
                    }

                    set(opts) {
                        let jsTree = this;
                        jsTree.options = opts;
                        if (opts.extended === false) {
                            $(this.mainNode + " .afterIcon").each(function () {
                                if ($(this).hasClass("arrowDown")) $(this).addClass("arrowRotate");
                            });
                            $(this.mainNode + " .childGroup").hide();
                        }
                        if (opts.checkboxes === true) {
                            $(this.mainNode + " .afterIcon").each(function () {
                                $(this).removeClass("arrowDown");
                                $(this).addClass("checkboxIcon");
                            });
                            jsTree.options.radios = false;
                        } else if (opts.radios === true) {
                            $(this.mainNode + " .afterIcon").each(function () {
                                if (!$(this).hasClass("arrowDown")) {
                                    $(this).addClass("radiobtnIcon");
                                }
                            });
                            jsTree.options.checkboxes = false;
                        } else {
                            jsTree.options.radios = false;
                            jsTree.options.checkboxes = false;
                        }

                        if (opts.editable === false) {
                            $(this.mainNode + " p").removeAttr("contenteditable");
                            $(this.mainNode + " .afterIcon").hide();
                        } else {
                            jsTree.options.editable = true;

                        }

                        this.rebindListeners();
                        return this;
                    }

                    getData() {
                        let jsTree = this;
                        folder_id = jsTree;
                        var retVal = [];
                        $(this.mainNode).subs().each(function () {
                            jsTree.pushData(retVal, jsTree, $(this));
                        });
                        return retVal;
                    }

                    pushData(parentData, jsTree, subject) {
                        if (subject.is("ul")) return;
                        if (subject.is(".itemParent")) {
                            let currentItem = {
                                title: subject.find("p").text()
                            };
                            if (subject.find(".afterIcon").hasClass("checked")) currentItem.checked = true;
                            if (subject.next().is("ul")) {
                                currentItem.subs = [];
                                $(subject.next()).subs().each(function () {
                                    jsTree.pushData(currentItem.subs, jsTree, $(this).find(".itemParent").eq(0));
                                });
                            }

                            parentData.push(currentItem);
                        }
                    }

                    addElement(el, parentNode = null, id = null) {
                        var $newNode;
                        $(".childGroup").hide();
                        if (parentNode.is("ul"))
                            $newNode = $(`<li class='item' id='${el.id}'><div class='itemParent' id='${el.id}'><i class="fa-regular fa-folder preIcon" style="color: #5B5B5B"></i>  </i><div class="contenteditable"><p contenteditable='false' style="font-family: Poppins, sans-serif; color: #5B5B5B" class="text-truncate" >${el.title}</p></div></span><span class='afterIcon' style="color: #5B5B5B"></span></div></li>`);
                        else
                            $newNode = $(`<div class='itemParent' id='${el.id}'> <i class="fa-regular fa-folder preIcon" style="color: #5B5B5B"></i> </span><div class="contenteditable"><p contenteditable='false' style="font-family: Poppins, sans-serif; color: #5B5B5B" class="text-truncate">${el.title} </p></div></span> <span class='afterIcon' style="color: #5B5B5B"></span></div>`);
                        //if(parentNode == null) parentNode = $(this.mainNode);
                        parentNode.append($newNode);
                        if (el.checked === true || el.checked === "true") $newNode.find(".afterIcon").addClass("checked");
                        if (el.subs !== undefined) {
                            $newNode.find(".afterIcon").addClass("arrowDown arrowRotate");
                            var $chContainer = $("<ul class='childGroup'></ul>");
                            if (parentNode.is("ul"))
                                $newNode.append($chContainer);
                            else
                                $(this.mainNode).append($chContainer);
                            el.subs.forEach(element => this.addElement(element, $chContainer));
                        }
                    }


        //EVENT LISTENERS BEGIN HERE
                    unbindListenders() {
                        $(this.mainNode + " p").off();
                        $(this.mainNode + " .preIcon").off();
                        $(this.mainNode + " .afterIcon").off();
                        $(".jsTreeContextMenu[ui-uuid='" + this.uuid + "'] p").off();
                    }

                    rebindListeners(jsTree = this) {
                        jsTree.unbindListenders();
                        $(this.mainNode + " p").keydown(function (e) {
                            if (e.keyCode == 13) {//code here is duplicate from below
                                jsTree.selectedItem = $(":focus").closest(".itemParent");
                                if (jsTree.selectedItem.parent().is("li")) {

                                } else if (jsTree.selectedItem.next().length > 0 && jsTree.selectedItem.next().is(".childGroup")) {
                                    $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='afterIconEdit'></span><span class='afterIcon'></span></div>");
                                    $newNode.insertAfter(jsTree.selectedItem.next());
                                } else {
                                    $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='afterIconEdit'></span><span class='afterIcon'></span></div>");
                                    $newNode.insertAfter(jsTree.selectedItem);
                                }
                                jsTree.rerender(jsTree);
                                return false;
                            }
                        });


                        $(this.mainNode + " p").on('blur', function () {
                            jsTree.options.onchange(jsTree);
                        });

                        $(this.mainNode + " .afterIcon").on('click', function () {
                            if ($(this).hasClass("arrowDown") && !$(this).hasClass("arrowRotate")) { //subs are expanded must retract
                                if ($(this).parent().parent().is("li"))
                                    $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                                else
                                    $(this).parent().next().animate({height: "toggle"}, 400);
                                $(this).addClass("arrowRotate");
                            } else if ($(this).hasClass("arrowDown") && $(this).hasClass("arrowRotate")) { //subs are retracted
                                if ($(this).parent().parent().is("li"))
                                    $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                                else
                                    $(this).parent().next().animate({height: "toggle"}, 400);
                                $(this).removeClass("arrowRotate");
                            } else if ($(this).hasClass("checkboxIcon")) {
                                if ($(this).hasClass("checked")) $(this).removeClass("checked");
                                else $(this).addClass("checked");
                            } else if ($(this).hasClass("radiobtnIcon")) {
                                $(jsTree.mainNode + " .afterIcon").removeClass("checked");
                                $(this).addClass("checked");
                            }

                            if ($(this).hasClass("checkboxIcon") || $(this).hasClass("radiobtnIcon")) {
                                if (jsTree.options.oncheck !== undefined) {
                                    let pathToDis = [];
                                    var curItem = $(this).parent();
                                    while (curItem.parent().is("li") || curItem.parent().is(jsTree.mainNode)) {
                                        pathToDis.unshift(curItem.find("p").text());
                                        curItem = curItem.parent().parent().prevAll().eq(0);
                                    }
                                    jsTree.options.oncheck($(this).hasClass("checked"), $(this).parent().find("p").text(), pathToDis);
                                }
                                if (jsTree.options.onchange !== undefined) {
                                    jsTree.options.onchange(jsTree);
                                }
                            }
                        });


                        var value
                        $(this.mainNode + " .preIcon").on('click', function () {
                            value = $(this).parents('.itemParent').attr('id')
                            $(`#${value}`).toggleClass('color');
                        });

                    }

                    rerender(jsTree = this) {
                        if (jsTree.options.checkboxes === true) {
                            $(jsTree.mainNode + " .afterIcon").each(function () {
                                if (!$(this).hasClass("arrowDown")) {
                                    $(this).addClass("checkboxIcon");
                                }
                            });
                            jsTree.options.radios = false;
                        } else if (jsTree.options.radios === true) {
                            $(jsTree.mainNode + " .afterIcon").each(function () {
                                if (!$(this).hasClass("arrowDown")) {
                                    $(this).addClass("radiobtnIcon");
                                }
                            });
                        } else {
                            $(jsTree.mainNode + " .itemParent").each(function () {//TODO optimize, when delete delay required, otherwise not
                                if ($(this).next().is("ul")) {
                                    if ($(this).next().subs().length > 0) {
                                        $(this).find(".afterIcon").eq(0).addClass("arrowDown");
                                        if ($(this).next().is(":visible")) {
                                            $(this).find(".afterIcon").eq(0).removeClass("arrowRotate");
                                        } else
                                            $(this).find(".afterIcon").eq(0).addClass("arrowRotate");
                                    } else
                                        $(this).find(".afterIcon").eq(0).removeClass("arrowDown");
                                } else
                                    $(this).find(".afterIcon").eq(0).removeClass("arrowDown");
                            });
                        }
                        if (jsTree.options.onchange !== undefined) {
                            jsTree.options.onchange(jsTree);
                        }

                        jsTree.rebindListeners(jsTree);
                    }

                    newuuid() {
                        return ([1e7] + -1e11).replace(/[018]/g, c =>
                            (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
                        );
                    }
                }
                let data = @json($folders_list);
                $(".firstTree").each(function( index ) {
                    const divIdAttribute=$('.firstTree')[index];
                    const divId= $(divIdAttribute).attr('id');

                    new DinampTreeEditor(`#${divId}`).setData(data);
                });
                let folderId;
                $('.contenteditable').on('click',function () {
                    const clickedFolder = $(this).parents('.itemParent');
                    const allFolders = $('.itemParent');

                    // Remove 'selectedFolder' class from other folders
                    allFolders.not(clickedFolder).removeClass('selectedFolder');

                    folderId = clickedFolder.attr('id');
                    clickedFolder.toggleClass('selectedFolder');

                    if(clickedFolder.hasClass('selectedFolder')){
                        $('#folderId').val(folderId);
                        load_data();
                    }
                });




                {{--Save selected prodcuts--}}
                let selectedIds = [];

                let labels = document.getElementsByClassName("label_css");
                let fileCounter = document.getElementById("product_counter");


                function updateCounter(value) {
                    fileCounter.innerHTML = parseInt(fileCounter.innerHTML) + value;
                }

                // let selectAllCheckbox = document.querySelector('.checkbox-wrapper-46 input[type="checkbox"]');
                // let selectAllCheckbox = document.querySelector('#select_all');
                let selectAllCheckbox = document.querySelector('.checkbox-wrapper-46 input[type="checkbox"]');
                let checkboxes = document.querySelectorAll('.checkbox-wrapper-31 input[type="checkbox"]');

                $(document).on("click", '.checkbox-wrapper-31 input[type="checkbox"]', function(event) {

            let id = this.id.split("-")[1];
            if (selectedIds.includes(id)) {
                selectAllCheckbox.checked = false;
                selectedIds.splice(selectedIds.indexOf(id), 1);
                updateCounter(-1);
            } else {
                selectedIds.push(id);
                updateCounter(1);
            }
        });
                            function main_select_all()
                            {
                                        let id ;
                                        let all_files = <?php echo json_encode($files); ?>;
                                        if(selectAllCheckbox.checked) {
                                            updateCounter(-selectedIds.length);
                                            selectedIds = []

                                            for (let i = 0; i < $('.checkbox-wrapper-31 input[type="checkbox"]').length; i++)  {
                                                updateCounter(1);
                                                id = $('.checkbox-wrapper-31 input[type="checkbox"]')[i].id.split("-")[1];
                                            selectedIds.push(id);
                                                $('.checkbox-wrapper-31 input[type="checkbox"]')[i].checked = true;

                                            }

                                        }
                                        else {
                                            selectedIds = [];

                                            for (let i = 0; i < $('.checkbox-wrapper-31 input[type="checkbox"]').length; i++) {
                                                // console.log(checkboxes[i])
                                                $('.checkbox-wrapper-31 input[type="checkbox"]')[i].checked = false;
                                                updateCounter(-1);

                                            }
                                            document.getElementById("product_counter").innerHTML = "0";

                                        }
                                    }
        $(document).on("click", '#cbx-46', main_select_all);
        //$(document).on("click", selectAllCheckbox, main_select_all);
    $(document).on('click', '.pagination a.folder-images', function (event) {
        event.preventDefault();
        let page = $(this).attr('href').split('page=')[1];
        load_data(page);
    });

        $(document).on('click', '#backToGallery', function (event) {
                $(".pagination-folder").addClass('d-none');
                $(".pagination-gallery").removeClass('d-none');
            });


                $('.close_modal').click(function() {
                    fileCounter.innerHTML = "0"

                    for (let i = 0; i < checkboxes.length; i++) {
                        checkboxes[i].checked = false;
                    }
                    selectedIds = [];
                    selectAllCheckbox.checked = false;
                });

                $('.modal_close').click(function() {
                    for (let i = 0; i < checkboxes.length; i++) {
                        checkboxes[i].checked = false;
                    }
                    selectedIds = [];
                    fileCounter.innerHTML = "0"
                    selectAllCheckbox.checked = false;
                    $("#upload_from_gallery").modal('hide')
                });
                window.addEventListener("load", function() {
                    selectAllCheckbox.checked = false;
                    fileCounter.innerHTML = "0"
                    for (let i = 0; i < checkboxes.length; i++) {
                        checkboxes[i].checked = false;
                    }
                });
                let xhr = null;
                let total_files;
                let select;
                let $pagenumber;
                function load_data($pagenumber) {
                    // if (xhr && xhr.readyState != 4) {
                    //     xhr.abort();
                    // }
                    if($pagenumber == ''){
                        $pagenumber = 1;
                    }
                    xhr = $.ajax({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        url: "{{ url('images/folder/') }}/"+folderId+ "?page=" +$pagenumber,
                                                type: "GET",
                        data: {
                            file_id: selectedIds,
                        },
                        success: function(response) {
                            let gallery = document.querySelector('.image_gallery');
                            let pagination = document.querySelector('.pagination-folder');
                            let html = '';
                            data = JSON.parse(response)

                            total_files = data.data;

                            // let select = document.querySelector(`#cbx-46`)

                            $('#clearFilter').show();
                            let paginationHtml = '';

                if (data.prev_page_url) {
                    paginationHtml += `
                        <li class="page-item">
                            <a class="page-link folder-images" href="${response.prev_page_url}" aria-label="Previous">
                                &laquo; Previous
                            </a>
                        </li>
                    `;
                }

                for (let i = 1 ; i <= data.last_page; i++) {
                    paginationHtml += `
                        <li class="page-item ${i === data.current_page ? 'active' : ''}">
                            <a class="page-link folder-images" href="${data.path}?page=${i}">${i}</a>
                        </li>
                    `;
                }

                if (data.next_page_url) {
                    paginationHtml += `
                        <li class="page-item">
                            <a class="page-link folder-images" href="${data.next_page_url}" aria-label="Next">
                                Next &raquo;
                            </a>
                        </li>
                    `;
                }
    console.log(paginationHtml)
                pagination.innerHTML = paginationHtml;

                $('#clearFilter').show();
                            selectedIds = []
                            if(total_files.length > 0) {
                                for (let i = 0; i < total_files.length; i++) {
                                    let file = total_files[i];
                                    html += `
            <label class="label_css" for="mycheck-${file.id}" style="position: relative; overflow: hidden; max-height: 198.5px;">
                <div class="checkbox-wrapper-31">
                <input id="mycheck-${file.id}" type="checkbox"/>
                <svg viewBox="0 0 35.6 35.6">
                    <circle class="background" cx="17.8" cy="17.8" r="17.8"></circle>
                    <circle class="stroke" cx="17.8" cy="17.8" r="14.37"></circle>
                    <polyline class="check" points="11.78 18.12 15.55 22.23 25.17 12.87"></polyline>
                </svg>
                </div>
                <img src="${file.link}" class="mb-1 rounded img-modal img_hover ">
                <div class="img_caption rounded-bottom d-flex justify-content-between">
                <div class="d-flex flex-column">
                    <span class=" rounded-bottom" style="line-height: 1.2;">Some Text Here</span>
                    <small>${data.name}</small>
                </div>
                <div>
                    <span>${file.height}x${file.width}</span>
                </div>
                </div>
            </label>
            `;
                                    gallery.innerHTML = html;
                                }
                            }
                            else {
                                html += `
            <span class="centering">No Files Found</span>
            `;
                                gallery.innerHTML = html;

                            }
                            fileCounter.textContent = `0`;
                            checkboxes = document.querySelectorAll('.checkbox-wrapper-31 input[type="checkbox"]');
                            //selectAllCheckbox.removeEventListener("click", main_select_all);


                            for (let i = 0; i < labels.length; i++) {
                                labels[i].addEventListener("click", function(event) {
                                    // event.stopPropagation();
                                    if (event.target.tagName === "INPUT") {

                                        let id = total_files[i].id
                                        if (selectedIds.includes(id)) {
                                            select.checked = false;
                                            selectedIds.splice(selectedIds.indexOf(id), 1);
                                            updateCounter(-1);
                                        } else {
                                            selectedIds.push(id);

                                            updateCounter(1);
                                        }
                                    }
                                });
                            }
                            // $('.inp-cbx').attr('id', `cbx-46-${folderId}`,);
                            // $('.inp-cbx').addClass(`cbx-46-${folderId}`);
                            select = document.querySelector(`#cbx-46`)


                            $(`#cbx-46`).on('click',function (param) {
                                response_select_all();
                            })


                            function response_select_all() {
                                let id;
                                total_files = data.files;
                                selectedIds = []
                                if(select.checked) {
                                    fileCounter.textContent = `0`;
                                    fileCounter.textContent = `${total_files.length}`;
                                    for (let i = 0; i < total_files.length; i++) {
                                        id = total_files[i].id;
                                        selectedIds.push(id);
                                        checkboxes[i].checked = true;
                                    }

                                }
                                else {
                                    selectedIds = [];
                                    fileCounter.textContent = `0`;
                                    for (let i = 0; i < total_files.length; i++) {
                                        checkboxes[i].checked = false;

                                    }

                                }
                            }
                            xhr = null;
                        },
                        error: function(xhr, status, error) {
                            // Handle error response
                            // ...

                            // Clear reference to xhr object so it can be garbage collected
                            xhr = null;
                        },
                        // complete: function() {
                        //     // Use variables after exiting AJAX call
                        //   selectedIds =[]
                        //     total_files = null
                        // }
                    });

                }

                function clear_filter() {

                    let all_files = <?php echo json_encode($files); ?>;

                    select = null;
                    let html = '';
                    $('#clearFilter').hide();

                    let gallery = document.querySelector('.image_gallery');
                    for (let i = 0; i < all_files.length; i++) {
                        let file = all_files[i];
                        html += `
            <label class="label_css" for="mycheck-${file.id}" style="position: relative; overflow: hidden; max-height: 198.5px;">
                <div class="checkbox-wrapper-31">
                <input id="mycheck-${file.id}" type="checkbox"/>
                <svg viewBox="0 0 35.6 35.6">
                    <circle class="background" cx="17.8" cy="17.8" r="17.8"></circle>
                    <circle class="stroke" cx="17.8" cy="17.8" r="14.37"></circle>
                    <polyline class="check" points="11.78 18.12 15.55 22.23 25.17 12.87"></polyline>
                </svg>
                </div>
                <img src="${file.link}" class="mb-1 rounded img-modal img_hover ">
                <div class="img_caption rounded-bottom d-flex justify-content-between">
                <div class="d-flex flex-column">
                    <span class=" rounded-bottom" style="line-height: 1.2;">Some Text Here</span>
                    <small>${data.name}</small>
                </div>
                <div>
                    <span>${file.height}x${file.width}</span>
                </div>
                </div>
            </label>
            `;
                        gallery.innerHTML = html;
                    }

                    checkboxes = document.querySelectorAll('.checkbox-wrapper-31 input[type="checkbox"]');
                    labels = document.getElementsByClassName("label_css");
                    for (let i = 0; i < labels.length; i++) {
                        labels[i].addEventListener("click", function(event) {
                            // event.stopPropagation();
                            if (event.target.tagName === "INPUT") {
                                let id = event.currentTarget.getAttribute("for").split("-")[1];
                                if (selectedIds.includes(id)) {
                                    selectAllCheckbox.checked = false;
                                    selectedIds.splice(selectedIds.indexOf(id), 1);
                                    updateCounter(-1);

                                } else {
                                    selectedIds.push(id);

                                    updateCounter(1);
                                }
                            }
                        });
                    }
                    //selectAllCheckbox.addEventListener("click", main_select_all);


                }

                $('#assign_products').click(function() {
                    $.ajax({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        url: "{{ url('product/files/attach') }}",
                        type: "POST",
                        data: {
                            file_ids: selectedIds,
                            product_id: {{$product->id}}
                        },
                        success: function(response) {
                            location.reload()

                        }
                    });
                });

        let initialData = []; // Global variable to store initial data

        function captureInitialData() {
            // Assuming each image data is stored within label elements under a certain class
            $('.label_css').each(function() {
                let id = $(this).find('input[type="checkbox"]').attr('id').split('-')[1];
                let src = $(this).find('img').attr('src');
                let name = $(this).find('.img_caption .d-flex flex-column span').text();
                let dimensions = $(this).find('.img_caption div span').text().split('x');
                initialData.push({
                    id: id,
                    src: src,
                    name: name,
                    width: dimensions[0],
                    height: dimensions[1]
                });
            });
        }

        // Call this function right after the modal is populated with initial gallery data
        captureInitialData();

        function resetGalleryView() {
            let gallery = $('.image_gallery');
            gallery.empty(); // Clear existing gallery items

            let html = '';
            initialData.forEach(file => {
                html += `<label class="label_css" for="mycheck-${file.id}" style="position: relative; overflow: hidden; max-height: 198.5px;">
                            <div class="checkbox-wrapper-31">
                                <input id="mycheck-${file.id}" type="checkbox"/>
                                <svg viewBox="0 0 35.6 35.6">
                                    <circle class="background" cx="17.8" cy="17.8" r="17.8"></circle>
                                    <circle class="stroke" cx="17.8" cy="17.8" r="14.37"></circle>
                                    <polyline class="check" points="11.78 18.12 15.55 22.23 25.17 12.87"></polyline>
                                </svg>
                            </div>
                            <img src="${file.src}" class="mb-1 rounded img-modal img_hover ">
                            <div class="img_caption rounded-bottom d-flex justify-content-between">
                                <div class="d-flex flex-column">
                                    <span class="rounded-bottom" style="line-height: 1.2;">${file.name}</span>
                                </div>
                                <div>
                                    <span>${file.width}x${file.height}</span>
                                </div>
                            </div>
                        </label>`;
            });
            gallery.html(html);
        }
        $(document).ready(function() {
            $('.All-images').addClass('selectedFolder');
            $('.firstTree .itemParent').on('click', function() {
                // Remove 'selectedFolder' class from the 'Back to Gallery' button
                $('.All-images').removeClass('selectedFolder');
                // Optionally, manage other UI updates or state changes here
                // e.g., updating which folder is visually marked as selected
                $('.firstTree .itemParent.selectedFolder').removeClass('selectedFolder');
                $(this).addClass('selectedFolder');
                $(".pagination-folder").removeClass('d-none');
                $(".pagination-gallery").addClass('d-none');
        })
        $(document).on('click', '.pagination a', function(e) {
                e.preventDefault();
                var url = $(this).attr('href');
                loadImages(url);
            });
            function loadImages(url) {
                $.ajax({
                    url: url,
                    type: 'get',
                    dataType: 'html',
                    success: function(response) {
                        // Replace the current content with the new one from the response
                        $('#upload_from_gallery .image_gallery').html($(response).find('.image_gallery').html());
                        $('#upload_from_gallery .pagination').html($(response).find('.pagination').html());
                    },
                    error: function(xhr, status, error) {
                        console.error("An error occurred while loading images: ", error);
                    }
                });
            }
            $('.modal_close, .close_modal').click(function() {
            });
        })

        $('.All-images').on('click', function() {
        $(this).addClass('selectedFolder');
                // Optional: Reset any specific folder selections in the UI
                $('.firstTree .itemParent.selectedFolder').removeClass('selectedFolder');
            resetGalleryView();
            let checkboxes = document.querySelectorAll('.checkbox-wrapper-31 input[type="checkbox"]');
        });
            </script>
@endpush
