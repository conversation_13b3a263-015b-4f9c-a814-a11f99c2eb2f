<style>

    .modal .modal-aside{
        width: 346px;
        max-width:80%; height: 100%; margin:0;
        transform: translate(0); transition: transform .2s;
    }


    .modal .modal-aside .modal_content{  height: inherit; border:0; border-radius: 0;}
    .modal .modal-aside .modal_content .modal_body{ overflow-y: auto }
    .modal.fixed-left .modal-aside{ margin-left:auto;  transform: translateX(100%); }
    .modal.fixed-right .modal-aside{ margin-right:auto; transform: translateX(-100%); }

    .modal.show .modal-aside{ transform: translateX(0);  }

</style>



<div id="modal_aside_left" class="modal fixed-left fade sidebar-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-aside modal-aside modal_dialog" role="document">
        <div class="modal-content modal_content" style="height: 100vh;">
            <div class="modal-header modal_header" style="background-color: white">
                <h2 class="modal-title shared-details-typography-head img_name text-truncate"  data-toggle="tooltip" data-placement="top" title="" style="width: 290px">B&B Italia Sec.jpg</h2>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>            </div>
            <div class="modal-body modal_body">
                <img class="card-img-top shared_img" src="https://i.imgur.com/tG3F3P2.png" onclick="open_side()" alt="Card image cap">
                <h2 class="mb-4 shared-details-typography-head mt-3">Image Properties</h2>
                <div class="d-flex">
                <h3 class="shared-details-typography-title">Height</h3>
                <p class="shared-details-spacing shared-details-typography-para mt-1 shared_height fs-14" style="margin-left: 150px;">223px</p>
            </div>
                <div class="d-flex ">
                    <h3 class="shared-details-typography-title">Width</h3>
                    <p class="shared-details-spacing shared-details-typography-para  mt-1 shared_width  fs-14" style="margin-left: 155px;">1122px</p>
                </div>
                <div class="d-flex">
                    <h3 class="shared-details-typography-title">Size</h3>
                    <p class="shared-details-spacing shared-details-typography-para mt-1 shared_size  fs-14" style="margin-left: 166px;">223KB</p>
                </div>
                <div class="d-flex">
                    <h3 class="shared-details-typography-title">Type</h3>
                    <p class="shared-details-spacing shared-details-typography-para  mt-1 shared_type  fs-14 " style="margin-left: 164px;">JPG</p>
                </div>
                <div class="d-flex">
                    <h3 class="shared-details-typography-title">Folder</h3>
                    <p class="shared-details-spacing shared-details-typography-para mt-1 folder_name  fs-14" style="margin-left: 154px;">Furniture</p>
                </div>
                <div class="d-flex">
                    <h3 class="shared-details-typography-title pt-1">Last Modification</h3>
                    <p class=" shared-details-typography-para update_info mt-1 fs-14" style="margin-left: 74px;">22/02/2022</p>
                </div>
                <div class="d-flex flex-column mt-4 mb-3">
                    <h3 class="shared-details-typography-title">Permissions</h3>
                    <p class=" shared-details-typography-para" >Viewer can download</p>
                </div>

                <a type="button" class="btn btn-outline-dark shared_img_download" style="padding: 11px 15px;">Download Image</a>

            </div>
            <div class="modal-footer">

            </div>
        </div>
    </div> <!-- modal-dialog .// -->
</div> <!-- modal.// -->
