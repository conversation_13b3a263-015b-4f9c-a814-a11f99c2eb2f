<div class="col-6 col-md-2 col-lg-2 d-flex align-items-center">
    <div class="form-group w-100">
        <label for="formula">{{__("Formula")}}</label>
        <select name="nodes[data][{{$row_count}}][with_formula]" class="formula_field form-control bg-white-smoke" id="formula" >
            @foreach($formulas as $formula_key => $formula_val)
                @if(isset($row_node['with_formula']))
                    <option value="{{$formula_key}}" class="Poppins regular text-color" {{(trim($row_node['with_formula']) == trim($formula_key)) ? 'selected' : null}}>{{$formula_val}}</option>
                @else
                    <option value="{{$formula_key}}" class="Poppins regular text-color">{{$formula_val}}</option>
                @endif
            @endforeach
        </select>
    </div>
</div>

<div class="col-6 col-md-1 col-lg-1 d-flex align-items-center justify-content-center">
    <div class="d-flex align-items-center">
        <span type="button" class="mt-3">
            <i class="icon-arrow-right fs-20"></i>
        </span>
    </div>
</div>

