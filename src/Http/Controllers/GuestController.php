<?php

namespace Apimio\Gallery\Http\Controllers;


use Apimio\Gallery\Classes\SearchAsset;
use Apimio\Gallery\Models\File;
use App\Http\Controllers\Controller;
use Apimio\Gallery\Models\Folder;
use App\Models\Organization\Organization;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class GuestController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }


    /**
     * @param $enc_id
     * @param Request $request
     * @return View
     */
    public function show($enc_id, Request $request)
    {
        try {
            $id = decrypt($enc_id);
            $files = File::withoutGlobalScopes()->whereHas('folders',function($query) use ($id) {
                $query->withoutGlobalScopes()->where('folders.id',$id);
            })->orderByDesc('id')->paginate(12);

            $folder  = Folder::withoutGlobalScopes()->findorFail($id);
            $folder_files = $folder->setRelation('files',$folder->files()->withoutGlobalScopes()->paginate(12));
            $org_name = Organization::withoutGlobalScopes()->where('id', $folder->organization_id)->first()->name;
            $list = (new SearchAsset)->SearchFolder($request,$id);

            return view("gallery::share", compact('list',  'id', 'org_name', 'folder', 'files','folder_files'));
        }
        catch(Exception $e){
            return redirect()->route("gallery.index")->withErrors($e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param  int  $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}
