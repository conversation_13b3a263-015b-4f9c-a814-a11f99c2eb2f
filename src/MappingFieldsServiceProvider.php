<?php

namespace Apimio\MappingConnectorPackage;

use Apimio\MappingConnectorPackage\components;

use Illuminate\Support\ServiceProvider;

class MappingFieldsServiceProvider extends ServiceProvider
{
    public function boot(){
        $this->loadRoutesFrom(__DIR__.'/routes/web.php');
        $this->loadViewsFrom(__DIR__ . '/views','mapping');
        $this->loadMigrationsFrom(__DIR__.'/database/migrations');
        $this->loadViewComponentsAs('component', [
            components\FromField::class,
            components\Vlookup::class,
            components\MappingPopup::class,
            components\MappingAddAttribute::class,
            components\alerts\TemplateDelete::class
        ]);

//        Blade::component('mapping::components.vlookup', 'vlookup');

        $this->publishes([
            __DIR__.'/assets/' => public_path('/mapping-fields/'),
        ], 'public');
    }

    public function register()
    {

    }

}
