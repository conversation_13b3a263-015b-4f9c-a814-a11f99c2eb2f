<?php

namespace Apimio\MappingConnectorPackage\rules;

use Illuminate\Contracts\Validation\Rule;

class UniqueManyToMany implements Rule
{

    private $table, $attribute;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($table, $attribute = array())
    {
        $this->table = $table;
        $this->attribute = $attribute;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $obj = $this->table->where($attribute, $value);


        if(isset($this->attribute["organization_id"])) {
            $obj = $obj->withoutGlobalScope("organization_id");
        }
        if(isset($this->attribute["user_id"])) {
            $obj = $obj->withoutGlobalScope("user_id");
        }

        // TODO: organization id has a wrong where clause it needs to be refactored.
        // now to need to apply different value but according to organization.

        foreach ($this->attribute as $attribute_key => $attribute_value) {
            if($attribute_key == "id") {
                $obj = $obj->where($attribute_key, "!=", $attribute_value);
            }
            else {
                $obj = $obj->where($attribute_key, $attribute_value);
            }
        }

        if($obj->count() > 0) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute must be unique.';
    }
}

