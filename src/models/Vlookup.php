<?php

namespace Apimio\MappingConnectorPackage\models;

use Apimio\MappingConnectorPackage\rules\UniqueManyToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Vlookup extends Model
{
    private $data;



    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });

    }



    public function set_data($data)
    {
        if (isset($data['_token']))
            unset($data['_token']);

        $this->data = $data;
        return $this;
    }

    public function rules()
    {
        $attributes = [];

        if(isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if(isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255', new UniqueManyToMany(new Vlookup(), $attributes)],
            'values' => 'required',
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        return $validator;
    }



    public function store($error_callback, $success_callback)
    {
        if ($this->data['submit_type'] == "add"){
            $validation = $this->validation();
            if (isset($validation)){
                if ($validation->fails()) {
                    return $error_callback($validation->errors());
                }
            }
        }
        elseif($this->data['submit_type'] == "edit"){
            if (empty($this->data['id'])){
                $error = [
                    'name' => [
                        "The name field is required."
                    ]
                ];
                return $error_callback($error);
            }
        }
        if (isset($this->data['id'])) {
            $vlookup = $this->find($this->data['id']);
        }else{
            $vlookup = $this;
        }

//        $explode_vlookups = explode("\r\n",$this->data['values']);
//        $vlookup_array = array();
//        if (count($explode_vlookups) > 1){
//            $explode_vlookups = array_filter($explode_vlookups);
//            foreach ($explode_vlookups as $vl){
//                $vlookup_array[explode(",", $vl)[0]] = explode(",", $vl)[1];
//            }
//        }
//        else{
//            $error = [
//                'values' => [
//                    "Please enter values in proper format."
//                ]
//            ];
//            return $error_callback($error);
//        }

        $lines = explode("\r\n", $this->data['values']);
        $vlookup_array = array();
        $errors = [];

// Check if there's at least one non-empty line
        if (count($lines) > 0) {
            foreach ($lines as $line) {
                // Split the line by comma
                $parts = explode(",", $line);

                // Check if exactly two parts are present
                if (count($parts) === 2) {
                    // Trim to remove possible whitespace around keys/values
                    $key = trim($parts[0]);
                    $value = trim($parts[1]);

                    // Additional validation could go here (e.g., checking if keys/values are non-empty)
                    $vlookup_array[$key] = $value;
                } else {
                    // Add an error message if the line doesn't have exactly two parts
                    $errors[] = "Invalid format detected. Each line must contain exactly one key and one value, separated by a comma.";
                    break; // Remove this if you want to process all lines regardless of errors
                }
            }
        } else {
            $errors[] = "Please enter at least one key,value vlookup pair.";
        }

        // Check if there were any errors
        if (!empty($errors)) {
            $error = ['values' => $errors];
            return $error_callback($error);
        }


        $payload = json_encode($vlookup_array);
        if (isset($this->data['organization_id']))
        {
            $vlookup->organization_id = $this->data['organization_id'];
        }
        $vlookup->name = $this->data['name'];
        $vlookup->payload = $payload;

        $vlookup->save();
        return $success_callback($vlookup);
    }
}
