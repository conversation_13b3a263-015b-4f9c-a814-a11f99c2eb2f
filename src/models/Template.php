<?php

namespace Apimio\MappingConnectorPackage\models;

use App\Models\Channel\Channel;
use App\Models\Product\Attribute;
use App\Models\Product\Family;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class Template extends Model
{
    private $data;
    protected $guarded = [];

    protected $casts = [
        'version_id' => 'json',
        'channel_id' => 'json',
    ];


    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });

    }


    /**
     * Set data templates.
     *
     *
     * @param array
     *
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }


    /**
     * Set organization id.
     *
     *
     * @param int
     *
     */
    public function set_organization(int $organization_id)
    {
        $this->organization_id = $organization_id;
        return $this;
    }


    /**
     * method for creating new template record
     *
     * @param callable $error_callback if we found any error
     * @param callable $success_callback return success callback
     *
     *
     * @return  object $success_callback with returned created template object
     */
    public function store($success_callback, $error_callback)
    {
        try {
            if (isset($this->data['temp_id'])) {
                $template = $this->find($this->data['temp_id']);

            } else {
                $template = $this;
            }

            if (isset($this->data['organization_id'])) {
                $template->organization_id = $this->data['organization_id'];
            }

            $template->version_id = $this->data['version'];
            $template->channel_id = $this->data['catalog'];
            $template->name = $this->data['temp_name'];
            $template->payload = $this->data['payload'] ?? json_encode($this->data['nodes']);
            $template->type = $this->data['template_method_type'] ?? "other";
            $template->export_type = $this->data['export_type'] ?? null;
            $template->product_status = $this->data['status'] ?? false;

            $template->save();

            return $success_callback($template);
        } catch (\Exception $e) {
            return $error_callback("Something went wrong.! Please try again later");
        }
    }


    /**
     * method for fetching shopify fields
     *
     */
    function shopify_mapping_array()
    {
        $custom_array = [
            'array_name' => "Shopify",
            'nodes' => [
                [

                    'name' => 'Shopify',
                    'attributes' => [
                        'handle' => 'Handle',
                        'title' => 'Title',
                        'body_html' => 'Body (HTML)',
                        'sku' => 'SKU',
                        'variant_grams' => 'Variant Grams',
                        'tags' => 'Tags',
                        'variant_compare_at_price' => 'Variant Compare At Price',
                        'variant_price' => 'Variant Price',
                        'image_src' => 'Image Src',
                        'variant_barcode' => 'Variant Barcode',
                        'seo_title' => 'SEO Title',
                        'vendor' => 'Vendor',
                        'seo_description' => 'SEO Description'
                    ]
                ],
                [

                    'name' => 'Custom',
                    'attributes' => [
                        'handle' => 'Handle',
                        'title' => 'Title',
                        'body_html' => 'Body (HTML)',
                        'variant_sku' => 'Variant SKU',
                        'variant_grams' => 'Variant Grams',
                        'tags' => 'Tags',
                        'variant_compare_at_price' => 'Variant Compare At Price',
                        'variant_price' => 'Variant Price',
                        'image_src' => 'Image Src',
                        'variant_barcode' => 'Variant Barcode',
                        'seo_title' => 'SEO Title',
                        'vendor' => 'Vendor',
                        'seo_description' => 'SEO Description'
                    ]
                ],
                [

                    'name' => 'Variants',
                    'attributes' => [
                        "id" => 'id',
                        "product_id" => 'product_id',
                        "title" => "title",
                        "price" => "price",
                        "sku" => "sku",
                        "position" => 'position',
                        "inventory_policy" => "inventory_policy",
                        "compare_at_price" => 'compare_at_price',
                        "fulfillment_service" => "fulfillment_service",
                        "inventory_management" => 'inventory_management',
                        "option1" => "option1",
                        "option2" => 'option2',
                        "option3" => 'option3',
                        "created_at" => 'created_at',
                        "updated_at" => 'updated_at',
                        "taxable" => 'taxable',
                        "barcode" => 'barcode',
                        "grams" => 'grams',
                        "image_id" => 'image_id',
                        "weight" => 'weight',
                        "weight_unit" => "weight_unit",
                        "inventory_item_id" => 'inventory_item_id',
                        "inventory_quantity" => 'inventory_quantity',
                        "old_inventory_quantity" => 'old_inventory_quantity',
                        "requires_shipping" => 'requires_shipping',
                        "admin_graphql_api_id" => "admin_graphql_api_id"

                    ]
                ]
            ]
        ];
        return $custom_array;
    }

    /**
     * method for fetching magento fields
     *
     */
    function magento_mapping_array()
    {
        $custom_array = [
            'array_name' => "Magento",
            'nodes' => [
                [
                    'name' => 'General',
                    'attributes' => [
                        'sku' => 'SKU',
                        'description' => 'description',
                        'price' => 'price',
                        'name' => 'name',
                        'url_key' => 'url_key',
                        'weight' => 'weight',
                    ]
                ]
            ]
        ];
        return $custom_array;
    }


    /**
     * method for fetching apimio fields
     *
     */
    public static function apimio_mapping_array($not_fetch_able_attributes = [], $withVarientOpt = true)
    {

        $custom_array['array_name'] = 'Apimio';

        $mapping_default_attributes = [
            'handle' => 'Product Identifier',
            'file' => 'Product Images',
            'vendor' => 'Vendor',
            'brand' => 'Brand',
            'categories' => 'Category',
        ];

        // this check is only for export and shopify mapping
        if (!$withVarientOpt) {
            $mapping_default_attributes['status'] = "Product Status";
        }


        $apimio_attributes = [
            'name' => 'Default',
            'attributes' => $mapping_default_attributes
        ];

        $variant_attributes = [
            'name' => 'Variant',
            'attributes' => [
                'sku' => 'SKU',
                'name' => 'Name',
                'file' => 'Image',
                'price' => 'Price',
                'compare_at_price' => 'Compare at Price',
                'cost_price' => 'Cost Price',
                'barcode' => 'UPC / Barcode',
                'weight' => 'Weight',
                'weight_unit' => 'Weight Unit',
                'track_quantity' => 'Track Quantity',
                'continue_selling' => 'Continue Selling',
            ]

        ];
        $variant_option_attributes = [
            'name' => 'Variant Option',
            'attributes' => [
                'option1_name' => 'Option 1 Name',
                'option1_value' => 'Option 1 Value',
                'option2_name' => 'Option 2 Name',
                'option2_value' => 'Option 2 Value',
                'option3_name' => 'Option 3 Name',
                'option3_value' => 'Option 3 Value'
            ]

        ];


        //just for import
        $inventory_with_store_name = [];
        $catalogs = Channel::all();
        foreach ($catalogs as $catalog) {
            $temp_store_with_location = [];
            $temp_store_with_location['name'] = "Inventory -> ".$catalog->name;
            $locations = $catalog->locations;
            foreach ($locations as $location) {
                $temp_store_with_location['attributes'][$location->id] = $location->name;
            }
            $inventory_with_store_name[] = $temp_store_with_location;
        }


        $custom_array['nodes'] = [
            $apimio_attributes
        ];

        $families = Family::with(['attributes'])->get();

        foreach ($families as $family) {
            $family_with_attributes = array();
            $family_with_attributes['name'] = $family->name;
            if ($family->has('attributes')) {
                foreach ($family->attributes as $attribute) {
                    if (isset($attribute->attribute_type_id) && in_array($attribute->attribute_type_id, array_keys($not_fetch_able_attributes))) {
                        continue;
                    }

                    $family_with_attributes['attributes'][$attribute->handle] = $attribute->name;
                }
            }
            $custom_array['nodes'][] = $family_with_attributes;
            if ($family->name == "General") {
                // variant attributes
                $custom_array['nodes'][] = $variant_attributes;

                //just for import
                if ($withVarientOpt) {
                    $custom_array['nodes'][] = $variant_option_attributes;
                }
                if (!empty($inventory_with_store_name)) {
                    $custom_array['nodes'] = array_merge($custom_array['nodes'], $inventory_with_store_name);
                }
            }

            unset($family_with_attributes);
        }
        return $custom_array;

    }


    /**
     * method for default import csv template for all user
     *
     * @param array $request_data data contain (version,channel) object and organization id
     * @param callable $error_callback if we found any error
     * @param callable $success_callback return success callback with success array
     *
     *
     * @return  array $success_callback with return success object
     */
    static function default_template(array $request_data, callable $error_callback, callable $success_callback)
    {
        $organization_id = $request_data['organization_id'] ?? null;
        if (isset($organization_id)) {
            $version_keys = array_keys($request_data['versions']);
            $channel_keys = array_keys($request_data['catalogs']);
            $version = $version_keys[0] ?? null;
            $channel = $channel_keys[0] ?? null;
        } else {
            return $error_callback("Please provide organization id");
        }
        if (!isset($version) && !isset($channel)) {
            return $error_callback("Catalog and versions are not found");
        }

        $array = [
            'organization_id' => $organization_id,
            'version' => $version,
            'channel' => $channel,
        ];


        $templates_data = [];

        if ($request_data['output_type'] == 'shopify') {
            $templates_data[] = $request_data['default_template_mapping'] ?? [];
        }

        if ($request_data['output_type'] == 'export') {
            $templates_data[] = self::exportShopifyMapping($array);
            $templates_data[] = self::exportMagentoMapping($array);
        }

        $template_objs = collect();


        foreach ($templates_data as $data) {
            $payload = json_encode($data['nodes']);
            $data['payload'] = $payload;

            $template_obj = new Template();

            if (isset($data['organization_id'])) {
                $template_obj->organization_id = $data['organization_id'];
            }
            $template_obj->version_id = $data['version'];
            $template_obj->channel_id = $data['catalog'];
            $template_obj->name = $data['temp_name'];
            $template_obj->payload = $data['payload'];
            $template_obj->type = $data['temp_type'];
            $template_obj->export_type = $data['export_type'] ?? null;
            $template_obj->product_status = isset($data['status']) ? $data['status'] : false;

            if (isset($request_data['selected_template']) && $request_data['selected_template'] == Str::slug($template_obj->name)) {
                return $success_callback($template_obj);
            }

            $template_objs->push($template_obj);
        }

        return $success_callback($template_objs);

    }


    static private function apimioMapping($data)
    {
        return [
            'organization_id' => $data['organization_id'],
            'temp_type' => 'import',
            'nodes' => [
                'data' => [
                    [
                        'from' => [
                            0 => 'Default,SKU',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'Default,sku',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Product Name',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,product_name',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,UPC/EAN/ISBN',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,upc_barcode',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Price',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,price',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Compare at Price',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,compare_at_price',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Cost Price',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,cost_price',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Quantity',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'Default,quantity',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Weight',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,weight',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Description',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'General,description',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Vendor',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'Default,vendor',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Brand',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'Default,brand',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Category',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'Default,categories',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,SEO URL',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'SEO,seo_url',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,SEO Title',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'SEO,seo_title',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,SEO Description',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'SEO,seo_description',
                        ],
                    ],
                    [
                        'from' => [
                            0 => 'Default,Media',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'Default,file',
                        ],
                    ],
                ],
                'variant' => [
                    'id' => 'handle Id',
                    'variant_options' => [
                        0 => 'Default,Color',
                        1 => 'Default,Size',
                        2 => NULL,
                    ],
                ],
            ],
            'version' => $data['version'],
            'catalog' => $data['channel'],
            'status' => '1',
            'temp_status' => 'on',
            'temp_name' => 'Apimio Default',
        ];
    }


    static private function exportShopifyMapping($data)
    {

        return [
            'organization_id' => $data['organization_id'],
            'temp_type' => 'export',
            'export_type' => 'shopify',
            'nodes' => [
                "data" => [
                    [
                        "from" => ["Default,handle"],
                        "with_formula" => "assign",
                        "to" => ["Handle"],
                    ],
                    [
                        "from" => ["General,product_name"],
                        "with_formula" => "assign",
                        "to" => ["Title"],
                    ],
                    [
                        "from" => ["General,description"],
                        "with_formula" => "assign",
                        "to" => ["Body (HTML)"],
                    ],
                    [
                        "from" => ["Default,file"],
                        "with_formula" => "assign",
                        "to" => ["Image Src"],
                    ],
                    [
                        "from" => ["SEO,seo_keyword"],
                        "with_formula" => "assign",
                        "to" => ["Tags"],
                    ],
                    [
                        "from" => ["Variant,sku"],
                        "with_formula" => "assign",
                        "to" => ["Variant SKU"],
                    ],
                    [
                        "from" => ["Variant,weight"],
                        "with_formula" => "assign",
                        "to" => ["Variant Grams"],
                    ],
                    [
                        "from" => ["Variant,weight_unit"],
                        "with_formula" => "assign",
                        "to" => ["Variant Weight Unit"],
                    ],
                    [
                        "from" => ["Variant,price"],
                        "with_formula" => "assign",
                        "to" => ["Variant Price"],
                    ],
                    [
                        "from" => ["Variant,compare_at_price"],
                        "with_formula" => "assign",
                        "to" => ["Variant Compare At Price"],
                    ],
                    [
                        "from" => ["Variant,barcode"],
                        "with_formula" => "assign",
                        "to" => ["Variant Barcode"],
                    ],
                    [
                        "from" => ["Variant,file"],
                        "with_formula" => "assign",
                        "to" => ["Variant Image"],
                    ],
                    [
                        "from" => ["SEO,seo_title"],
                        "with_formula" => "assign",
                        "to" => ["SEO Title"],
                    ],
                    [
                        "from" => ["SEO,seo_description"],
                        "with_formula" => "assign",
                        "to" => ["SEO Description"],
                    ],
                    [
                        "from" => ["Default,vendor"],
                        "with_formula" => "assign",
                        "to" => ["Vendor"],
                    ],
                    // [
                    //     "from" => ["General,quantity"],
                    //     "with_formula" => "assign",
                    //     "to" => ["Variant Inventory Qty"],
                    // ],
                    [
                        "from" => ["Variant,cost_price"],
                        "with_formula" => "assign",
                        "to" => ["Cost per item"],
                    ],
                    [
                        "from" => ["Default,categories"],
                        "with_formula" => "assign",
                        "to" => ["Standard Product Type"],
                    ],
                    [
                        "from" => ["Default,status"],
                        "with_formula" => "assign",
                        "to" => ["Status"],
                    ],
                ],
            ],
            'version' => $data['version'],
            'catalog' => $data['channel'],
            'status' => '1',
            'temp_status' => 'on',
            'temp_name' => 'Shopify Default',
        ];
    }


    static private function exportMagentoMapping($data)
    {

        return [
            'organization_id' => $data['organization_id'],
            'temp_type' => 'export',
            'export_type' => 'magento',
            'nodes' => [
                "data" => [
                    [
                        "from" => ["Variant,sku"],
                        "with_formula" => "assign",
                        "to" => ["sku"],
                    ],
                    [
                        "from" => ["General,product_name"],
                        "with_formula" => "assign",
                        "to" => ["name"],
                    ],
                    [
                        "from" => ["General,description"],
                        "with_formula" => "assign",
                        "to" => ["description"],
                    ],
                    [
                        "from" => ["Variant,price"],
                        "with_formula" => "assign",
                        "to" => ["price"],
                    ],
                    [
                        "from" => ["Variant,weight"],
                        "with_formula" => "assign",
                        "to" => ["weight"],
                    ],
                    [
                        "from" => ["Default,handle"],
                        "with_formula" => "assign",
                        "to" => ["url_key"],
                    ],
                ],
            ],
            'version' => $data['version'],
            'catalog' => $data['channel'],
            'status' => '1',
            'temp_status' => 'on',
            'temp_name' => 'Magento Default',
        ];
    }


}
