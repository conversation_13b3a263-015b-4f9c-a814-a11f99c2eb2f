<?php

use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['web']], function () {
    Route::group(['namespace'=>'Apimio\MappingConnectorPackage\Http\Controllers'], function (){
        Route::post('products/import/template/mapping','MappingFieldController@mapping_data')->name('mappingfields.import');
        Route::post('products/export/template/mapping','MappingFieldController@mapping_data')->name('mappingfields.export');

        Route::post('Mapping/template/delete','MappingFieldController@delete_template')->name('mapping_delete_template');
        Route::get('Mapping/template/apply/{id}','MappingFieldController@apply_template')->name('apply_mapping_template');
        Route::post('Mapping/template/create','MappingFieldController@save_template')->name('template.save');

        // components route
        Route::post('/add-row', 'MappingFieldController@add_row')->name("add.row");
        Route::post('/fetch-formula-fields', 'MappingFieldController@fetch_formula_fields')->name("fetch.formula.fields");
        Route::post('/convert', 'MappingFieldController@mapping_convert')->name("mapping.convert");

        // import variants options
        Route::post('/mapping/variants/add-option', 'MappingFieldController@mapping_add_variant_option')->name("mapping.add.variant.option");


        /*Formulas Routes*/
        Route::resource('vlookup', 'VlookupController');
        Route::post('vlookup/fetch', 'VlookupController@fetch')->name('vlookup.fetch');
    });
});

