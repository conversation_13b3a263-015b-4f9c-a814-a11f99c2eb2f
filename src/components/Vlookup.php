<?php

namespace Apimio\MappingConnectorPackage\components;

use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

class Vlookup extends Component
{
    public  $organization_id;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($organization)
    {
        $this->organization_id = $organization;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $vlookups = \Apimio\MappingConnectorPackage\models\Vlookup::all();
        return view('mapping::components.vlookup',compact('vlookups'));
    }
}
