<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;
use \Apimio\Gallery\Models\Folder as F;

class Folder extends Component
{
    public $name;
    public $id;
    public $link;
    public $parentFolderId;
    public $folder;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($name,$id,$link ,$parentFolderId = null)
    {
        $this->name = $name;
        $this->parentFolderId = $parentFolderId;
        $this->link = $link;
        $check = (int)$id;
        if($check ==0 ){
            $this->id = $id;
            $this->folder = F::findOrFail(decrypt($id));
        }
    else{
            $this->id = (int)$id;
        $this->folder = F::findOrFail($id);
        }
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.folder');
    }
}
