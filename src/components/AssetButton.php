<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;

class AssetButton extends Component
{
    public $folder;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.asset-button');
    }
}
