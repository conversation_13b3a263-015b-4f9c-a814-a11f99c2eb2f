<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;

class RemoveModal extends Component
{
    public $id; //Model opening Id
    public $formUrl; // Form submit route

    /**
     * Create a new component instance.
     *
     * @param $id
     * @param $formUrl
     * @return void
     */
    public function __construct($id, $formUrl)
    {
        $this->id = $id;
        $this->formUrl = $formUrl;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.remove-modal');
    }
}
