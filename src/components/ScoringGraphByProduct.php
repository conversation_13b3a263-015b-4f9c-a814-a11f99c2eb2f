<?php

namespace Apimio\Gallery\components;

use Apimio\Gallery\Classes\ImageQualityCount;
use Apimio\Gallery\Models\File;
use Illuminate\View\Component;

class ScoringGraphByProduct extends Component
{
    public $files;
    public $score;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->files = File::all();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $this->score = new ImageQualityCount();
        $this->score = $this->score->countImageQuality($this->files);
        return view('gallery::components.scoring-graph-by-product');
    }
}
