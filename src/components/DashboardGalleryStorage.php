<?php

namespace Apimio\Gallery\components;

use Apimio\Gallery\Classes\SubscribedPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

class DashboardGalleryStorage extends Component
{
    public $storage;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->storage['plan'] = (new SubscribedPlan())->GetPlan(Auth::user())->CalculateUsedStorage();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('gallery::components.dashboard-gallery-storage');
    }
}
