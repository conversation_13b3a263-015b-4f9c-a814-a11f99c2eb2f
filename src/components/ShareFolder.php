<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;

class ShareFolder extends Component
{
    public $id; //Model opening Id
    public $shareLink; //Link to share folder
    /**
     * Create a new component instance.
     * @param $id
     * @param null $shareLink
     * @return void
     */
    public function __construct($id,$shareLink = null)
    {
        $this->id = $id;
        $this->shareLink = env('APP_URL').'/'.$shareLink;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.share-folder');
    }
}
