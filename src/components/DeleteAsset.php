<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;

class DeleteAsset extends Component
{
    public $id; //Model opening Id
    public $fileid; //Model opening Id
    public $link;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($id,$fileid,$link)
    {
        $this->id =$id;
        $this->fileid =$fileid;
        $this->link =$link;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.delete-asset');
    }
}
