<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;

class FolderCreate extends Component
{
    public $id; //Model opening Id
    public $header; // Model header text
    public $formUrl; // Form submit route
    public $folderId; // Main folder Id
    public $folderName; // Main Folder name
    public $parentFolderId; // Parent folder Id in case of child folder
    public $message;
    public $title;
    public $btnText;

    /**
     * Create a new component instance.
     *
     * @param $id
     * @param $header
     * @param $formUrl
     * @param null $folderId
     * @param null $folderName
     * @param null $parentFolderId
     * @param null $message
     * @param null $title
     * @param null $btnText

     */
    public function __construct($id, $header, $formUrl , $folderId = null, $folderName = null, $parentFolderId = null, $message = null, $title = null, $btnText = null)
    {
        $this->id = $id;
        $this->header = $header;
        $this->btnText = $btnText;
        $this->formUrl = $formUrl;
        $this->title = $title;
        $this->message = $message;
        $this->folderId = $folderId;
        $this->folderName = $folderName;
        $this->parentFolderId = $parentFolderId;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $link=$_SERVER['REQUEST_URI'];
        return view('gallery::components.folder-create',compact('link'));
    }
}
