<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Step 1: Fetch existing data before altering the table structure.
        $existingData = DB::table('templates')->get(['id', 'version_id', 'channel_id']);

        // Step 2: Modify the table schema.
        Schema::table('templates', function (Blueprint $table) {
            // Assuming the foreign keys are named following <PERSON><PERSON>'s convention. Adjust if necessary.
            $table->dropForeign(['version_id']); // Use the actual constraint name
            $table->dropForeign(['channel_id']); // Use the actual constraint name

            $table->dropColumn('version_id');
            $table->dropColumn('channel_id');
        });

        Schema::table('templates', function (Blueprint $table) {
            $table->json('version_id')->nullable()->after('organization_id');
            $table->json('channel_id')->nullable()->after('version_id');
        });

        // Step 3: Update the table with the existing data converted to JSON.
        foreach ($existingData as $data) {
            DB::table('templates')->where('id', $data->id)->update([
                'version_id' => json_encode($data->version_id), // Convert to JSON array
                'channel_id' => json_encode($data->channel_id), // Convert to JSON array
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Disable foreign key checks to avoid constraint violations during the update
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Convert JSON data back to integer IDs.
        $templates = DB::table('templates')->get(['id', 'version_id', 'channel_id']);

        Schema::table('templates', function (Blueprint $table) {
            $table->dropColumn('version_id');
            $table->dropColumn('channel_id');
        });

        Schema::table('templates', function (Blueprint $table) {
            $table->unsignedBigInteger('version_id')->nullable()->after('organization_id');
            $table->unsignedBigInteger('channel_id')->nullable()->after('version_id');
        });

        // Update the table with the converted data.
        foreach ($templates as $template) {
            $versionId = json_decode($template->version_id, true) ?? null;
            $channelId = json_decode($template->channel_id, true) ?? null;

            DB::table('templates')->where('id', $template->id)->update([
                'version_id' => $versionId,
                'channel_id' => $channelId,
            ]);
        }

        // Re-enable foreign key checks after updates are complete
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Now, safely add foreign key constraints
        Schema::table('templates', function (Blueprint $table) {
            $table->foreign('version_id')->references('id')->on('versions')->onDelete('cascade');
            $table->foreign('channel_id')->references('id')->on('channels')->onDelete('cascade');
        });
    }
};
