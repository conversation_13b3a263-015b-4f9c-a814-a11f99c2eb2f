<?php

use Apimio\MappingConnectorPackage\models\Template;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $template = [];
        if (Schema::hasTable('templates'))
        {
            $template = Template::all();
            Schema::dropIfExists('templates');
        }
        Schema::create('templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('version_id')->constrained('versions')->onDelete('cascade');
            $table->foreignId('channel_id')->constrained('channels')->onDelete('cascade');
            $table->string('name');
            $table->json('payload');
            $table->enum('type',['import','export','shopify','clone','other'])->default('import');
            $table->boolean('product_status');
            $table->timestamps();
        });

        foreach ($template as $tem){
            $template_new = new Template();
            $template_new->create($tem->toArray());
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('templates');
    }
}
