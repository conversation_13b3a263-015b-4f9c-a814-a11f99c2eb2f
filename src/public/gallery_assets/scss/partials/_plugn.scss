//@import "variables";
//// sumoselect css
//.SumoSelect {
//    > .CaptionCont {
//        > label {
//            > i {
//                background-image: url("../images/down_black.svg") !important;
//                background-position: center center;
//                width: 16px;
//                height: 16px;
//                display: block;
//                position: absolute;
//                top: 2px !important;
//                left: -2px !important;
//                right: 0;
//                bottom: 0;
//                margin: auto;
//                background-repeat: no-repeat;
//                opacity: 1 !important;
//            }
//        }
//    }
//}
//
//.SumoSelect {
//    > .CaptionCont {
//        color: $black !important;
//        font-weight: 400;
//        font-size: 0.875rem;
//        height: 2.25rem !important;
//        line-height: 1rem;
//        &:hover {
//            border: 1px solid $blue !important;
//        }
//        &:focus {
//            border: 1px solid $blue !important;
//        }
//    }
//}
//#AttributeSet{
//    overflow: hidden;
//}
//#Category{
//    overflow: hidden;
//}
//
//// tagger css
//.tox-statusbar {
//    display: none !important;
//}
//.tagger + .tagger {
//    margin-top: 10px;
//}
//.custom-border-css {
//    border: 1px solid $gains-boro !important;
//    background: $white-smoke;
//    margin-right: 1px;
//    border-radius: 0px;
//}
//.tox {
//    .tox-statusbar {
//        align-items: center;
//        background-color: #fff;
//        border-top: 1px solid #e3e3e3;
//        color: rgba(34, 47, 62, 0.7);
//        display: flex;
//        flex: 0 0 auto;
//        font-size: 14px;
//        font-weight: 400;
//        height: 25px;
//        overflow: hidden;
//        padding: 2px 8px;
//        position: relative;
//        display: block !important;
//        text-transform: none;
//    }
//    .tox-edit-area__iframe {
//        background-color: #f8f8f8 !important;
//        border: 0;
//        box-sizing: border-box;
//        flex: 1;
//        height: 100%;
//        position: absolute;
//        width: 100%;
//    }
//    .tox-notification--in {
//        opacity: 0 !important;
//        height: 0px !important;
//        width: 0px !important;
//        display: none;
//    }
//}
//.tox-statusbar__branding {
//    display: none !important;
//}
//.tox-notification--warning {
//    background-color: #fff5cc;
//    border-color: #fff0b3;
//    height: 0px !important;
//    width: 0px !important;
//    color: #222f3e;
//}
//.tox-notifications-container {
//    position: absolute;
//    left: 1198px !important;
//    top: 433px !important;
//    max-height: 383px;
//}
//
//// date picker
//.active2 {
//    border: 2px solid #2c4bff !important;
//    opacity: 1;
//}
//
//.gj-textbox-md {
//    background: #F8F8F8;
//    border: 1px solid #E5E5E5;
//    display: block;
//    font-family: Roboto, sans-serif;
//    font-size: .875rem;
//    line-height: 0;
//    padding: 4px 12px;
//    width: 100%;
//    text-align: left;
//    color: #000000;
//}
//
//.gj-datepicker-md [role=right-icon] {
//    top: 7px;
//}
//
//.comboTreeArrowBtn{
//    -webkit-appearance: none;
//    -moz-appearance: none;
//     appearance: none;
//    background-image: url("../images/down_black.svg");
//    background-repeat: no-repeat;
//    background-position: right 8px center !important;
//    background-color: #fff !important;
//    background-size: 20px;
//    right: 1px !important;
//    bottom: 1px !important;
//    top: 1px !important;
//    border: 0 !important;
//}
//.comboTreeArrowBtn:hover {
//    -webkit-appearance: none;
//    -moz-appearance: none;
//     appearance: none;
//    background-image: url("../images/down_black.svg") !important;
//    background-repeat: no-repeat !important;
//    background-color: #fff !important;
//    background-position: right 8px center !important;
//    background-size: 20px;
//    border: 0 !important;
//}
//.comboTreeArrowBtn:active {
//    -webkit-appearance: none;
//    -moz-appearance: none;
//     appearance: none;
//    background-image: url("../images/down_black.svg") !important;
//    background-repeat: no-repeat !important;
//    background-color: #fff !important;
//    background-position: right 8px center !important;
//    background-size: 20px;
//    border: 0 !important;
//}
//.mdi:before, .mdi-set {
//    display: inline-block;
//    font: normal normal normal 26px/1 "Material Design Icons" !important;
//    -webkit-font-smoothing: antialiased;
//    -moz-osx-font-smoothing: grayscale;
//}
//.mdi-chevron-down-circle-outline::before {
//    content: "\F0B27";
//    font-size: 20px !important;
//}
//.mdi-chevron-right-circle-outline::before {
//    content: "\F0B2B";
//    font-size: 20px !important;
//}
//
//
//// js tree category
//.jsTree {
//    width: 100%;
//}
//.jsTree .itemParent {
//    /* div. more down under */
//    transition: all 0.3s ease-in;
//    padding: 6px 0px;
//    display: flex;
//}
//.jsTree .itemParent:hover {
//    background-color: #d1d1d1;
//}
//.jsTree .itemParent .contenteditable {
//    margin: 0px;
//    flex-grow: 1;
//}
//.jsTree .itemParent p {
//    margin: 0px 6px;
//    max-width: 300px;
//    padding: 2px 0px;
//}
//#color-change-delete{
//    fill: red;
//}
//
//.jsTree .itemParent .afterIcon {
//    display: inline-block;
//    flex-shrink: 0;
//    width: 19px;
//    height: 19px;
//    margin: 0px 4px;
//    background: url('../../../img/tree-images/delete.svg');
//    background-size: 12px 12px;
//    background-repeat: no-repeat;
//    background-position: center center;
//    cursor: pointer;
//    transition: opacity 0.3s ease-out;
//    background-size: 18px 16px;
//    opacity: 1;
//}
//.jsTree .itemParent:hover .afterIcon {
//    opacity: 1;
//}
//.jsTree .itemParent .afterIconEdit {
//    display: inline-block;
//    flex-shrink: 0;
//    width: 19px;
//    height: 19px;
//    margin: 0px 4px;
//    background: url('../../../img/tree-images/edit.svg');
//    background-size: 12px 12px;
//    background-repeat: no-repeat;
//    background-position: center center;
//    cursor: pointer;
//    transition: opacity 0.3s ease-out;
//    background-size: 18px 25px;
//    opacity: 1;
//}
//.jsTree .childGroup {
//    /* ul */
//    padding: 0px 0px 0px 12px;
//    margin: 0;
//}
//.jsTree .item {
//    /* li */
//    list-style: none;
//    padding: 0;
//    margin: 0;
//    transition: all 0.3s ease-in;
//}
//.jsTree .itemParent .preIcon {
//    display: inline-block;
//    flex-shrink: 0;
//    width: 19px;
//    height: 19px;
//    margin: 0px 4px;
//    background-size: 14px 14px !important;
//    background-repeat: no-repeat !important;
//    background-position: center center !important;
//}
//.jsTree .itemParent .preIcon.arrowDown {
//    cursor: pointer;
//    background: url('../../../img/tree-images/arrowdown-black.svg');
//    transition: transform 0.3s ease-out;
//    margin-top:4px;
//}
//.jsTree .itemParent .preIcon.arrowDown.arrowRotate {
//    transform: rotate(-90deg);
//}
//.jsTreeContextMenu {
//    width: max-content;
//    display: none;
//    position: fixed;
//    border-radius: 1px;
//    overflow: hidden;
//    background: white;
//    border: 1px solid #106fab;
//    box-sizing: border-box;
//}
//.jsTreeContextMenu p {
//    margin: 0;
//    padding: 4px 8px;
//    transition: all 0.3s ease-in;
//    background: white;
//}
//.jsTreeContextMenu p:hover {
//    background: #eee;
//}
//.firstTree{
//    padding: 0px 0px 0px 18px;
//}
//
//// selectize input element css
//.selectize-input {
//    height: 36px !important;
//    border: 1px solid $gains-boro !important;
//    padding: 0px 12px !important;
//    font-size: 0.875rem !important;
//    color:$black;
//    font-weight: 400;
//    line-height: 37px !important;
//    background: $white-smoke !important;
//}
//
//.selectize-control.single {
//	.selectize-input {
//		&:after {
//			content: ' ';
//			display: block;
//			position: absolute;
//			top: 50%;
//			right: 12px !important;
//			margin-top: -3px;
//			width: 0;
//			height: 0;
//			border-style: solid;
//			border-width: 6px 6px 0 6px !important;
//			border-color: $black transparent transparent transparent !important;
//		}
//	}
//}
//
