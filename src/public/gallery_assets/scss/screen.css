/*Gallery*/
/* Gallery Section  */
/*.folder-title {*/
/*    display: none !important;*/
/*}*/
/*.page-title {*/
/*    display: none !important;*/
/*}*/
.collection {
  display: grid;
  grid-auto-columns: max-content;
  grid-auto-flow: dense;
  /*grid-auto-rows: minmax(18px, auto);*/
  grid-gap: 15px;
  gap: 5px;
  grid-template-columns: repeat(6, 1fr);
  /*height: 103px;*/
  /*overflow: hidden;*/
  /*width: 615px;*/
}

.share-collection {
  display: grid;
  grid-auto-columns: max-content;
  grid-auto-flow: dense;
  /*grid-auto-rows: minmax(18px, auto);*/
  grid-gap: 15px;
  gap: 5px;
  grid-template-columns: repeat(7, 1fr);
  /*height: 103px;*/
  /*overflow: hidden;*/
  /*width: 615px;*/
}

.progress-bar-properties {
  height: 8px;
  border-radius: 50px;
  margin-bottom: 5px;
}

.card-collection {
  display: grid;
  grid-auto-columns: max-content;
  grid-template-columns: repeat(6, 1fr);
  grid-gap: 15px;
}

.card-toggle:hover {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px !important;
  transition: transform 0.8s ease-out;
  transform: scale(1.03);
  /*transform: translateY(-5px);*/
}

.hide_title {
  display: none;
}

.folder_head {
  font-family: "Roboto", sans-serif;
  color: black !important;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 2rem;
  text-decoration: none;
}

.folder_head:hover {
  text-decoration: none;
}

.path-head {
  font-size: 24px;
  line-height: 30px;
  color: #a5a5a5;
  text-decoration: none;
}

.modal-aside,
.modal.right .modal-aside {
  width: 346px;
  position: fixed;
  height: 100vh;
  right: 0;
  top: 0;
  transform: translate3d(0%, 0, 0);
}

.modal_content,
.modal.right .modal_content {
  height: 100%;
  overflow-y: auto;
}

.modal_body,
.modal.right .modal_body {
  padding: 15px 15px 80px;
}

/*Right*/
.modal.right.fade .modal-aside {
  /*right: -320px;*/
  transition: opacity 0.3s linear, right 0.3s ease-out;
}

.modal.right.fade.in .modal-aside {
  right: 0;
}

/* ----- MODAL STYLE ----- */
.modal_content {
  border-radius: 0;
  border: none;
}

.modal_header {
  border-bottom-color: #eeeeee;
  background-color: #fafafa;
}

.folder:hover {
  border-color: #2c4bff !important;
}

.folder:hover .folder-icon {
  color: #2c4bff !important;
}

.folder:hover .folder_name {
  color: #2c4bff !important;
}

.link-card {
  border-radius: 7px;
  box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;
}

.link-card:hover {
  /*moz-transition: all .4s ease-in-out;*/
  /*-o-transition: all .4s ease-in-out;*/
  /*-webkit-transition: all .4s ease-in-out;*/
  /*transition: all .4s ease-in-out;*/
  background: linear-gradient(0deg, rgb(44, 75, 255) 0%, rgb(2, 126, 251) 100%);
}

.link-card:hover .link-card-text {
  color: white;
}

.unlink_btn {
  background-color: white !important;
}

/*.link-card:hover  .unlink_btn {*/
/*    background-color: white; !important;*/
/*}*/
.custom-margin {
  margin-bottom: -7px;
}

.asset_description {
  width: 52rem;
}

.score-label {
  width: 14px !important;
  height: 14px !important;
}

/* Gallery Section */
@media (max-width: 1420px) {
  .asset_description {
    width: 43rem !important;
  }
}
@media (max-width: 821px) {
  .asset_description {
    width: 30rem !important;
    margin-bottom: 0 !important;
  }
  .asset_header {
    flex-direction: column;
  }
  .btn_section {
    margin-bottom: 8px !important;
  }
  .collection {
    grid-template-columns: repeat(3, 1fr);
    gap: 0;
  }
  .score-label {
    width: 10px !important;
    height: 10px !important;
  }
  .image-score {
    flex-direction: column;
    width: 80%;
  }
  .image-score-good {
    /*justify-content: space-evenly;*/
  }
  .card-collection {
    grid-template-columns: repeat(4, 1fr) !important;
  }
  .card-img-top {
    height: 105px !important;
  }
  .card-body {
    padding: 8px 8px 1px 8px !important;
  }
}
@media only screen and (min-width: 767px) and (max-width: 991px) {
  .asset_description {
    width: 43rem !important;
  }
}/*# sourceMappingURL=screen.css.map */