<?php

namespace App\Notifications;

use App\Models\Organization\Organization;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ApimioNotification extends Notification
{
    use Queueable;
    private $details;
    private $aFlag = false;
    private $mailFlag = false;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($details)
    {
        $this->details = $details;

    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        //when true it will skip mail driver and save notification only in your Database record.
        if ($this->aFlag == true){
            return [DBCustomChannel::class];
        }
        if ($this->mailFlag == true){
            return ['mail'];
        }
        return ['mail',DBCustomChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
                ->subject($this->details['subject'])
                ->greeting($this->details['greeting'])
                ->line(new HtmlString($this->details['body']))
//                ->action($this->details['actionText'], $this->details['actionURL'])
                ->line($this->details['thanks']);
    }


    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        return [
            $this->details
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  boolean
     *
     */
    public function only_db_notify($param)
    {
        return $this->aFlag = $param;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  boolean
     *
     */
    public function only_mail_notify($param)
    {
        return $this->mailFlag = $param;
    }

}
