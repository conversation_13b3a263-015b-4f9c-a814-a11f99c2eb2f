<?php

namespace App\Models\Cashier;

use InvalidArgumentException;
use App\Classes\Plan\PlanClass;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Lara<PERSON>\Cashier\Subscription as CashierSubscription;

class Subscription extends CashierSubscription
{
    use HasFactory;

    public function swap($prices, array $options = [])
    {
        if (empty($prices = (array) $prices)) {
            throw new InvalidArgumentException('Please provide at least one price when swapping.');
        }

        $this->guardAgainstIncomplete();

        $items = $this->mergeItemsThatShouldBeDeletedDuringSwap(
            $this->parseSwapPrices($prices)
        );

        $stripeSubscription = $this->owner->stripe()->subscriptions->update(
            $this->stripe_id, $this->getSwapOptions($items, $options)
        );

        $firstItem = $stripeSubscription->items->first();
        $isSinglePrice = $stripeSubscription->items->count() === 1;

        $this->fill([
            'stripe_status' => $stripeSubscription->status,
            'stripe_price' => $isSinglePrice ? $firstItem->price->id : null,
            'quantity' => $isSinglePrice ? ($firstItem->quantity ?? null) : null,
            'ends_at' => null,
        ])->save();

        $subscriptionItemIds = [];

        foreach ($stripeSubscription->items as $item) {
            $subscriptionItemIds[] = $item->id;
            $itemProductIds[] = $item->price->product;

            $this->items()->updateOrCreate([
                'stripe_id' => $item->id,
            ], [
                'stripe_product' => $item->price->product,
                'stripe_price' => $item->price->id,
                'quantity' => $item->quantity ?? null,
            ]);
        }

        // Delete items that aren't attached to the subscription anymore...
        $this->items()->whereNotIn('stripe_id', $subscriptionItemIds)->delete();

        $stripe_product = $firstItem->price->product ?? null;

        if(isset($stripe_product)){
            $plan = PlanClass::$plans->WhereIn("stripe_product_id",$itemProductIds)->first();
            if($plan){
                $this->owner->update(['plan_handle' => $plan->handle]);
            }
        }

        $this->unsetRelation('items');

        $this->handlePaymentFailure($this);

        return $this;
    }
}
