<?php

namespace App\Models\Admin;

use App\Models\Invite\Invite;
use App\Models\Organization\File;
use App\Models\Organization\Organization;
use App\Models\Organization\OrganizationUser;
use Illuminate\Database\Eloquent\Model;

class Admin extends Model
{
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function show_organizations($id)
    {
        $user_organization = OrganizationUser::where('user_id',$id)->get();
        if (sizeof($user_organization) > 0) {
            foreach ($user_organization as $key => $organization)
            {
                $data['org'][$key] = Organization::where('id', $organization->organization_id)->first();
                $data['org'][$key]->files_count = File::where('organization_id', $organization->organization_id)->count();
                $data['org'][$key]->files_size = File::where('organization_id', $organization->organization_id)->sum('size');
            }
            return $data;
        } else {
            return abort(404);
        }

    }

    public function deleteOrganization($id)
    {
        return  Organization::findOrFail($id)->delete();
    }

    public function all_invitations($id)
    {
        $organization_ids = OrganizationUser::where('user_id',$id)->pluck('organization_id')->toArray();
        return Invite::whereIn('organization_id_sender',$organization_ids)->orderBy('id','desc')->paginate(32);
    }
}
