<?php

namespace App\Models\Product;

use App\Classes\Template\GenerateNode;
use App\Models\Channel\Channel;
use App\Models\Organization\Organization;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class Template extends Model
{

    private $data;

    protected $guarded = [];


    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });

    }


    /**
     * Set data templates.
     *
     *
     * @param array
     *
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }


    /**
     * Set organization id.
     *
     *
     * @param int
     *
     */
    public function set_organization(int $organization_id)
    {
        $this->organization_id = $organization_id;
        return $this;
    }


    /**
     * method for creating new template record
     *
     * @param  callable  $error_callback if we found any error
     * @param  callable  $success_callback return success callback
     *
     *
     * @return  object $success_callback with returned created template object
     */
    public function store($success_callback, $error_callback)
    {
        try {
            if (isset($this->data['temp_id'])) {
                $template = $this->find($this->data['temp_id']);

            } else {
                $template = $this;
            }

            if (isset($this->data['organization_id'])) {
                $template->organization_id = $this->data['organization_id'];
            }

            $template->version_id = $this->data['version'];
            $template->channel_id = $this->data['catalog'];
            $template->name = $this->data['temp_name'];
            $template->payload = $this->data['payload'];
            $template->type = $this->data['temp_type'];
            if(isset($this->organization_id))
                $template->organization_id = $this->organization_id ;
            $template->product_status = isset($this->data['status']) ? $this->data['status'] : false;
            $template->save();
            return $success_callback($template);
        }
        catch(\Exception $e){
            return $error_callback($e->getMessage());
        }
    }





    /**
     * method for storing template record
     *
     * @param  callable  $success_callback return success callback
     * @param  callable  $error_callback if we found any error
     *
     * @return  object | string object returns template record and string return if error occurs
     */
    function template_store(array $data,$success_callback, $error_callback){
        $node = new GenerateNode();

        // generating json node
        $payload = $node->generateObjectNode($data['nodes']);
        $data['payload'] = $payload;

        //creating template
        return $this->set_data($data)->store(function ($tem_obj) use ($success_callback){
            return $success_callback($tem_obj);
        },function ($error) use ($error_callback){
            return $error_callback($error);
        });
    }









    public function channels()
    {
        return $this->belongsTo(Channel::class)->withoutGlobalScopes();
    }

    public function organizations()
    {
        return $this->belongsTo(Organization::class);
    }


    public function versions()
    {
        return $this->belongsTo(Version::class)->withoutGlobalScopes();
    }
}
