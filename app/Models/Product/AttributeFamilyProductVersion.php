<?php

namespace App\Models\Product;

use App\Events\Product\CurrencySeparatorUpdated;
use Illuminate\Database\Eloquent\Model;

class AttributeFamilyProductVersion extends Model
{
    protected $guarded = [];

    protected $fillable = ['product_id', 'version_id', 'attribute_id', 'family_id', 'value', 'unit', 'product_version_id','attribute_family_id'];

    public static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::created(function ($model) {
            $product_version = ProductVersion::with('version')->where('id',$model->product_version_id)->first();
            if ($product_version) {
                $attribute = Attribute::whereHas('attribute_families',function ($query) use($model) {
                    $query->where('attribute_family.id',$model->attribute_family_id);
                })->first();

                if ($attribute->isNumberField()) {
                    if ($product_version->version->separator == ',') {
                        //if value is separated by ,
                        if (preg_match('/^\d{3},/', $model->value)) {
                            $model->value =  str_replace('', $product_version->version->separator , $model->value);
                        } else {
                            $model->value =  str_replace('.', $product_version->version->separator , $model->value);
                        }
                    } else {
                        $model->value =  str_replace(',', $product_version->version->separator , $model->value);
                    }
                }

            } else {
                return false;
            }

        });
    }



    public function attributeFamilies() {
        return $this->belongsTo(AttributeFamily::class)->withoutGlobalScopes();
    }

    public function family(){
        return $this->belongsTo(Family::class,'family_id','id')->withoutGlobalScopes();
    }

    public function familyProductVersions() {
        return $this->belongsTo(FamilyProductVersion::class)->withoutGlobalScopes();
    }

    public function attribute() {
        return $this->belongsTo(Attribute::class)->withoutGlobalScopes();
    }

    public function versions() {
        return $this->belongsTo(Version::class)->withoutGlobalScopes();
    }
}
