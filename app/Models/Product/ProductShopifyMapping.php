<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ProductShopifyMapping extends Model
{
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (Auth::check())
                $model->organization_id = Auth::user()->organization_id;
        });
        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }
}
