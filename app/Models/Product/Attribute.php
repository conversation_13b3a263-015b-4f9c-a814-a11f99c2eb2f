<?php

namespace App\Models\Product;

use App\Classes\Shopify\CreateMetaFieldDefinition;
use App\Jobs\Shopify\CreateMetafieldDefinationJob;
use App\Models\Channel\AttributeChannel;
use App\Models\Channel\Channel;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\OrganizationUser;
use App\Rules\AttributeValidationRule;
use App\Rules\UniqueManyToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;



class Attribute extends Model
{
    private $data, $filter;

    public  bool $isShopifyMetaFieldSyncEnable;

    protected $guarded = [];

    protected $fillable = ['name', 'attribute_type_id',  'description', 'handle' , 'organization_id','rules'];
    /**
     * Constructor for storing data and applying post logic
     * to organization structure.
     *
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }


    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    /**
     * Set data attribute.
     *
     *
     * @param array
     *
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    /*
     * getting same name variats
     * */
    function check_variant_options()
    {
        /*checking same name variant attribute while adding new record*/
        $same_variant = $this->query()->where('name', $this->data['name'])->where('attribute_type_id', 13);

        if (isset($this->data['organization_id'])) {
            $same_variant = $same_variant->where('organization_id', $this->data['organization_id']);
        }

        if (isset($this->data['id'])) {
            $same_variant = $same_variant->where('id', '!=', $this->data['id']);
        }
        $same_variant = $same_variant->first();
        if ($same_variant) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Set organization id.
     *
     *
     * @param int
     *
     */
    public function set_organization(int $organization_id)
    {
        $this->organization_id = $organization_id;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return string
     */
    public function get_data()
    {
        return $this->data;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $attributes = [];

        if (isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if (isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        $rules = [
            "attribute_type_id" => "required",
            'name' => ['required', 'min:3', 'max:255'/*, new UniqueManyToMany(new Attribute(), $attributes)*/],
            'attribute_options' => 'required_if:attribute_type_id,4,13',
            'attribute_options.*.name' => ['required', 'max:255'],
            'description' => 'max:250',
            //            'min' => new AttributeValidationRule($this->data),
            'attribute_family' => isset($this->data['method_type']) ? Rule::requiredIf(!in_array($this->data['method_type'], ['import', 'clone'])) : Rule::requiredIf($this->data['attribute_type_id'] != 13),
        ];


        if ((isset($this->data['type']) && $this->data['type'] == 'date') && (isset($this->data['start_date']) && isset($this->data['end_date']))) {
            $rules['start_date'] = 'before_or_equal:end_date,';
        } elseif ((isset($this->data['type']) && $this->data['type'] == 'date_and_time') && (isset($this->data['start_date_time']) && isset($this->data['end_date_time']))) {
            $rules['start_date_time'] = 'before_or_equal:end_date_time';
        }
        // Check if required is equal to 1, then add max and min rules
        if (isset($this->data['is_required']) && $this->data['is_required'] == 1) {
            //            $rules['max'] = new AttributeValidationRule($this->data);
            $rules['min'] = new AttributeValidationRule($this->data);
        }
        return $rules;
    }

    public function messages()
    {
        return [
            'attribute_type_id.required' => 'The Attribute type is required',
            'name.required' => 'The Attribute name field is required',
            'attribute_options.required_if' => 'Please add attribute options to create multi select dropdown.',
            'attribute_options.*.name.required' => 'Attribute Option cannot be empty.',
            'attribute_options.*.name.max' => 'Name cannot be greater than 255',
            'attribute_family.required' => 'Please select attribute set.',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules(), $this->messages())->after(function ($validator) {

            if ($this->data['attribute_type_id'] == 13) {
                // if($this->check_variant_options() ) {
                //     $validator->errors()->add('name', 'variant option must be unique.');
                // }
            }
            if (isset($this->data['attribute_family'])) {
                if (isset($this->data['id'])) {
                    if (($this->set_id($this->data['id'])->id != $this->data['id']) || ($this->set_id($this->data['id'])->name != $this->data['name'])) {
                        $attribute_data = '';
                        foreach ($this->data['attribute_family'] as $family) {

                            $family = Family::query()->where('id', $family);
                            if (isset($this->organization_id)) {
                                $family = $family->where('organization_id', $this->organization_id);
                            }
                            $family = $family->whereHas('attributes', function (Builder $query) {
                                $query->where('name', $this->data['name']);
                            })->first();
                            if ($family) {
                                $attribute_data .= $family->id . ',';
                            }
                        }
                    }
                } else {
                    $attribute_data = '';
                    foreach ($this->data['attribute_family'] as $family) {
                        $family = Family::query()->where('id', $family);
                        if (isset($this->organization_id)) {
                            $family = $family->where('organization_id', $this->organization_id);
                        }
                        $family = $family->whereHas('attributes', function (Builder $query) {
                            $query->where('name', $this->data['name']);
                        })->first();
                        if ($family) {
                            $attribute_data .= $family->id . ',';
                        }
                    }
                }
            }
            if (!empty($attribute_data)) {
                $validator->errors()->add('duplicate_attribute_ids', $attribute_data);
            }

            if (isset($this->data['min'])) {
                if (str_contains($this->data['min'], "-")) {
                    $validator->errors()->add('min', "minimum numbers cannot be in negative");
                }
            }
            if (isset($this->data['max'])) {
                if (str_contains($this->data['max'], "-")) {
                    $validator->errors()->add('max', "maximum numbers cannot be in negative");
                }
            }
            if (isset($this->data['max_number_precision'])) {
                if (str_contains($this->data['max_number_precision'], "-")) {
                    $validator->errors()->add('max_number_precision', "precision numbers cannot be in negative");
                }
            }
        });
        return $validator;
    }


    /**
     * Get the variants unique option values.
     *
     *
     */
    public function unique_attribute_options()
    {
        try {
            if (isset($this->data['organization_id']) && isset($this->data['name'])) {
                $attribute_fetch = $this->with('attribute_options')
                    ->where('name', $this->data['name'])
                    ->where('organization_id', $this->data['organization_id'])
                    ->get()->first();
            } else {
                $attribute_fetch = $this->with('attribute_options')
                    ->where('name', $this->data['name'])
                    ->get()->first();
            }

            if (isset($attribute_fetch)) {
                $this->data['id'] = $attribute_fetch->id;
                foreach ($this->data['attribute_options'] as $key => $attr_option) {
                    $option_check = $this->with('attribute_options')
                        ->whereHas('attribute_options', function ($query) use ($attr_option) {
                            $query->where('name', $attr_option);
                        })
                        ->where('name', $this->data['name'])->get();
                    if ($option_check->count() > 0) {
                        unset($this->data['attribute_options'][$key]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }


    public function unique_attribute_duplication_options($attribute_options, $attribute_id = null)
    {
        $variant_options_array = [];
        $attribute_options = array_map(function ($q) use (&$variant_options_array, &$attribute_id) {
            if (!in_array($q['name'], $variant_options_array)) {
                if (AttributeOption::query()->where(['name' => $q['name'], 'attribute_id' => $attribute_id])->first()) {
                    return null;
                } else {
                    $variant_options_array[] = $q['name'];
                    return $q;
                }
            } else {
                return null;
            }
        }, $attribute_options);
        return array_filter($attribute_options);
    }


    /*
     * adding variant attributes and assign them to family
     * */
    public function variant_atributes($data, $attribute)
    {
        $attribute_family = new AttributeFamily();
        foreach ($data['family'] as $family) {
            $attribute_family->family_id = $family;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();
        }
    }

    public function isShopifyMetaFieldSyncEnable(bool $value): static
    {
        $this->isShopifyMetaFieldSyncEnable = $value;
        return $this;
    }

    public function store($error_callback, $success_callback)
    {
        // dd($this->data);
        $import_create_attribute_flag = 0;
        // below code works when user want to create attribute from mapping packages module
        if (isset($this->data['method_type']) && $this->data['method_type'] == 'import') {
            if (isset($this->data['attribute_family_name'])) {
                $Temp_family = Family::where('name', $this->data['attribute_family_name'])->get()->first();
                if (empty($Temp_family)) {
                    $Temp_family = new Family();

                    $Temp_family = $Temp_family->set_data([
                        'organization_id' => $this->data['organization_id'],
                        'name' => $this->data['attribute_family_name']
                    ])->store(function ($error) use ($error_callback) {
                        return $error_callback($error);
                    }, function ($family) {
                        return $family;
                    });
                }
                $this->data['attribute_family'][] = $Temp_family->id;
            }
        }

        $validation = $this->validation();

        if ($validation->fails()) {
            // TODO: The below code is creating issues when we add attribute as multiselect.
            //            if ($this->data['attribute_type_id'] == 4) {
            //                $error_check = $validation->errors();
            //                if ($error_check->has('name')) {
            //                    $import_create_attribute_flag = 1;
            //                    dd($validation->errors());
            //                    $this->unique_attribute_options();
            //                }
            //            }
            //            else {
            return $error_callback($validation->errors());
            //            }
        }





        // for csv variant code
        if (isset($this->data['method_type']) && in_array($this->data['method_type'], ['import', 'clone'])) {
            if (in_array($this->data['attribute_type_id'], [4, 13])) {
                $old_attribute = $this->where('name', $this->data['name'])->where('attribute_type_id', $this->data['attribute_type_id'])->get()->first();
                if ($old_attribute) {
                    $import_create_attribute_flag = 1;
                    $this->unique_attribute_options();
                }
            }
        }




        if (isset($this->data['id'])) {
            $attribute = $this->find($this->data['id']);
        }
        /* elseif (isset($this->data['key']))  {
            $attribute = $this->where('handle',$this->data['key'])->first();
            If key(handle) does not exists, make new attribute
            if (!$attribute) {
                $attribute = $this;
            }
        }*/ else {
            $attribute = null;
            if ($this->data['attribute_type_id'] == 13) {
                $attribute = $this->where(['name' => $this->data['name'], 'attribute_type_id' => 13])->first();
            }
            if (!isset($attribute)) {
                $attribute = $this;
            }
        }

        if (isset($this->data['organization_id'])) {
            $attribute->organization_id = $this->data['organization_id'];
        }

        /*Changing date and time parameters*/
        if (isset($this->data['start_date_time'])) {
            $this->data['start_date'] = $this->data['start_date_time'];
            unset($this->data['start_date_time']);
        }
        if (isset($this->data['end_date_time'])) {
            $this->data['end_date'] = $this->data['end_date_time'];
            unset($this->data['end_date_time']);
        }

        if ($this->data['attribute_type_id'] == 1) //Type Single line text
        {
            $arr = [
                'regular_expression' => $this->data['regular_expression'] ?? null,
                'max' => $this->data['max'] ?? null,
                'min' => $this->data['min'] ?? null,
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->singlelinetTextAttribute($arr['regular_expression'], $arr['max'], $arr['min'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 2) //Type Number
        {
            $arr = [
                'max' => $this->data['max'] ?? null,
                'min' => $this->data['min'] ?? null,
                'max_precision' => $this->data['max_number_precision'] ?? null,
                'type' => $this->data['type'] ?? 'integer',
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->numberAttribute($arr['max'], $arr['min'], $arr['max_precision'], $arr['type'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 3) //Type Multiline line text
        {
            $arr = [
                'regular_expression' => $this->data['regular_expression'] ?? null,
                'max' => $this->data['max'] ?? null,
                'min' => $this->data['min'] ?? null,
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->multilineTextAttribute($arr['regular_expression'], $arr['max'], $arr['min'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 4) //Type Multiselect
        {
            $arr = [
                'value_type' => $this->data['value_type'] ?? 'single',
                'type' =>  'choices',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->multiSelectAttribute($arr['value_type'], $arr['shopify_id'], $arr['required'], $arr['type']);
        }

        if ($this->data['attribute_type_id'] == 5) //Type Data and Time
        {
            $arr = [
                'start_date' => isset($this->data['start_date']) ? date('Y-m-d H:i:s', strtotime($this->data['start_date'])) : null,
                'end_date' => isset($this->data['end_date']) ? date('Y-m-d H:i:s', strtotime($this->data['end_date'])) : null,
                'type' => $this->data['type'] ?? null,
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();

            $attribute->rules = $attribute_type->dateAndTimeAttribute($arr['start_date'], $arr['end_date'], $arr['type'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 6) //Type File
        {
            $arr = [
                'type' => $this->data['file_type'] ?? null,
                'file' => $this->data['file_choice'] ?? null,
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->fileAttribute($arr['type'], $arr['file'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 7) //Type Measurement
        {
            $arr = [
                'type' => $this->data['type'] ?? 'weight',
                'max_unit' => $this->data['max_unit'] ?? null,
                'min_unit' => $this->data['min_unit'] ?? null,
                'max_value' => $this->data['max'] ?? null,
                'min_value' => $this->data['min'] ?? null,
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->measurementAttribute($arr['type'], $arr['max_unit'], $arr['min_unit'], $arr['max_value'], $arr['min_value'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 8) //Type Rating
        {
            $arr = [
                'max' => $this->data['max'] ?? null,
                'min' => $this->data['min'] ?? null,
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->ratingAttribute($arr['max'], $arr['min'], $arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 9)  //type json
        {
            $arr = [
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->jsonAttribute($arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 10)  //type true/false
        {
            $arr = [
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->booleanAttribute($arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 11)  //type url
        {
            $arr = [
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->urlAttribute($arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        if ($this->data['attribute_type_id'] == 12)  //type color
        {
            $arr = [
                'value_type' => $this->data['value_type'] ?? 'single',
                'shopify_id' => $this->data['shopify_id'] ?? null,
                'required' => $this->data['is_required'] ?? null,
            ];
            $attribute_type = new \App\Classes\Product\Attribute();
            $attribute->rules = $attribute_type->colorAttribute($arr['value_type'], $arr['shopify_id'], $arr['required']);
        }

        $attribute->name = $this->data['name'];
        if (isset($this->data['description'])) {
            $attribute->description = $this->data['description'];
        }
        if (isset($this->data['attribute_type_id'])) {
            $attribute->attribute_type_id = $this->data['attribute_type_id'];
        }

        if (isset($this->data['key'])) {     //if slug comes from shopify
            $attribute->handle = $this->data['key'];
        } else {
            $attribute->handle = Str::slug($this->data['name'], '_');
        }
        if (isset($this->organization_id))
            $attribute->organization_id = $this->organization_id;
        if (isset($this->data['organization_id']))
            $attribute->organization_id = $this->data['organization_id'];
        $attribute->save();

        if (isset($this->data['shopify_id']) && isset($this->data['channel_id'])) {
            $attributeChannel = AttributeChannel::query()->where([
                'channel_id' => $this->data['channel_id'],
                'attribute_id' => $attribute->id,
                'store_connect_type' => 'shopify',
            ])->first();
            if (!$attributeChannel) {
                $attributeChannel = new AttributeChannel();
                $attributeChannel->channel_id = $this->data['channel_id'];
                $attributeChannel->attribute_id = $attribute->id;
                $attributeChannel->store_connect_type = 'shopify';
            }
            $attributeChannel->store_connect_id = $this->data['shopify_id'];
            $attributeChannel->save();
        }


        /* if user selects attribute family while adding attribute */
        if (isset($this->data['attribute_family'])) {
            $attribute->families()->sync($this->data['attribute_family']);
        }

        if (isset($this->data['family']) && $this->data['attribute_type_id'] == 13) {
            $this->variant_atributes($this->data, $attribute);
        }

        if ($this->data['attribute_type_id'] == 4 || $this->data['attribute_type_id'] == 13) {
            try {
                if ($import_create_attribute_flag == 1) //This will run while importing products.
                {
                    $attribute->attribute_options()->createMany($this->data['attribute_options']);
                } else {

                    // $attribute->attribute_options()->delete();
                    $attribute->attribute_options()->createMany($this->unique_attribute_duplication_options($this->data['attribute_options'], $attribute->id));
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        } else {
            $attribute->attribute_options()->delete();
        }
        /*
         *
         * for saving attribute in shopify
         *
         * */
        if (!isset($this->data['id']) && isset($this->isShopifyMetaFieldSyncEnable) && $this->isShopifyMetaFieldSyncEnable && $attribute->attribute_type_id != 13) {

            $rules = json_decode($attribute->rules, true);

            if (!isset($rules['shopify_id'])) {

                $org_id = $attribute->organization_id;

                $user_id = OrganizationUser::query()
                    ->where('organization_id', $org_id)
                    ->orderBy('id', 'asc')
                    ->first()->value('user_id');

                $channel_id = Channel::query()
                    ->where('organization_id', $org_id)
                    ->pluck('id')
                    ->toArray();

                $shopify_channel_count  = ShopifyChannel::query()
                    ->whereIn('channel_id', $channel_id)
                    ->count();

                if ($shopify_channel_count > 0) {

                    CreateMetafieldDefinationJob::dispatch([
                        'organization_id' => $org_id,
                        'channel_ids' => $channel_id,
                        'attribute' => $attribute,
                        'user_id' => $user_id
                    ]);
                }
                return $success_callback($attribute, true);
            }
        }
        return $success_callback($attribute);
    }


    public function fetch()
    {
        $attribute = $this->with("attribute_type", "families")->where('attribute_type_id', '!=', 13);
        if ($this->filter) {
            if (isset($this->filter["name"]))
                $attribute = $attribute->where("name", "LIKE", "%" . $this->filter["name"] . "%");
        }
        return $attribute->orderBy("id", "DESC")->paginate(32);
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }


    public function isNumberField()
    {
        $rules = json_decode($this->rules);
        return isset($rules->type) && in_array($rules->type, ['price', 'integer', 'decimal']);
    }


    public function attribute_type()
    {
        return $this->belongsTo(AttributeType::class);
    }

    public function attribute_options()
    {
        return $this->hasMany(AttributeOption::class);
    }

    public function attribute_families()
    {
        return $this->hasMany(AttributeFamily::class, 'attribute_id')->withoutGlobalScopes();
    }

    public function families()
    {
        return $this->belongsToMany(Family::class)->withoutGlobalScopes()->withPivot('id');
    }

    public function attributeChannel()
    {
        return $this->hasMany(AttributeChannel::class);
    }


    public function text_validation()
    {
        //        $res = json_decode($this->rules,true);
        //
        //        $val = array();
        //        foreach($res as $key=> $value) {
        //            if ($key == "regex"){
        //                $val[$key] = $key.":".base64_encode($value);
        //            } else {
        //                $val[$key] = $key.":".$value;
        //            }
        //            if (isset($val['required'])) {
        //                $val["required"] = "required";
        //            }
        //        }
        //        $str = implode("|",$val);
        //        return $str;

        return $this->rules;
    }



    public function validate_single_values()
    {
        //        $str = '';
        //        $res = json_decode($this->rules, true);
        //
        //        foreach ($res as $key => $value) {
        //            if ($key === 0) {
        //                if (!empty($str)) {
        //                    $str .= "|".$value;
        //                } else {
        //                    $str .= $value;
        //                }
        //            } else {
        //                if ($key == "required") {
        //                    if (!empty($str)) {
        //                        $str .= "|".$key;
        //                    } else {
        //                        $str .= $key;
        //                    }
        //                } else {
        //                    $str .= "|".$key.":".$value;
        //                }
        //            }
        //        }
        //        return $str;
        return $this->rules;
    }

    public function file_attribute_validation()
    {

        $str = '';
        $res = json_decode($this->rules, true);
        foreach ($res as $key => $value) {
            if ($key == 'type') {
                $str .= $value;
            }

            if ($key == 'file') {
                foreach ($value as $data) {
                    $str .= '|' . $data;
                }
            }
            if ($key == 'required') {
                $str .= "|required";
            }
        }
        return $str;
    }

    /*return attribute type ['single', 'value']*/
    public function attribute_value_type()
    {
        $res = json_decode($this->rules, true);
        if (isset($res['value_type'])) {
            return $res['value_type'];
        } else {
            return '';
        }
    }


    public function attribute_default_min_unit()
    {
        $res = json_decode($this->rules, true);
        if (isset($res['min_unit'])) {
            return $res['min_unit'];
        } else {
            return null;
        }
    }


    /**
     * this method is used for mapping create attribute
     * these type not be created or not be parsed in mapping creation packages
     *
     *
     * @return array
     */
    static public function not_fetch_able_attributes(): array
    {
        return [
            4 => 'multiselect',
            5 => 'date and time',
            6 => 'file',
            8 => 'rating',
            10 => 'true or false',
            12 => 'color',
            13 => 'variants'

        ];
    }
}
