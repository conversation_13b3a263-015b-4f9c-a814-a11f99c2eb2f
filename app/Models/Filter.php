<?php

namespace App\Models;

use Auth;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Filter extends Model
{
    use HasFactory;

    private $data;
    protected $guarded = [];



    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = auth()->user()->organization_id;
                    $model->user_id = auth()->user()->id;
                }
            }
        });

        static::addGlobalScope('organization_user', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', auth()->user()->organization_id)
                        ->where('user_id', '=', auth()->user()->id);
            }
        });

    }

    /**
     * Set data filters.
     *
     *
     * @param array
     *
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }


    /**
     * Set organization id.
     *
     *
     * @param int
     *
     */
    public function set_organization(int $organization_id)
    {
        $this->organization_id = $organization_id;
        return $this;
    }

    public function getPayloadAttribute($value)
    {
        return json_decode($value, true);
    }


    /**
     * method for creating or updating new FILTERS record
     *
     * @param  callable  $error_callback if we found any error
     * @param  callable  $success_callback return success callback
     *
     *
     * @return  object $success_callback with returned created FILTERS payload object
     */
    public function store($error_callback,$success_callback)
    {
        try {
            if (isset($this->data['id'])) {
                $filter = $this->find($this->data['id']);

            } else {
                $filter = $this;
            }

            if (isset($this->data['organization_id'])) {
                $filter->organization_id = $this->data['organization_id'];
            }

            if (isset($this->data['user_id'])) {
                $filter->user_id = $this->data['user_id'];
            }

            $filter->payload = is_array($this->data['payload']) ? json_encode($this->data['payload']) : $this->data['payload'];
            $filter->type = $this->data['type'];
            $filter->save();

            return $success_callback($filter);
        }
        catch(\Exception $e){
            return $error_callback("Something went wrong.! Please try again later");
        }
    }

}
