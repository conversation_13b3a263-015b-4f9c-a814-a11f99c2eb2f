<?php

namespace App\Models;

use App\Models\Product\Template;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExportCsvApiRecord extends Model
{
    protected $table = 'export_records';

    use HasFactory;

    protected $fillable = ['id','email','template_id','brands_portal_id'];

    //relations

    public function template()
    {
        return $this->belongsTo(Template::class);
    }
    public function brands_portal()
    {
        return $this->belongsTo(BrandsPortal::class);
    }
}
