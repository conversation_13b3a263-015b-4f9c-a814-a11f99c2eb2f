<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;

class BatchProgress extends Model
{
    use HasFactory;

    public array $type_to_title_array = [
        'export_csv' => "Products Export CSV Queue",
        'import_csv' => "Products Import CSV Queue",
        'bulk_assign' => "Products Bulk Assign Queue",
        'shopify_sync_in' => "Products Syncing to Shopify Store Queue",
        'shopify_sync_out' => "Products Fetching from Shopify Store Queue",

    ];

    public $guarded = [];

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    public function store($data){
        $obj = $this;
        if(isset($data['id'])){
            $obj = $obj->find($data['id']);
        }
        $obj->organization_id = $data['organization_id'];
        $obj->user_id = $data['user_id'];
        $obj->batch_id = $data['batch_id'];

        if(isset($data['type'])) {
            $obj->type = $data['type'];
        }
        if(isset($data['status'])) {
            $obj->status = $data['status'];
        }
        $obj->save();

        return $obj;

    }



    public function batch()
    {
        $batch = Bus::findBatch($this->batch_id);

        $data = [
            'id' => $batch->id,
            'batch_progress' => ($batch->pendingJobs == 0) ? 100 : $batch->progress(),
        ];
        return $data;
    }




    public function title()
    {
        $title = "";
        if (in_array($this->type,array_keys($this->type_to_title_array))){
            $title = $this->type_to_title_array[$this->type];
        }
        return $title;
    }

}
