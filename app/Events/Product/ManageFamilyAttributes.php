<?php

namespace App\Events\Product;

use App\Models\Product\Product;
use Ghazniali95\ShopifyConnector\App\Classes\Rest\Admin2023_10\Collect;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ManageFamilyAttributes
{
    use Dispatchable, InteractsWithSockets, SerializesModels;


    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }

    /**
     * Create a new event instance.
     *
     * @param array $families
     * @param Product $product
     * @param array $versions
     * @return void
     */
    public function __construct(public Product $product,public  array $families, public array $versions = [])
    {

    }
}
