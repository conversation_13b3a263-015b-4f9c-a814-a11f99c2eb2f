<?php

namespace App\View\Components\Dashboard;

use App\Models\Organization\File;
use Illuminate\View\Component;

class media_stats_pie_chart extends Component
{

    public $total_files;

    public $file_stats;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        $files = new File();
        $this->file_stats =  $files->get_quality_stats();
        if($this->file_stats["approve"] == 0 && $this->file_stats["warning"] == 0 && $this->file_stats["error"] == 0) {
            $this->file_stats["approve"] = 100;
            $this->file_stats["warning"] = 0;
            $this->file_stats["error"] = 0;
        }

        $this->total_files = $files->count();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.dashboard.media_stats_pie_chart');
    }
}
