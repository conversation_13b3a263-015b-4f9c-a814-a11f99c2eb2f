<?php

namespace App\View\Components\Guide;

use App\Models\Product\Brand;
use App\Models\Product\Product;
use Illuminate\View\Component;

class ExportProducts extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $product = new Product();
        //Getting total products with their respective sku's
        $data['product_count'] = $product->count();

        return view('components.guide.export-products', compact('data'));
    }
}
