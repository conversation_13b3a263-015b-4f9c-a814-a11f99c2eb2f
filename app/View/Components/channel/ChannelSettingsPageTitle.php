<?php

namespace App\View\Components\Channel;

use Illuminate\View\Component;

class ChannelSettingsPageTitle extends Component
{
    public $catalogname;
    public $description;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($catalogname, $description)
    {
        $this->catalogname = $catalogname;
        $this->description = $description;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.channel.channel-settings-page-title');
    }
}
