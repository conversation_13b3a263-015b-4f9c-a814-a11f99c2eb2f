<?php

namespace App\View\Components\invite;

use Illuminate\View\Component;

class title extends Component
{
    public $name;
    public $description;
    public $badge;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($name, $description, $badge)
    {
        $this->name = $name;
        $this->description = $description;
        $this->badge = $badge;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.invite.title');
    }
}
