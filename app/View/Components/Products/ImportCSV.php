<?php

namespace App\View\Components\Products;

use App\Models\Channel\Channel;
use App\Models\Product\Family;
use App\Models\Product\Version;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

/**
 * @deprecated This class will be removed in the future.
*/
class ImportCSV extends Component
{

    public $template_attributes;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($templateAttributes)
    {
        $this->template_attributes = $templateAttributes;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
            $products_versions = Version::get();
            $products_catalog = Channel::get();
        return view('components.products.import-c-s-v' , compact('products_versions' ,'products_catalog'));
    }
}
