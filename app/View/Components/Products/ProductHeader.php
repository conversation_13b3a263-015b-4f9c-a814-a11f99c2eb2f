<?php

namespace App\View\Components\Products;

use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Version;
use Illuminate\View\Component;

class ProductHeader extends Component
{
    public $id,$product,$versions,$default_families,$isVersionDropdown;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($id,$product = null,$versions= null,$default_families= null, $isVersionDropdown=true)
    {
        $this->id = $id;
        $this->product = $product;
        $this->versions = $versions;
        $this->default_families = $default_families;
        $this->isVersionDropdown = $isVersionDropdown;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $id =$this->id;
        $products = new Product();
        $this->product = $products->fetch(function($product) use ($id){
            return $product->findOrFail($id);
        });
        $ids = Version::rightJoin('product_version', 'versions.id', '=', 'product_version.version_id')->where("product_id", $id)
            ->pluck("versions.id")->toArray();

        $this->versions = Version::all();
        $default_family = new Family();
        $this->default_families = $default_family->fetch_data(function ($family){
            return $family->where('is_default',1)->get();
        });
        return view('components.products.product-header');
    }
}
