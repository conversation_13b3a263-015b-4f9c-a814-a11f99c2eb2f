<?php

namespace App\View\Components\Products;

use App\Models\Product\Family;
use Illuminate\View\Component;

class VariantNavbar extends Component
{
    public $id,$default_families;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($id,$default_families= null)
    {
        $this->id = $id;
        $this->default_families = $default_families;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $default_family = new Family();
        $this->default_families = $default_family->fetch_data(function ($family){
            return $family->where('is_default',1)->get();
        });
        return view('components.products.variant-navbar');
    }
}
