<?php

namespace App\View\Components\Products;

use App\Models\Product\Family;
use App\Models\Product\ProductVersion;
use App\Models\Product\Version;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\View\Component;
use Illuminate\View\View;

class EditProductHeaderNavs extends Component
{
    public $default_families = [];
    public $versions = [];
    public $product;
    public $version;
    public $version_score;
    public $total_percentage;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($product, $version)
    {
        $this->product = $product;
        $this->version = $version;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return Application|Factory|View
     */
    public function render()
    {
        $this->default_families = Family::where("is_default", 1)->get();
        $this->versions = Version::get();
        $this->version_score = $this->version->getScoreByProductId($this->product->id);
        $product_versions = ProductVersion::where('product_id', $this->product->id)->get();
        $count = 0;
        $this->total_percentage = 0;
        foreach ($product_versions as $key => $product_version) {
            $count++;
            $version = Version::find($product_version->version_id);
            $this->total_percentage +=  $version->productVersionCompletnessPercentage($this->product->id);
        }

        if ($count > 0) {
            $this->total_percentage = ceil(($this->total_percentage*100)/($count*100));
        } else {
            $this->total_percentage = 1;
        }
        return view('components.products.edit-product-header-navs');
    }
}
