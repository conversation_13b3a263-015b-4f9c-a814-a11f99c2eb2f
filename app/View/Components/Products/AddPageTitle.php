<?php

namespace App\View\Components\Products;

use Illuminate\View\Component;

class AddPageTitle extends Component
{
    public $name, $routes;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($name, $routes)
    {
        $this->name = $name;
        $this->routes = $routes;

    }
    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.add-page-title');
    }
}
