<?php

namespace App\View\Components\Products\Import;

use Illuminate\View\Component;

/**
 * @deprecated This class will be removed in the future.
 */
class VariantsOption extends Component
{
    /**
     * The heading attributes and selection template option value.
     *
     * @var array
     */
    public $heading_attributes;
    public $template_option;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($heading_attributes,$template_option = null)
    {
        $this->heading_attributes = $heading_attributes;
        $this->template_option = $template_option;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.import.variants-option');
    }
}
