<?php

namespace App\View\Components\Products;

use App\Models\Channel\Channel;
use App\Models\Product\Version;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

/**
 * @deprecated This class will be removed in the future.
 */
class ExportCSV extends Component
{
    public $template_attributes;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($templateAttributes = null)
    {
        $this->template_attributes = $templateAttributes;
    }


    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $products_versions = array();
//            $products_versions = Version::where('organization_id',Auth::user()->organization_id)->select('id','name')->distinct('name')->get();
            $products_versions = Version::get();
            $products_catalog = Channel::get();
            $export_type = [
                'shopify' => 'Shopify',
                'magento' => 'Magento',
                'default' => "Default"
            ];
        return view('components.products.export-c-s-v' , compact('products_versions','products_catalog', 'export_type'));
    }
}
