<?php

namespace App\View\Components\Products;

use Illuminate\View\Component;
use App\Models\Channel\Channel;

class AddProduct extends Component
{
    public $channels;
    public $orgId;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($orgId = null , $channels = [])
    {
        $this->orgId = $orgId ?? auth()->user()->organization_id;
        $this->channels = Channel::where('organization_id', $this->orgId)->get();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.add-product');
    }
}
