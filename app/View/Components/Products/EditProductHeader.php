<?php

namespace App\View\Components\Products;

use App\Models\Product\Product;
use Illuminate\View\Component;

class EditProductHeader extends Component
{
    public $product;
    public $version;
    public $buttons;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($product,$buttons = true,$version = null)
    {
        $this->product = $product;
        $this->version = $version;
        $this->buttons = $buttons;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.edit-product-header');
    }
}
