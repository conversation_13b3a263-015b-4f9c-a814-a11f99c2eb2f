<?php

namespace App\View\Components\Products;

use App\Models\Product\Version;
use Illuminate\View\Component;

/**
 * @deprecated This class will be removed in the future.
 */
class Vlookup extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        $vlookups = \App\Models\Formula\Vlookup::all();
        return view('components.products.vlookup',compact('vlookups'));
    }
}
