<?php

namespace App\View\Components\General;

use Illuminate\View\Component;

class EmptyPage extends Component
{
    public $description;
    public $button;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($description , $button = true)
    {
        $this->description = $description;
        $this->button = $button;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.general.empty-page');
    }
}
