<?php

namespace App\View\Components\General;

use Illuminate\Http\Request;
use Illuminate\View\Component;

class SearchBar extends Component
{
    public $placeholder;
    public $q;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($placeholder, Request $request)
    {
        $this->placeholder = $placeholder;
        $this->q = $request->get('q');
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.general.search-bar');
    }
}
