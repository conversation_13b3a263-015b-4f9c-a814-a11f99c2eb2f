<?php

namespace App\View\Components\General;

use App\Models\Product\Product;
use Illuminate\View\Component;

class ProductGraph extends Component
{
    public $channel, $data;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($channel =null , $data = null)
    {
        $this->channel = $channel;
        $this->data = $data;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {

        $data['total_product'] = 0 ;
        $data['publish_product'] = 0 ;
        $data['draft_product'] = 0 ;
        $data['sync_product'] = 0 ;
        $data['graph'] = '[0,0,0]' ;
        if($this->channel != null)
        {
            $channel_id = $this->channel;
            $products= new Product();
            $data['total_product'] = $products->get_count(function($product) use ($channel_id){
                return $product->whereHas('channels',function($q) use ($channel_id){
                    $q->whereIn('channel_id',$channel_id);
                })->count();
            });

            $data['publish_product'] = $products->get_count(function($product) use ($channel_id){
                return $product->where('status', 1)->whereHas('channels',function($q) use ($channel_id){
                    $q->whereIn('channel_id',$channel_id)->whereNull('channel_sync_id');
                })->count();
            });

            $data['draft_product'] = $products->get_count(function($product) use ($channel_id){
                return $product->where('status', 0)->whereHas('channels',function($q) use ($channel_id){
                    $q->whereIn('channel_id',$channel_id)->whereNull('channel_sync_id');;
                })->count();
            });
            $data['sync_product'] = $products->get_count(function($product) use ($channel_id){
                return $product->whereHas('channels',function($q) use ($channel_id){
                    $q->whereIn('channel_id',$channel_id)->whereNotNull('channel_sync_id');
                })->count();
            });
        }
        else
        {
            $products= new Product();
            $data['total_product'] = $products->get_total_count();

            $published_not_link_products = $products->get_count(function($product){
                return $product->where('status', 1)->doesntHave('channels')->count();
            });

            $draft_not_link_products = $products->get_count(function($product){
                return $product->where('status', 0)->doesntHave('channels')->count();
            });


            $data['publish_product'] = $products->get_count(function($product){
                return $product->where('status', 1)->whereHas('channels',function($q) {
                    $q->whereNull('channel_sync_id');
                })->count();
            });
            $data['publish_product'] = $data['publish_product']+$published_not_link_products;

            $data['draft_product'] = $products->get_count(function($product){
                return $product->where('status', 0)->whereHas('channels',function($q){
                    $q->whereNull('channel_sync_id');;
                })->count();
            });
            $data['draft_product'] = $data['draft_product']+$draft_not_link_products;

            $data['sync_product'] = $products->get_count(function($product){
                return $product->whereHas('channels',function($q){
                    $q->whereNotNull('channel_sync_id');
                })->count();
            });
        }

        $data['graph'] ='['.$data['publish_product'].','.$data['draft_product'].','.$data['sync_product'].']';
       $this->data = $data;

        return view('components.general.product-graph');
    }
}
