<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use phpDocumentor\Reflection\Types\Parent_;
use Stripe\StripeClient;

class PlanCreateCommand extends Command
{
    private $stripe;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:plans';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create plans in database and in stripe dashboard';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $bar = $this->output->createProgressBar();

        $bar->start();

        //Community Plan
        \Illuminate\Support\Facades\DB::table('plans')->insert([
            'stripe_monthly_id' => App::environment('production') ? 'price_1K2cHoAfw08UxlACMplJLVpj' : 'price_1K2DD2JKtHOhwx4oTbN7c2XT',
            'stripe_yearly_id' => App::environment('production') ? 'price_1K2cHoAfw08UxlACcQJVkRwz' : 'price_1K2DD2JKtHOhwx4ovXBruvUU',
            'name' => "Community Plan",
            'handle' => "community_plan",
            'price_per_month' => 0,
            'price_per_year' => 0,
            'no_of_products' => 100,
            'icon_link' => 'media/billing/community.png',
            'storage' => 2,
            'no_of_catalogue' => 1,
            'no_of_languages' => 1,
            'no_of_currencies' => 1,
            'no_of_retailers' => 5,
            'no_of_vendors' => 5,
            'no_of_team_members' => 0,
            'no_of_channels' => 1,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => false,
            'trial_period' => 0,
        ]);

        $bar->advance();

        //Standard Plan
        \Illuminate\Support\Facades\DB::table('plans')->insert([
            'stripe_monthly_id' => App::environment('production') ? 'price_1K2cL2Afw08UxlAC8wQc6t39' : 'price_1K2DEjJKtHOhwx4oOrzgIQGw',
            'stripe_yearly_id' => App::environment('production') ? 'price_1K2cL2Afw08UxlAC5vZahQUw' : 'price_1K2DEjJKtHOhwx4oozHgr24p',
            'name' => "Standard Plan",
            'handle' => "standard_plan",
            'price_per_month' => 199,
            'price_per_year' => 159,
            'no_of_products' => 5000,
            'icon_link' => 'media/billing/starter.png',
            'storage' => 100,
            'no_of_catalogue' => 1,
            'no_of_languages' => 0,
            'no_of_currencies' => 0,
            'no_of_retailers' => 0,
            'no_of_vendors' => 0,
            'no_of_team_members' => 3,
            'no_of_channels' => 0,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => true,
            'trial_period' => 14,
        ]);

        $bar->advance();

        //Plus Plan
        \Illuminate\Support\Facades\DB::table('plans')->insert([
            'stripe_monthly_id' => App::environment('production') ? 'price_1K2cNAAfw08UxlACL5MWUnZE' : 'price_1K2DHkJKtHOhwx4oNB8NanLr',
            'stripe_yearly_id' => App::environment('production') ? 'price_1K2cNAAfw08UxlACfqefpNzP' : 'price_1K2DHkJKtHOhwx4oPC6N4g5B',
            'name' => "Plus Plan",
            'handle' => "plus_plan",
            'price_per_month' => 999,
            'price_per_year' => 799,
            'no_of_products' => 50000,
            'icon_link' => 'media/billing/business.png',
            'storage' => 1000,
            'no_of_catalogue' => 1,
            'no_of_languages' => 0,
            'no_of_currencies' => 0,
            'no_of_retailers' => 0,
            'no_of_vendors' => 0,
            'no_of_team_members' => 10,
            'no_of_channels' => 0,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => true,
            'trial_period' => 14,
        ]);

        $bar->advance();
        //cataloge plan
        DB::table('plans')->insert([
            'stripe_monthly_id' => App::environment('production') ? 'price_1K3bu2JKtHOhwx4o9AlEnJ74' : 'price_1K3bu2JKtHOhwx4o9AlEnJ74',
            'stripe_yearly_id' => /*App::environment('production') ? 'price_1K2cNAAfw08UxlACfqefpNzP' : 'price_1K2DHkJKtHOhwx4oPC6N4g5B'*/ '',
            'name' => "Cataloge Plan",
            'handle' => "cataloge_plan",
            'price_per_month' => 30,
            'price_per_year' => 0,
            'no_of_products' => 0,
            'icon_link' => 'media/billing/business.png',
            'storage' => 0,
            'no_of_catalogue' => 0,
            'no_of_languages' => 0,
            'no_of_currencies' => 0,
            'no_of_retailers' => 0,
            'no_of_vendors' => 0,
            'no_of_team_members' => 0,
            'no_of_channels' => 0,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => true,
            'trial_period' => 14,
        ]);
        $bar->finish();

        return $this->info('Plans Created Successfully');
    }
}
