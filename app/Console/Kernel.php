<?php

namespace App\Console;

use App\Console\Commands\SaveImage;
use App\Models\Schedule\Task;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        SaveImage::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // price schedules
        $schedule->call(function () {
            $now = Carbon::now('UTC');

            // Process pending tasks
            $pendingTasks = Task::where('status', 'pending')
                ->where('scheduled_at', '<=', $now)
                ->get();

            foreach ($pendingTasks as $task) {
                $task->processTasks(false); // Process normal tasks
                if (empty($task->reverse)) {
                    $task->update(['status' => 'completed']);
                } else {
                    $task->update(['status' => 'processing']);
                }
            }

            // Process reversal tasks
            $reversalTasks = Task::where('status', 'processing')
                ->where('reverse->status', 'pending')
                ->where('reverse->scheduled_at', '<=', $now)
                ->get();


            foreach ($reversalTasks as $task) {
                $task->processTasks(true); // Process reversal tasks
                $task->update(['status' => 'completed','reverse->status' => 'completed']);
            }
        })->everyMinute()->name('Process Scheduled Tasks');

        // $schedule->command('inspire')->hourly();
        $schedule->command('images:save')->everyTwoMinutes();
        $schedule->command('organization:subscription')->daily();
        $schedule->command('organization:shopifySubscription')->monthly();
        // ->cron('0 0 */29 * *') // Run every 29 days
        // ->withoutOverlapping();


    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
