<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ImageDimension implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $img = getimagesize($value);
        $width = $img[0];
        $height = $img[1];
        if ($height < $width + 50 || $height > $width - 50) {
            return true;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Image is not rectangular!';
    }
}
