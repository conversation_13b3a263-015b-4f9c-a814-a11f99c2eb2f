<?php

namespace App\Rules;

use App\User;
use Illuminate\Contracts\Validation\Rule;

class OwnEmailCheck implements Rule
{
    private $own_id;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($own_id)
    {
      $this->own_id = $own_id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $user = User::where('email',$value)->where('id',$this->own_id)->first();
        if($user){
            return false;
        }else {
            return true;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'you can\'t invite yourself.';
    }
}
