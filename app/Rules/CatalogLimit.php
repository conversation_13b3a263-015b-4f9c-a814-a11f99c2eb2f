<?php

namespace App\Rules;

use App\Classes\Shopify\ShopifyUsageCharge;
use App\Models\Organization\Plan;
use Illuminate\Auth\Access\Response;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Traits\Billing\BillingTrait;

class CatalogLimit implements Rule
{
    use BillingTrait;
    public $user;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {

    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The catalog limit reached, please upgrade your subscription or contact customer support.';
    }
}
