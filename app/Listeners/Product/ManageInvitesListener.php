<?php

namespace App\Listeners\Product;

use App\Models\Invite\Invite;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Exception;
use Illuminate\Support\Facades\Log;

class ManageInvitesListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $invites_ids = [];
        if (!$event->data) {
            return;
        }

        foreach ($event->data as $data) {
            if (array_key_exists('id',$data)) {
                $invites_ids[] = $data['id'];
            } elseif(array_key_exists('name',$data)) {
                if (!empty($data['name'])) {
                    $invites_ids[] = Invite::findOrCreateForProduct($data['name'], $event->product)->id;
                }
            } else {
                Log::warning('Both "id", "name" keys and data are required.');
            }
        }
        if ($event->refresh) {
            $event->product->invites()->sync(array_filter($invites_ids));
        } else {
            $event->product->invites()->syncWithoutDetaching(array_filter($invites_ids));
        }
    }
}
