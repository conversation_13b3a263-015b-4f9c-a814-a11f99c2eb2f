<?php

namespace App\Listeners\Product;

use App\Events\Product\ManageFamilyAttributes;
use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Family;
use App\Models\Product\ProductVersion;
use Illuminate\Support\Facades\Log;

class ManageFamilyAttributesListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param ManageFamilyAttributes $event
     * @return void
     */
    public function handle(ManageFamilyAttributes $event)
    {
        $product = $event->product;

        $version_id = null;
        if (empty($event->versions)) {
            $version = $product->versions()->first();
            $version_id = $version->id ?? null;
        } else {
            //TODO: Implemented attributes value saving for just single version, after we do this for multiple versions
             $version = array_shift($event->versions);
            $version_id = $version['id'] ?? null;
        }

        foreach ($event->families as $family) {
            $name = $family['name'] ?? null;
            $attributes = $family['attributes'] ?? [];

            if (empty($name)) {
                \Log::channel('events')->error("family name does not exist in array");
                return;
            }

            // Check if the family already exists or create a new one
            $family = Family::firstOrCreate(['name' => $name, "organization_id" => $product->organization_id]);



            if (!$version_id) {
                \Log::channel('events')->error("Version not available for product id : " . $product->id);
                return;
            }


            foreach ($attributes as $attribute) {
                if (empty($attribute['handle']) || empty($attribute['attribute_type_id'])) {
                    Log::channel('events')->warning("Invalid attribute data");
                    continue;
                }


                // Define your search criteria
                $searchCriteria = [
                    'handle' => $attribute['handle'],
                    'attribute_type_id' => $attribute['attribute_type_id'],
                    'organization_id' => $product->organization_id
                ];

                // Attempt to find the first record that matches the criteria
                $attributeObj = Attribute::where($searchCriteria)->first();

                // If no existing record is found, create a new one
                if (!$attributeObj) {
                    $attributeObj = Attribute::create([
                        'name' => $attribute['name'] ?? $attribute['handle'],
                        'handle' => $attribute['handle'],
                        'attribute_type_id' => $attribute['attribute_type_id'],
                        'organization_id' => $product->organization_id
                    ]);
                }
                // Assign the attribute to the family
                $family->attributes()->syncWithoutDetaching($attributeObj);

                // Define the additional keys and values
                $additionalKeysValues = [
                    "family_id" => $family->id ?? null,
                    "attribute_id" => $attributeObj->id ?? null,
                    "version_id" => $version_id,
                    "product_id" => $product->id,
                    "attribute_family_id" => AttributeFamily::where("attribute_id", $attributeObj->id)->where("family_id", $family->id)->value("id"),
                    "product_version_id" => ProductVersion::where("product_id", $product->id)->where("version_id", $version_id)->value("id"),
                ];

                if (isset($attribute['value']) && !is_array($attribute['value'])) {
                    $attribute['value'] = [
                        [
                            'value' => $attribute['value']
                        ]
                    ];
                }elseif(isset($attribute['value']) && !isset($attribute['value'][0]['value'])){
                    foreach ($attribute['value'] as $key => $value){
                        $attribute['value'][$key] = [
                            'value' => $value
                        ];
                    }
//                    $attribute['value'] = [$attribute['value']];
                }

                AttributeFamilyProductVersion::query()->where($additionalKeysValues)->delete();

                $additionalKeysValues['unit'] = $attributeObj->attribute_default_min_unit();

                if(!isset($attribute['value'])){
                    $attribute['value'] = [];     
                 }    
                 
                foreach ($attribute['value'] as $attribute_value) {
                    if ($attributeObj->attribute_type_id == 4) {
                        if (!empty($attribute_value['value'])) {
                            $attributeObj->attribute_options()->firstOrCreate(['name' => $attribute_value['value']]);
                        }
                    }


                    $data = AttributeFamilyProductVersion::Create([
                        'product_id' => $additionalKeysValues['product_id'],
                        'attribute_id' => $additionalKeysValues['attribute_id'],
                        'family_id' => $additionalKeysValues['family_id'],
                        'version_id' => $additionalKeysValues['version_id'],
                        'attribute_family_id' => $additionalKeysValues['attribute_family_id'],
                        'product_version_id' => $additionalKeysValues['product_version_id'],
                        'value' => $attribute_value['value'] ?? null,
                        'unit' => $attribute_value['unit'] ?? $additionalKeysValues['unit'],
                    ]);
                }
            }
        }
    }
}
