<?php

namespace App\Policies;

use App\Models\Organization\Organization;
use App\Models\Organization\OrganizationUser;
use App\Models\Organization\OrganizationUserPermission;
use App\Models\Organization\Permission;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class OrganizationUserPermissionPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function view_dashboard(User $user, $org_id): bool
    {
        return $this->hasPermission($user, $org_id , 'view_dashboard');
    }

    public function add_shopify(User $user, $org_id): bool
    {
        return $this->hasPermission($user, $org_id , 'add_shopify');
    }

    public function sync_product_to_shopify(User $user, $org_id): bool
    {
        return $this->hasPermission($user, $org_id , 'sync_product_to_shopify');
    }

    public function perform_billing(User $user, $org_id ): bool
    {
        return $this->hasPermission($user, $org_id , 'perform_billing');
    }

    public function add_and_edit_product(User $user , $org_id): bool
    {
        return $this->hasPermission($user, $org_id , 'add_and_edit_product');
    }

    public function invite_team(User $user , $org_id): bool
    {
        return $this->hasPermission($user, $org_id , 'invite_team');
    }

    private function getOrganizationId($user , $org_id){
      return  OrganizationUser::where(['user_id'=>$user->id, 'organization_id'=>$org_id])->value('id');
    }

    private function getPermissionId($permission){
       return Permission::where('handle',$permission)->value('id');
    }

    private function hasPermission($user , $org_id , $permission){
        $org_user_id = $this->getOrganizationId($user ,$org_id);
        $permission_id = $this->getPermissionId($permission);
        $per = OrganizationUserPermission::query()
            ->where([
                'organization_user_id' =>$org_user_id ,
                'permission_id'=>$permission_id
            ] )->first();
        if($per) {
            return true;
        }else
        {
            return false;
        }

    }
}
