<?php

namespace App\Policies;

use App\Classes\Shopify\ShopifyUsageCharge;
use App\Models\Channel\Channel;
use App\Models\Organization\Organization;
use App\Models\Organization\Plan;
use App\Models\Product\Product;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CatalogPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\User $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\User $user
     * @param \App\Models\Channel\Channel $channel
     * @return mixed
     */
    public function view(User $user, Channel $channel)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\User $user
     * @return mixed
     */
    public function create(User $user)
    {
        if(!App::environment('local')) {
            //if user comes from stripe
            if(Auth::user()->user_organization()->subscription_type() == 'stripe') {
                $current_plan_id = Auth::user()->user_organization()->plans()->first();
                if (Plan::where('handle','community_plan')->where('stripe_monthly_id', $current_plan_id->current_stripe_plan)->orWhere('stripe_yearly_id', $current_plan_id->current_stripe_plan)->first()) {
                    //if user has subscribed free community plan
                    return Response::deny('Store Limit reached, upgrade billing.');
                } else {
                    //incrementing subscription quantity
                    return $user->subscription('default')->incrementQuantity(1, $current_plan_id->current_stripe_plan);
                }
            }
            //if user comes from shopify
            elseif(Auth::user()->user_organization()->subscription_type() == "shopify") {
                $shop = new ShopifyUsageCharge();
                //if user is on free plan
                if ($shop->plan_check() == 'free plan') {
                    return Response::deny('Store Limit reached, upgrade billing.');
                }
                //if user is on paid plan
                else {
                    if($shop->check_capped_amount()) {
                        dd('here');
                    } else {
                        return Response::deny('your capped amount limit reached, please contact customer support.');
                    }
                }
            } else {
                return Response::deny('Store Limit reached, upgrade billing.');
            }
        } else {
            return Response::allow();
        }
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\User $user
     * @param \App\Models\Channel\Channel $channel
     * @return mixed
     */
    public function update(User $user, Channel $channel)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\User $user
     * @param \App\Models\Channel\Channel $channel
     * @return mixed
     */
    public function delete(User $user, Channel $channel)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\User $user
     * @param \App\Models\Channel\Channel $channel
     * @return mixed
     */
    public function restore(User $user, Channel $channel)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param \App\User $user
     * @param \App\Models\Channel\Channel $channel
     * @return mixed
     */
    public function forceDelete(User $user, Channel $channel)
    {
        //
    }
}
