<?php

namespace App\Classes\Shopify;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class Delete extends ConvertToShopifyArray
{

    public function __construct(public array $data)
    {

        parent::__construct($this->data);
    }

    function delete_shopify_product()
    {
        if ($this->shopify_product_id && $this->shopify_base_url) {
            Http::retry(1, 1000)->withHeaders([
                'Content-Type' => 'application/json'
            ])->delete($this->shopify_base_url . "products/" . $this->shopify_product_id . ".json");
        }
    }

    function delete_shopify_product_image()
    {
        if ($this->shopify_product_id && $this->shopify_base_url && $this->shopify_product_image_id) { 
            Http::retry(1, 1000)->withHeaders([
                'Content-Type' => 'application/json'
            ])->delete($this->shopify_base_url . "products/" . $this->shopify_product_id ."/images/".$this->shopify_product_image_id.".json");
        }
    }
}
