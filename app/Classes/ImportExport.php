<?php


namespace App\Classes;

use Apimio\MappingConnectorPackage\classes\formulas\ApplyTemplateOnItem;
use Apimio\MappingConnectorPackage\traits\Formulas;
use App\Classes\Template\GenerateNode;
use App\Exports\ProductsExport;
use App\Imports\ProductsImport;
use App\Jobs\FinalNotificationJob;
use App\Jobs\Import\StoreProductsChunkArray;
use App\Models\Channel\Channel;
use App\Models\Channel\ShopifyChannel;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Template;
use App\Models\Product\Version;
use App\Notifications\ApimioNotification;
use App\Traits\AWSSetting;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;

class ImportExport
{
    use Formulas, AWSSetting;

    public
        $products, $data, $product_obj;

    private $shopify_array = array();
    private $apimio_heading_attributes = array();


    public function __construct()
    {
    }


    /**
     * method for export products to shopify csv template
     *
     * @deprecated this method will be removed in future
     *
     */
    public function export_to_shopify_csv($request, $callback)
    {
        $final_products = [];
        $product_obj = new Product();
        try {
            $version = $request["version"];
            $product_obj->fetch(
                function ($product) use (&$final_products, $version) {
                    return $product->whereHas(
                        'versions',
                        function ($q) use (&$final_products, $version) {
                            $q->where('version_id', $version);
                        }
                    )->chunk(
                        100,
                        function ($products) use (&$final_products) {
                            foreach ($products as $product) {
                                try {
                                    $shopify_product_collection = $this->get_shopify_product_collection($product);
                                    if (count($shopify_product_collection) > 1) {
                                        foreach ($shopify_product_collection as $variants) {
                                            $final_products[] = $variants;
                                        }
                                    } else {
                                        $final_products[] = $shopify_product_collection[0];
                                    }
                                } catch (\Exception $e) {

                                    Log::error($e);
                                    Log::error($product);
                                }
                            }
                        }
                    );
                }
            );

            if ($callback) {
                return $callback($final_products);
            }
        } catch (\Exception $e) {
            Log::error($e);
        }
    }


    /**
     * method for export products to shopify csv template
     *
     * @deprecated this method will be removed in future
     *
     */
    public function export_to_apimio_csv($request, $callback)
    {
        $final_products = [];
        $product_obj = new Product();
        try {
            $version = $request["version"];
            $product_obj->fetch(
                function ($product) use (&$final_products, $version, $product_obj) {
                    return $product->whereHas(
                        'versions',
                        function ($q) use (&$final_products, $version, $product_obj) {
                            $q->where('version_id', $version);
                        }
                    )->chunk(
                        100,
                        function ($products) use (&$final_products, $product_obj) {
                            foreach ($products as $product) {
                                try {
                                    $product = $product_obj->product_fetch($product);
                                    $apimio_product_collection = $this->get_apimio_product_collection($product);
                                    if (count($apimio_product_collection) > 1) {
                                        foreach ($apimio_product_collection as $variants) {
                                            $final_products[] = $variants;
                                        }
                                    } else {
                                        $final_products[] = $apimio_product_collection[0];
                                    }
                                } catch (\Exception $e) {
                                    Log::error($e);
                                    Log::error($product);
                                }
                            }
                        }
                    );
                }
            );

            if ($callback) {
                return $callback($final_products);
            }
        } catch (\Exception $e) {
            Log::error($e);
        }
    }


    /**
     * method for Fetching product brands name
     *
     *
     * @deprecated this method will be removed in future
     *
     */
    public function fetch_brand($product)
    {
        $brands = array();
        if ($product->has('brands')) {
            foreach ($product->brands as $brand) {
                if (isset($brand->name)) {
                    $brands[] = $brand->name;
                }
            }
        }
        return empty($brands) ? null : implode(", ", $brands);
    }


    /**
     * method for Fetching product categories name
     *
     * @deprecated this method will be removed in future
     *
     */
    public function fetch_category($product)
    {
        $categories = array();
        if ($product->has('categories')) {
            foreach ($product->categories as $category) {
                if (isset($category->name)) {
                    $categories[] = $category->name;
                }
            }
        }
        return empty($categories) ? null : implode(", ", $categories);
    }


    /**
     * method for Fetching product vendors name
     *
     * @deprecated this method will be removed in future
     *
     */
    public function fetch_vendor($product)
    {
        $vendors = array();
        if ($product->has('vendors')) {
            foreach ($product->vendors as $vendor) {
                if (isset($vendor->name)) {
                    $vendors[] = $vendor->name;
                }
            }
        }
        return empty($vendors) ? null : implode(", ", $vendors);
    }


    /**
     * method for Fetching product versions name
     *
     *
     * @deprecated this method will be removed in future
     *
     */
    public function fetch_version($product)
    {
        $versions = array();
        if ($product->has('versions')) {
            foreach ($product->versions as $version) {
                if (isset($version->name)) {
                    $versions[] = $version->name;
                }
            }
        }
        return empty($versions) ? null : implode(", ", $versions);
    }


    /**
     * method for Fetching product files link
     *
     *
     * @deprecated this method will be removed in future
     *
     */
    public function fetch_file($product)
    {
        $files = array();
        if ($product->has('files')) {
            foreach ($product->files as $file) {
                if (isset($file->link)) {
                    $files[] = $file->link;
                }
            }
        }
        return empty($files) ? null : implode(", ", $files);
    }


    /**
     * method for making a collection of export csv products
     *
     * @deprecated this method will be removed in future
     *
     */
    public function get_apimio_product_collection($product)
    {
        $temp_product = [];
        $pr_name = '';
        $products_collection = [];
        try {
            $temp_product['Sku-Id'] = isset($product->sku) ? $product->sku : null;
            $temp_product['Category'] = $this->fetch_category($product);
            $temp_product['Brand'] = $this->fetch_brand($product);
            $temp_product['Vendor'] = $this->fetch_vendor($product);
            $temp_product['Version'] = $this->fetch_version($product);
            $temp_product['File'] = $this->fetch_file($product);

            if ($product->has('versions')) {
                foreach ($product->versions as $version) {
                    if (isset($version->families)) {
                        foreach ($version->families as $family) {
                            if ($family->has('attributes')) {
                                foreach ($family->attributes as $attribute) {
                                    if (isset($attribute->name)) {

                                        if ($attribute->name == 'Product Name') {
                                            $pr_name = $attribute->value;
                                        }
                                        $temp_product[$attribute->name] = $attribute->value;

                                        // collecting names for apimio headings
                                        if (!in_array($attribute->name, $this->apimio_heading_attributes)) {
                                            $this->apimio_heading_attributes[] = $attribute->name;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $temp_product['Variant Attribute'] = '';
            $temp_product['Variant Option'] = '';
            $temp_product['Quantity'] = '';
            $temp_product['Product Completeness'] = $product->get_completeness_score() . '%';
            $products_collection[] = $temp_product;
            unset($temp_product);
            if ($product->has('variants')) {
                foreach ($product->variants as $variant) {

                    if (isset($pr_name)) {
                        if (isset($variant->name)) {
                            $temp_pr_name = $pr_name . " " . $variant->name;
                        }
                    }

                    $temp_product = [];
                    $temp_product['Sku-Id'] = isset($variant->sku) ? $variant->sku : null;
                    $temp_product['Category'] = '';
                    $temp_product['Brand'] = '';
                    $temp_product['Vendor'] = '';
                    $temp_product['Version'] = '';
                    $temp_product['File'] = '';
                    $temp_product['Product Name'] = isset($temp_pr_name) ? $temp_pr_name : null;
                    $temp_product['Price'] = isset($variant->price) ? $variant->price : null;
                    $temp_product['Cost Price'] = isset($variant->cost_price) ? $variant->cost_price : null;
                    $temp_product['UPC / Barcode'] = isset($variant->barcode) ? $variant->barcode : null;
                    $variant_options = isset($variant->option) ? json_decode($variant->option) : null;
                    $temp_attribute = [];
                    if (isset($variant_options->attributes)) {
                        foreach ($variant_options->attributes as $attribute) {
                            if (isset($attribute->name)) {
                                $temp_attribute[] = $attribute->name;
                            }
                        }
                    }
                    $temp_attribute = empty($temp_attribute) ? null : implode("-", $temp_attribute);
                    if ($temp_attribute == null) {
                        $temp_product['Variant Attribute'] = $temp_attribute;
                    }

                    $temp_product['Variant Option'] = isset($variant_options->options) ? implode("-", $variant_options->options) : null;
                    $temp_product['Quantity'] = isset($variant->quantity) ? $variant->quantity : null;
                    $products_collection[] = $temp_product;
                    unset($temp_product);
                }
            }
            return $products_collection;
        } catch (\Exception $e) {
            Log::error($e);
        }
    }


    /**
     * method for fetching shopify attribute data
     *
     * @deprecated this method will be removed in future
     *
     * @return array
     */
    public function get_shopify_product_data($product)
    {
        $temp_product['Title'] = isset($product['title']) ? $product['title'] : null;
        $temp_product['Body (HTML)'] = isset($product['body_html']) ? $product['body_html'] : null;
        $temp_product['Vendor'] = isset($product['vendor']) ? $product['vendor'] : null;
        $temp_product['Standard Product Type'] = isset($product['product_type']) ? $product['product_type'] : null;
        $temp_product['Custom Product Type'] = null;
        $temp_product['Tags'] = null;
        $temp_product['Published'] = 'TRUE';
        $temp_product['Status'] = "active";
        $temp_product['Gift Card'] = "FALSE";
        $temp_product['Option1 Name'] = isset($product['options'][0]["name"]) ? $product['options'][0]["name"] : "";
        $temp_product['Option2 Name'] = isset($product['options'][1]["name"]) ? $product['options'][1]["name"] : "";
        $temp_product['Option3 Name'] = isset($product['options'][2]["name"]) ? $product['options'][2]["name"] : "";
        return $temp_product;
    }


    /**
     * method for fetching shopify variants attribute data
     *
     * @deprecated this method will be removed in future
     *
     * @return array
     */
    public function get_shopify_variants_data($product, $variant, $count)
    {
        $temp_product['Handle'] = isset($product['handle']) ? $product['handle'] : null;
        $temp_product['Option1 Value'] = isset($variant["option1"]) ? $variant["option1"] : null;
        $temp_product['Option2 Value'] = isset($variant["option2"]) ? $variant["option2"] : null;
        $temp_product['Option3 Value'] = isset($variant["option3"]) ? $variant["option3"] : null;
        $temp_product['Variant SKU'] = isset($variant["sku"]) ? $variant["sku"] : null;
        $temp_product['Variant Grams'] = "";
        $temp_product['Variant Inventory Tracker'] = "shopify";
        $temp_product['Variant Inventory Qty'] = "";
        $temp_product['Variant Inventory Policy'] = "continue";
        $temp_product['Variant Fulfillment Service'] = "manual";
        $temp_product['Variant Price'] = isset($variant["price"]) ? $variant["price"] : null;
        $temp_product['Variant Compare At Price'] = isset($variant["compare_at_price"]) ? $variant["compare_at_price"] : null;
        $temp_product['Variant Requires Shipping'] = "TRUE";
        $temp_product['Variant Taxable'] = "TRUE";
        $temp_product['Variant Barcode'] = isset($variant["barcode"]) ? $variant["barcode"] : null;
        $temp_product['Image Src'] = isset($product['images'][$count]["src"]) ? $product['images'][$count]["src"] : "";
        $temp_product['Image Position'] = isset($product['images'][$count]["position"]) ? $product['images'][$count]["position"] : "";
        $temp_product['Image Alt Text'] = "";
        $temp_product['SEO Title'] = "";
        $temp_product['SEO Description'] = "";
        $temp_product['Variant Image'] = "";
        $temp_product['Variant Weight Unit'] = "";
        $temp_product['Variant Tax Code'] = "";
        $temp_product['Cost per item'] = "";

        return $temp_product;
    }


    /**
     * method for making a collection of export csv products
     *
     * @deprecated this method will be removed in future
     * @return array
     */
    public function get_shopify_product_collection($product_shopify): array
    {
        $shopify_obj = new ShopifyChannel();
        $product = $shopify_obj->set_array_product($product_shopify->id, $product_shopify->versions[0]->id);
        // convert variants to CSV row
        $count = 0;
        $products_collection = array();

        if (isset($product["variants"])) {
            foreach ($product["variants"] as $key => $variant) {
                $temp_product = array();
                if ($key == 0) {
                    $temp_product = $this->get_shopify_product_data($product);
                }
                // variant columns
                $temp_product = array_merge($this->get_shopify_variants_data($product, $variant, $count), $temp_product);
                array_push($products_collection, $temp_product);
                unset($temp_product);
                $count++;
            }
        } else {
            $temp_product = array();
            $temp_product = $this->get_shopify_product_data($product);
            array_push($products_collection, $temp_product);
            unset($temp_product);
        }
        return $products_collection;
    }


    /**
     * method for getting products attributes heading name for CSV export
     *
     * @deprecated this method will be removed in future
     * @return array
     */
    public function get_apimio_product_attributes_name(): array
    {
        $heading_array = array();
        array_push($heading_array, "Sku-Id");
        array_push($heading_array, "Category");
        array_push($heading_array, "Brand");
        array_push($heading_array, "Vendor");
        array_push($heading_array, "Version");
        array_push($heading_array, "File");

        $heading_array = array_merge($heading_array, $this->apimio_heading_attributes);

        array_push($heading_array, "Variant Attribute");
        array_push($heading_array, "Variant Option");
        array_push($heading_array, "Quantity");
        array_push($heading_array, "Product Completeness");
        return $heading_array;
    }


    /**
     * @deprecated this method will be removed in future
     *
     * @return \Illuminate\Database\Eloquent\Builder|mixed
     */
    public function getProducts()
    {

        return $this->products;
    }


    /**
     * @return array
     */
    public function getShopifyArray(): array
    {
        return $this->shopify_array;
    }


    /**
     * @deprecated this method will be removed in future
     *
     * @param array $shopify_array
     */
    public function is_sku_available(array $headings)
    {
        return in_array("sku", $headings[0]);
    }


    /**
     * @deprecated this method will be removed in future
     * @return array
     */
    public function getHeadingAttributes(): array
    {
        return $this->heading_attributes;
    }

    public function set_data($data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @deprecated this method will be removed in future
     *
     * @return \Illuminate\Validation\Validator
     */
    public function validation_for_CSV_import()
    {
        return Validator::make($this->data, [
            "nodes" => 'required',
            "version" => 'required',
            "status" => 'required',
            "catalog" => 'required'
        ]);
    }


    /**
     * method for fetching distinct variants options values
     *
     * @param array $option which is used to fetch csv variant option column data
     * @param callable $error_callback if we found any error
     * @param callable $success_callback return success callback with distinct option values
     *
     * @deprecated this method will be removed in future
     *
     * @return  array $success_callback with return an distinct option values array
     */
    public function get_distinct_variant_options(array $option, callable $error_callback, callable $success_callback)
    {
        if (is_array($option)) {
            $unique_values = array_unique($option);
            $success_callback($unique_values);
        } else {
            $error_callback('Please provide array data');
        }
    }


    /**
     * method for fetching all the product variants
     *
     * @param string $variant_column_name which differentiate the selected column
     * @param string $variant_value product variants value use to distinguish
     * @param Collection $products pass the product collection from which we want to find their variants
     *
     * @deprecated this method will be removed in future
     *
     * @return  Collection|null return a collection if data exist or return null if param data is empty
     */
    function find_product_variants(string $variant_column_name, string $variant_value, &$products)
    {

        if (isset($variant_column_name) && isset($variant_value) && !empty($products)) {
            $variants_array = array_filter(
                $products,
                function ($collection_keys) use ($variant_value, &$products, $variant_column_name) {
                    if ((string)$products[$collection_keys][$variant_column_name] === (string)$variant_value) {
                        $variant = '';
                        $variant = $products[$collection_keys];
                        unset($products[$collection_keys]);
                        return $variant;
                    }
                },
                ARRAY_FILTER_USE_KEY
            );
        } else {
            $variants_array = null;
        }


        return array_values($variants_array);
    }


    /**
     * method for fetching selected variants with options from csv import file
     *
     * @param Collection $products pass the product collection from which we want to find variant options
     *
     * @deprecated this method will be removed in future
     *
     * @return  array return an array of selected variants attributes
     */
    function get_import_csv_variant_options($product)
    {
        $final_row = array();

        if (isset($this->data['nodes']['variant']['id'])) {
            $final_row['variant_id'] = $product[$this->data['nodes']['variant']['id']];
        }

        $variant_options = array_filter($this->data['nodes']['variant']["variant_options"]);

        if (!empty($variant_options)) {
            try {
                foreach ($this->data['nodes']['variant']["variant_options"] as $key_variant => $variants) {
                    $count = $key_variant + 1;

                    if (isset($variants) && isset($product[$variants])) {
                        $final_row['option_name_' . $count] = $variants;
                        $final_row['option_value_' . $count] = isset($product[$variants]) ? $product[$variants] : null;
                    }
                }
            } catch (\Exception $e) {
                Log::error("variant_options try catch ");
                Log::error($e->getMessage());
            }
        }


        return $final_row;
    }


    /**
     * method for storing template record
     *
     * @param callable $success_callback return success callback
     * @param callable $error_callback if we found any error
     *
     * @deprecated this method will be removed in future
     *
     * @return  object | string object returns template record and string return if error occurs
     */
    function template_store($success_callback, $error_callback)
    {
        $node = new GenerateNode();

        // generating json node
        $payload = $node->generateObjectNode($this->data['nodes']);
        $this->data['payload'] = $payload;

        //creating template
        $template = new Template();
        return $template->set_data($this->data)->store(function ($tem_obj) use ($success_callback) {
            return $success_callback($tem_obj);
        }, function ($error) use ($error_callback) {
            return $error_callback($error);
        });
    }


    /**
     * method for getting csv to array data and create products
     *
     * @param Collection $collection which is used to fetch csv rows data
     * @param callable $error_callback if we found any error
     * @param callable $success_callback return success callback
     *
     *
     * @return  array $success_callback with created all products
     */

    public function convert_CSV_to_apimio(Collection $converted_arrays, $error_callback, $success_callback)
    {
        try {


            $nodes = [];
            $batch_data = $this->data ?? [];
            $jobable = false;
            if (isset($converted_arrays['nodes'])) {
                $nodes = $converted_arrays['nodes'];
                unset($converted_arrays['nodes']);
            }

            if (isset($converted_arrays['incorrect_csv_product_rows']) && count($converted_arrays['incorrect_csv_product_rows']) != 0) {
                $disk = Storage::disk('s3');
                $filename = $batch_data['filename'] ?? null;
//                if ($filename && !$disk->exists($filename)) {
//                    $this->send_notification_for_import(
//                        $batch_data,
//                        true,
//                        "Your CSV file is now being processed by Apimio. We’ll notify you once all product data has been successfully imported"
//                    );
//                }

                $this->save_error_row_in_csv($converted_arrays['incorrect_csv_product_rows'], $this->data);
                unset($converted_arrays['incorrect_csv_product_rows']);
            }

            if (isset($nodes['products'])) {

                $node_products = $nodes['products'];

                // // check if variants attributes id's exist in apimio
                // if (isset($nodes['attributes_with_values'])){
                //     foreach ($nodes['attributes_with_values'] as $key_var_attribute => $variants_attribute){
                //         $attribute = Attribute::withoutGlobalScope('organization_id')->where('name',$variants_attribute['name'])
                //             ->where('attribute_type_id',13)
                //             ->where('organization_id',$converted_arrays['organization_id'])
                //             ->get()->first();

                //         if (isset($attribute)){
                //             $nodes['attributes_with_values'][$key_var_attribute]['id'] = $attribute->id;
                //         }
                //     }
                // }


                // if count of products is greater than 10 then chunk it
                if ($node_products->count() > 5) {
                    $product_chunks = $node_products->chunk(5);
                } else {
                    $product_chunks = collect([$node_products]);
                }

                $chunks_jobs = [];

                foreach ($product_chunks as $key =>$chunk) {
                    //////////////////////////////////////below code is for without queue testing do not remove this code//////////////////////////////////
//                    $this->import_csv_products_chunk($chunk, $converted_arrays, $nodes['attributes_with_values'] ?? [], $this->data);
                    ///////////// product chunks (JOB BATCHING) //////////////
                    $encode_data_s3_url = $this->saveToS3([
                        'chunk' => $chunk ?? [],
                        'converted_arrays' => $converted_arrays ?? [],
                        'attributes_with_values' => $nodes['attributes_with_values'] ?? [],
                        'all_data' => $this->data
                    ], "import_csv_products_chunk".$key."_".time()."_".Str::random(4));


                    $jobable = true;
                    $chunks_jobs[] = new StoreProductsChunkArray($encode_data_s3_url);
                    ///////////// (JOB BATCHING END) //////////////

                }

//                $notification_data = $this->get_notification_for_import($batch_data, true, "Your CSV import is complete! You can now review the newly imported product data in Apimio.");

                $notification_data = [
                    'send_to' => ['email','db'],
                    'method'         => 'App\\Classes\\ImportExport@get_notification_for_import',
                    'method_params'  => [
                        $batch_data,
                        true,
                        "Your CSV import is complete! You can now review the newly imported product data in Apimio."
                    ],
                ];

                $chunks_jobs[] = new FinalNotificationJob($notification_data);

                //if job able is true then start batching
                if ($jobable) {
                    if (env('CUSTOM_QUEUE_WORKER') == 'local') {
                       $batch = Bus::findBatch($batch_data['batch']);
                       $batch?->add($chunks_jobs);
                   }else{
                       $organization_id = $this->data['organization_id'] ?? "";
                       $this->dispatchJobsToFifoQueue($chunks_jobs, $organization_id, $organization_id );
                   }

                }
            }
        } catch (\InvalidArgumentException $e) {
            info('Error in convert_CSV_to_apimio: ');
            info($e->getMessage());
            Log::channel('mapping')->error($e->getMessage());
        }
        return $success_callback('success');
    }


    function save_error_row_in_csv($with_incorrect_rows_data, $data = [])
    {
        $incorrect_csv_product_rows = $with_incorrect_rows_data ?? [];
        $csv_heading = [];
        $first_col = $incorrect_csv_product_rows->first();
        if (isset($first_col)) {
            $csv_heading = $first_col->keys();;
        }
        $csv_heading = !empty($csv_heading) ? $csv_heading->toArray() : [];

        if (isset($with_incorrect_rows_data) && $with_incorrect_rows_data->isNotEmpty()) {
            try {
                $disk = Storage::disk('s3');
                $filename = $data['filename'] ?? null;

                //                if ($disk->exists($filename)) {
//                    $newFile = (new ProductsExport($with_incorrect_rows_data, $csv_heading))->download("my_error_temp_file.xlsx");
//                    $new_array = (new ProductsImport())->toCollection($newFile->getFile());
//
//                    if ($new_array->isNotEmpty()) {
//                        $existingFile = (new ProductsImport())->toCollection($filename, 's3');
//                        $existingFile = $existingFile->first()->merge($new_array->first());
//                        Excel::store(new ProductsExport($existingFile, $csv_heading), $filename, 's3');
//                    }
//                } else {
//                    Excel::store(new ProductsExport(collect($with_incorrect_rows_data), $csv_heading), $filename, 's3');
//                }
                if ($disk->exists($filename)) {
                    $temp_filename = 'temp_error_file.xlsx';
                    Excel::store(new ProductsExport($with_incorrect_rows_data, $csv_heading), $temp_filename, 's3');
                    $new_array = (new ProductsImport())->toCollection($temp_filename, 's3');
                    if ($new_array->isNotEmpty()) {
                        $existingFile = (new ProductsImport())->toCollection($filename, 's3');
                        $mergedFile = $existingFile->first()->merge($new_array->first());
                        Excel::store(new ProductsExport($mergedFile, $csv_heading), $filename, 's3');
                        $disk->delete($temp_filename);
                    }
                } else {
                    // If the file does not exist, store the new data directly to S3
                    Excel::store(new ProductsExport(collect($with_incorrect_rows_data), $csv_heading), $filename, 's3');
                }
            } catch (\Exception $e) {
                Log::error("Error during csv error file processing: " . $e->getMessage());
            }
        }
    }


    function get_notification_for_import($data, $error_file = false, $message_txt = "Your CSV file has been successfully imported.")
    {
        $path = $data['filename'] ?? "";

        Log::debug("ImportProducts Queue success for user " . $data['user']->id);
        $body_message = $message_txt;
        $url = "";


//        if (isset($data['all_data']['request_data']['import_action']) && in_array($data['all_data']['request_data']['import_action'], ["1", "3"])) {
//            if ($data['all_data']['request_data']['temp']['rows_count'] > $data['all_data']['request_data']['temp']['product_limit']) {
//                $body_message .= "<br><br>Your SKU's limit exceed. Please upgrade your <b><a href=" . route('billing') . ">Billing Plan</a></b> to add remaining SKU's<br>";
//            }
//        }

        $disk = Storage::disk('s3');

        info(['$disk->exists($path)', $disk->exists($path),$path]);
        if ($path !== "" && $error_file && $disk->exists($path)) {
            $url = route('download', ['filename' => $path]);
            $body_message .= "<br><br>Your product csv file contain some errors or warnings.
                                          <br>Please copy and paste the URL below into your web browser and
                                          check the first column of downloaded csv: <br><a href='" . $url . "'>" . $url . "</a><br>";
        } else {
            $url = url(route('products.index'));
        }

        $details = [
            'subject' => 'Import CSV Products Queue',
            'greeting' => 'Hi ' . $data['user']->fname,
            'body' => $body_message,
            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
            'actionText' => 'View',
            'actionURL' => $url,
            'user_id' => $data['user']->id,
            'organization_id' => $data['organization_id'],
            'batch' => $data['batch']
        ];

        $data['details'] = $details;
        $data['user_id'] = $data['user']->id ?? null;
        return $data;
    }



    /**
     * send  notification for import batching
     * @param $data
     *
     */
    function send_notification_for_import($data, $error_file = false, $message_txt = "Your CSV file has been successfully imported.")
    {
        $path = $data['filename'] ?? "";

        Log::debug("ImportProducts Queue success for user " . $data['user']->id);
        $body_message = $message_txt;
        $url = "";



//        if (isset($data['all_data']['request_data']['import_action']) && in_array($data['all_data']['request_data']['import_action'], ["1", "3"])) {
//            if ($data['all_data']['request_data']['temp']['rows_count'] > $data['all_data']['request_data']['temp']['product_limit']) {
//                $body_message .= "<br><br>Your SKU's limit exceed. Please upgrade your <b><a href=" . route('billing') . ">Billing Plan</a></b> to add remaining SKU's<br>";
//            }
//        }

        $disk = Storage::disk('s3');
        if ($path !== "" && $error_file) {
            $url = route('download', ['filename' => $path]);
                $body_message .= "<br><br>Your product csv file contain some errors or warnings.
                                          <br>Please copy and paste the URL below into your web browser and
                                          check the first column of downloaded csv: <br><a href='" . $url . "'>" . $url . "</a><br>";
        } else {
            $url = url(route('products.index'));
        }

        $details = [
            'subject' => 'Import CSV Products Queue',
            'greeting' => 'Hi ' . $data['user']->fname,
            'body' => $body_message,
            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
            'actionText' => 'View',
            'actionURL' => $url,
            'user_id' => $data['user']->id,
            'organization_id' => $data['organization_id'],
            'batch' => $data['batch']
        ];


        $notifier = new ApimioNotification($details);

        //un-comment if you want to stop notification in email
        //$notifier->only_db_notify(true);

        if ($error_file) {
            $updatedNotification = $data['user']->notifications()->find($data['notification_id']);
            if (isset($updatedNotification)) {

                $updatedNotification->data = [$details];
                $updatedNotification->save();
            }
        } else {
            $data['user']->notify($notifier);
            return $data['user']->notifications->where('organization_id', $data['organization_id'])->first();
        }
    }


    /**
     * import csv chunk
     * @return void
     */
    function import_csv_products_chunk($products = null, $converted_arrays = [], $attributes_with_values = [], $all_data = [])
    {
        $request_data = $all_data['all_data'] ?? [];
        // Initialize $products as a collection if it is null
        if ($products === null) {
            $products = collect();
        }


        if (isset($converted_arrays['import_flow']) && $converted_arrays['import_flow'] == 'single') {
            $final_products[] = $products;
        } else {
            $final_products = $products;
        }

        foreach ($final_products as $pr_key => $product) {
            // add organization_id in require_setting
            if (!isset($converted_arrays['organization_id'])) {
                Log::channel('mapping')->warning("Organization Id not found in converted array (IMPORT CSV)");
            }

            $request_data["product_handle"] = $pr_key ?? null;
            $request_data["import_flow"] = $converted_arrays['import_flow'] ?? null;


            //convert product and their variants into apimio array format
            $this->get_import_apimio_product_array(
                $product,
                $attributes_with_values,
                $request_data,
                function ($converted_product) use ($request_data, $product, $all_data) {
                    $converted_product['template_method_type'] = $request_data['template_method_type'] ?? null;
                    $product_obj = new Product();

                    $non_convert_csv_product_data = $product ?? collect();

                    $product_obj->convert_to_product(
                        function ($error) {
                            //error callback
                            Log::channel('mapping')->error($error);
                        },
                        function ($success) {
                            // success callback

                        },
                        $converted_product,
                        $non_convert_csv_product_data,
                        $all_data
                    );
                },
                function ($error) {
                    Log::channel('mapping')->error("get array error");
                    Log::channel('mapping')->error($error);
                }
            );
        }
    }


    public function handleCategories(array $require_setting, array $productParent): array
    {
        $processedCategories = [];

        if (isset($productParent['Default']['categories'])) {
            $categories = explode(",", $productParent['Default']['categories']);

            foreach ($categories as $key => $category) {
                $category = str_replace(['/'], '>', $category);
                $subCategories = explode(">", $category);
                $subCategories = array_map('trim', $subCategories);

                $parentCategory = null;

                foreach ($subCategories as $child) {
                    $categoryObj = new Category();

                    if (count($subCategories) > 1) {
                        $categoryExist = $this->checkCategoryExistence($child, $require_setting, $parentCategory);

                        $childData = [
                            'name' => $child,
                            'organization_id' => $require_setting['organization_id'],
                            'category_id' => $parentCategory
                        ];

                        $updateCategoryFlag = true;
                        if ($categoryExist) {
                            if ($parentCategory == null) {
                                $parentCategory = $categoryExist->id;
                                $updateCategoryFlag = false;
                            } else {
                                $childData['id'] = $categoryExist->id;
                            }
                        }

                        if ($updateCategoryFlag) {
                            $categoryObj->set_data($childData)->store(
                                function ($error) {
                                    return $error;
                                },
                                function ($obj) use (&$parentCategory) {
                                    $parentCategory = $obj->id;
                                }
                            );
                        }
                    }

                    $processedCategories[$key] = ['name' => $child];
                }
            }
        }

        return $processedCategories;
    }


    private function checkCategoryExistence($childName, $require_setting, $parentCategory)
    {
        if (isset($require_setting['organization_id'])) {
            return Category::withoutGlobalScopes()
                ->where("organization_id", $require_setting['organization_id'])
                ->where("name", $childName)
                ->first();
        } else {
            return Category::where("name", $childName)->first();
        }
    }


    public function processProductAttributes(array $require_setting, array $productParent, array $defaultFamiliesAttributesList): array
    {
        $processedAttributes = [];
        $seoUrlFlag = false;
        $inventories_with_locationId = [];

        foreach ($productParent as $prKey => $prValue) {

            if (str_contains($prKey, ' -> ')) {
                if (isset($inventories_with_locationId['locations'])) {
                    $inventories_with_locationId['locations'] = array_merge($inventories_with_locationId['locations'], $prValue);
                } else {
                    $inventories_with_locationId['locations'] = $prValue;
                }
                continue;
            }
            if (isset($defaultFamiliesAttributesList[$prKey]) && !empty($defaultFamiliesAttributesList[$prKey])) {
                if (is_iterable($prValue)) {
                    foreach ($prValue as $famAttKey => $famAttVal) {
                        if (in_array($famAttKey, array_keys($defaultFamiliesAttributesList[$prKey]))) {
                            if ($famAttKey == "seo_url") {
                                $seoUrlFlag = true;
                            }

                            $attributeFamily = $this->getAttributeFamily($require_setting, $prKey, $famAttKey);
                            $attributeTypeId = isset($attributeFamily) ? $attributeFamily->attributes->first()->attribute_type_id : "1";

                            if (in_array($attributeTypeId, [4, 13])) {
                                $famAttVal = $this->processAttributeValue($famAttVal);
                            }

                            $processedAttributes[$prKey]['attributes'][] = [
                                'handle' => $famAttKey,
                                'attribute_type_id' => $attributeTypeId,
                                'value' => $famAttVal,
                            ];
                        }
                    }
                }
            }
        }

        // If SEO URL flag is not set, add default SEO URL
        // if (!$seoUrlFlag && isset($productParent['General']['product_name'])) {
        //     $processedAttributes['SEO']['attributes'][] = [
        //         'handle' => "seo_url",
        //         'attribute_type_id' => "11",
        //         'value' => Str::slug($productParent['General']['product_name']),
        //     ];
        // }

        // Reformat the families into keys
        $familiesIntoKeys = collect($processedAttributes)->map(function ($familyAttribute, $familyName) {
            return [
                'name' => $familyName,
                'attributes' => $familyAttribute['attributes']
            ];
        })->values()->all();

        return [
            'families' => $familiesIntoKeys,
            'inventories' => $inventories_with_locationId,
        ];
    }


    //get inventory with location id

    /**
     * @param $require_setting
     * @param $productParent
     * @return array
     *
     * @deprecated this method will be removed in future
     */

    private function getInventoryWithLocationId($require_setting, $productParent)
    {
        $inventories_with_locationId = [];
        if (isset($productParent['Default']['inventory'])) {
            $inventories = explode(",", $productParent['Default']['inventory']);
            foreach ($inventories as $inventory) {
                $inventory = explode(":", $inventory);
                $inventory = array_map('trim', $inventory);
                $location = $inventory[0];
                $quantity = $inventory[1];
                $locationId = $this->getLocationId($require_setting, $location);
                if ($locationId) {
                    $inventories_with_locationId[] = [
                        'location_id' => $locationId,
                        'quantity' => $quantity
                    ];
                }
            }
        }
        return $inventories_with_locationId;
    }

    private function getAttributeFamily($require_setting, $familyName, $attributeHandle)
    {
        return Family::withoutGlobalScopes()->with(['attributes' => function ($q) use ($attributeHandle) {
            $q->where('handle', $attributeHandle);
        }])->where('organization_id', $require_setting['organization_id'])->where('name', $familyName)->first();
    }

    private function processAttributeValue($value)
    {
        $value = str_replace(['/', '|'], ',', $value);
        $value = explode(",", $value);
        return array_map('trim', $value);
    }

    public function processProductFiles($product_files = null, $processedFiles = []): array
    {
        if (isset($product_files)) {
            $pattern = '/,\s*(?=https?:\/\/)/';
            $files = preg_split($pattern, $product_files);

            foreach ($files as $file) {
                if (!empty($file)) {
                    $processedFiles[] = $this->fetch_file_details($file);
                }
            }
        }

        return $processedFiles;
    }


    public function find_variant_option_name(string $variant_value, $mapped_data)
    {
        // Filter the array to keep elements where 'to' contains the variant value
        $filteredData = array_filter($mapped_data, function ($data) use ($variant_value) {
            return isset($data['to']) && in_array($variant_value, $data['to']);
        });

        // Use array_map to extract and process the 'from' field
        $fromValues = array_map(function ($data) {
            $fromParts = explode(',', $data['from'][0]);
            return count($fromParts) > 1 ? trim($fromParts[1]) : null;
        }, $filteredData);

        // Return the first non-null value found or null if none are found
        foreach ($fromValues as $value) {
            if ($value !== null) {
                return $value;
            }
        }

        return null;
    }


    function add_variant_attributes_with_values(array $attributes_with_values = [], array $options_data = [])
    {
        // Transform both arrays into associative arrays with 'name' as the key
        $transformedArray1 = array_column($attributes_with_values, null, 'name');
        $transformedArray2 = array_column($options_data, null, 'name');

        // Iterate through the second array and merge it with the first
        foreach ($transformedArray2 as $name => $item) {
            if (isset($transformedArray1[$name])) {
                // Merge options if name exists in both arrays
                $transformedArray1[$name]['options'] = array_unique(array_merge(
                    $transformedArray1[$name]['options'],
                    $item['options']
                ));
            } else {
                // Add the item from the second array if it's not in the first
                $transformedArray1[$name] = $item;
            }
        }

        // Return the merged array, reindexed with numeric keys
        return array_values($transformedArray1);
    }


    /**
     * method for getting apimio product array from csv
     *
     * @param array $product which is used to convert in apimio format
     * @param array $attributes_with_values which is used to assign attriutes name and values in variants
     * @param integer $version_id pass version id
     * @param integer $catalog_id pass catalog id
     * @param integer $status pass Status
     * @param callback $success_callback returned with product format array
     * @param callback $error_callback returned with error
     *
     *
     * @return  array $success_callback with product formated array
     */
    public function get_import_apimio_product_array(Collection $product, array $attributes_with_values, $require_setting, callable $success_callback, callable $error_callback)
    {
        try {
            $version_id = $require_setting['version'] ?? null;
            $catalog_id = $require_setting['catalog'] ?? null;
            $status = $require_setting['status'] ?? null;
            $import_flow = $require_setting['import_flow'] ?? null;
            $attributes_with_values = [];
            $products = [];

            // Assuming $product is an array
            $lastElement = $product->last() ?? null;

            foreach ($product as $variant) {

                if (!isset($product_parent)) {
                    $product_parent = [];
                }

                if (!isset($product_array)) {
                    $product_array = [];
                }

//                if (!isset($product_array['variants']['data'])) {
//                    $product_array['variants']['data'] = [];
//                }

                // convert variant with formula
                $apply_formula = new ApplyTemplateOnItem();
                $converted_variant = $apply_formula->ConvertItemWithFormula($variant, $require_setting['nodes']['data'], false, collect([]));


                //fetch families and inventories
                //families
                $default_families_attributes_list = [];
                $families_obj = Family::where('organization_id', $require_setting['organization_id'])->get();
                if ($families_obj) {
                    foreach ($families_obj as $family_obj) {
                        $default_families_attributes_list[$family_obj->name] = $family_obj->get_family_attributes();
                    }
                }
                $temp_families_and_inventories = $this->processProductAttributes($require_setting, $converted_variant, $default_families_attributes_list);

                // assign first variant as parent because we need to fetch brands, vendors, categories, files from parent row
                if (empty($product_parent)) {
                    $product_parent = $converted_variant ?? [];

                    //handle
                    $product_array['handle'] = $product_parent['Default']['handle'] ?? null;

                    //sku
                    $product_array['variant_sku'] = $product_parent['Variant']['sku'] ?? null;

                    //Product Status
                    $product_array['status'] = $status;

                    //brands
                    if (isset($product_parent['Default']['brand'])) {
                        $product_array['brands'][] = [
                            'name' => $product_parent['Default']['brand']
                        ];
                    }


                    //vendors
                    if (isset($product_parent['Default']['vendor'])) {
                        $product_array['vendors'][] = [
                            'name' => $product_parent['Default']['vendor']
                        ];
                    }

                    //channels
                    if (is_iterable($catalog_id)) {
                        foreach ($catalog_id as $catalog) {
                            $product_array['channels'][] = [
                                'id' => $catalog,
                            ];
                        }
                    } else {
                        $product_array['channels'][] = [
                            'id' => $catalog_id,
                        ];
                    }

                    //versions
                    $product_array['versions'][] = [
                        'id' => $version_id,
                    ];

                    //categories
                    $productCategories = $this->handleCategories($require_setting, $product_parent);
                    if (isset($product_parent['Default']['categories'])) {
                        $product_array['categories'] = $productCategories;
                    }

                    //attributes
                    $product_array['families'] = $temp_families_and_inventories['families'] ?? [];
                } // parent row end


                //add media files in parent row
                if (isset($converted_variant['Default']["file"])) {
                    $product_array['files'] = $this->processProductFiles($converted_variant['Default']["file"] ?? null, $product_array['files'] ?? []);
                }


                //variants data
                $options = [];
                $values = [];

                // finding variants options name and values
                for ($i = 1; $i < 4; $i++) {
                    if (isset($converted_variant['Variant Option']['option' . $i . '_value'])) {
                        if (!isset($converted_variant['Variant Option']['option' . $i . '_name'])) {
                            $variant_value = "Variant Option,option" . $i . "_value";
                            $converted_variant['Variant Option']['option' . $i . '_name'] = $this->find_variant_option_name($variant_value, $require_setting['nodes']['data']);
                        }
                        $options[] = [
                            'name' => $converted_variant['Variant Option']['option' . $i . '_name'],
                            'options' => [
                                $converted_variant['Variant Option']['option' . $i . '_value']
                            ]
                        ];
                        $values[] = $converted_variant['Variant Option']['option' . $i . '_value'];
                    }
                }
                $attributes_with_values = $this->add_variant_attributes_with_values($attributes_with_values, $options);
                if (!isset($converted_variant['Variant']["name"])) {
                    if (isset($values) && !empty($values)) {
                        $converted_variant['Variant']["name"] = implode("-", $values);
                    }
                }

                //variant files
                $temp_variant_images['files'] = $this->processProductFiles($converted_variant['Variant']["file"] ?? null);

                $temp_variant = [];
                if (isset($values) && !empty($values))
                    $temp_variant['options'] = $values;
                if (isset($converted_variant['Variant']["sku"]))
                    $temp_variant['sku'] = $converted_variant['Variant']["sku"];
                if (isset($converted_variant['Variant']["name"]))
                    $temp_variant['name'] = $converted_variant['Variant']["name"];
                if (isset($converted_variant['Variant']["price"]))
                    $temp_variant['price'] = $converted_variant['Variant']["price"];
                if (isset($converted_variant['Variant']["cost_price"]))
                    $temp_variant['cost_price'] = $converted_variant['Variant']["cost_price"];
                if (isset($converted_variant['Variant']["compare_at_price"]))
                    $temp_variant['compare_at_price'] = $converted_variant['Variant']["compare_at_price"];
                if (isset($converted_variant['Variant']["barcode"]))
                    $temp_variant['barcode'] = $converted_variant['Variant']["barcode"];
                if (isset($converted_variant['Variant']["weight"]))
                    $temp_variant['weight'] = $converted_variant['Variant']["weight"];
                if (isset($converted_variant['Variant']["weight_unit"]))
                    $temp_variant['weight_unit'] = $converted_variant['Variant']["weight_unit"];
                if (!empty($temp_variant_images['files']))
                    $temp_variant['files'] = $temp_variant_images['files'];
                if (isset($temp_families_and_inventories['inventories']) && !empty($temp_families_and_inventories['inventories']))
                    $temp_variant['inventories'] = $temp_families_and_inventories['inventories'];

                if (isset($temp_variant) && !empty($temp_variant)){
                    $product_array['variants']['data'][] = $temp_variant;
                }
                elseif (count($product) > 1){
                    $product_array['variants']['data'][] = $temp_variant;
                }

                if ($variant == $lastElement && $import_flow != 'single') {
                    //assign attributes with their values
                    if (isset($attributes_with_values) && !empty($attributes_with_values)) {
                        $product_array['variants']['attributes'] = $attributes_with_values;
                    }
                    $products['products'][] = $product_array;
                }

                if ($import_flow == 'single') {
                    $products['products'][] = $product_array;
                    unset($product_array);
                    unset($product_parent);
                }
            }

            if ($success_callback) {
                return $success_callback($products);
            }
        } catch (\Exception $e) {
            log::error($e->getMessage());
        }
    }


    /**
     * @param $file
     * @return array
     *
     * @deprecated need to remove all the commented code from this method.
     */
    public function fetch_file_details($file): array
    {

        $temp_file = array();
        if (isset($file) && $file != null) {
            //for dropbox images
            //            if (strpos($file,'dropbox') !== false) {
            //                $components = parse_url($file);
            //
            //                if (isset($components['query'])){
            //                    $file .= "&raw=1";
            //                }
            //                else{
            //                    $file .= "?raw=1";
            //                }
            //            }

            try {
                //                $file_dimension =  getimagesize($file) ?? '';
                //                $header_data = get_headers($file, true);
                //                $size = isset($header_data['Content-Length'])?(int) $header_data['Content-Length']:0;
                //                $sizeInKb = round($size / 1024, 2);
                //
                //                $path = $file;
                //                $path = explode('.',$path);
                //                $ext = end($path);
                //                $ext = strtok($ext, '?');

                $temp_file = [
                    'link' => $file,
                    //                    'width' => $file_dimension['0'] ?? null,
                    //                    'height' => $file_dimension['1'] ?? null,
                    //                    'size' => $sizeInKb ?? null,
                    //                    'ext' => $ext ?? null,

                ];
            } catch (\Exception $e) {
                Log::error(["Fetching image size in import", $e->getMessage()]);
            }
        }

        return $temp_file;
    }


    /**
     * method for default import csv template for all user
     *
     * @param callable $error_callback if we found any error
     * @param callable $success_callback return success callback with success array
     *
     *
     * @return  array $success_callback with return success object
     */
    public function apimio_default_template(callable $error_callback, callable $success_callback)
    {
        $organization_id = Auth::user()->organization_id;
        if (isset($organization_id)) {
            $version = Version::get()->first();
            $channel = Channel::get()->first();
        } else {
            return $error_callback("Please provide organization id");
        }
        if (!isset($version) && !isset($channel)) {
            return $error_callback("Catalog and versions are not found");
        }

        $data = [
            'organization_id' => $organization_id,
            'temp_type' => 'import',
            'nodes' => [
                'data' => [
                    0 => [
                        'from' => [
                            0 => 'Category',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'categories',
                        ],
                    ],
                    1 => [
                        'from' => [
                            0 => 'SKU',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'sku',
                        ],
                    ],
                    2 => [
                        'from' => [
                            0 => 'Product Name',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'product_name',
                        ],
                    ],
                    3 => [
                        'from' => [
                            0 => 'UPC / Barcode',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'upc_barcode',
                        ],
                    ],
                    4 => [
                        'from' => [
                            0 => 'Price',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'price',
                        ],
                    ],
                    5 => [
                        'from' => [
                            0 => 'Compare at Price',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'compare_at_price',
                        ],
                    ],
                    6 => [
                        'from' => [
                            0 => 'Cost Price',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'cost_price',
                        ],
                    ],
                    7 => [
                        'from' => [
                            0 => 'Weight',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'weight',
                        ],
                    ],
                    8 => [
                        'from' => [
                            0 => 'Description',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'description',
                        ],
                    ],
                    9 => [
                        'from' => [
                            0 => 'Vendor',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'vendor',
                        ],
                    ],
                    10 => [
                        'from' => [
                            0 => 'Brand',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'brand',
                        ],
                    ],
                    11 => [
                        'from' => [
                            0 => 'SEO Title',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'seo_title',
                        ],
                    ],
                    12 => [
                        'from' => [
                            0 => 'SEO URL',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'seo_url',
                        ],
                    ],
                    13 => [
                        'from' => [
                            0 => 'SEO Description',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'seo_description',
                        ],
                    ],
                    14 => [
                        'from' => [
                            0 => 'Media',
                        ],
                        'with_formula' => 'assign',
                        'to' => [
                            0 => 'file',
                        ],
                    ],
                ],
                'variant' => [
                    'id' => 'handle Id',
                    'variant_options' => [
                        0 => 'Color',
                        1 => 'Size',
                        2 => NULL,
                    ],
                ],
            ],
            'version' => $version->id,
            'catalog' => $channel->id,
            'status' => '1',
            'temp_status' => 'on',
            'temp_name' => 'Apimio Default',
        ];

        $node = new GenerateNode();
        $payload = $node->generateObjectNode($data['nodes']);
        $data['payload'] = $payload;


        $template_obj = new Template();

        if (isset($data['organization_id'])) {
            $template_obj->organization_id = $data['organization_id'];
        }
        $template_obj->version_id = $data['version'];
        $template_obj->channel_id = $data['catalog'];
        $template_obj->name = $data['temp_name'];
        $template_obj->payload = $data['payload'];
        $template_obj->type = $data['temp_type'];
        $template_obj->product_status = isset($data['status']) ? $data['status'] : false;


        return $success_callback($template_obj);
    }
}
