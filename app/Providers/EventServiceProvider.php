<?php

namespace App\Providers;

use Carbon\Carbon;
use App\Events\LocationEvent;
use App\Events\PostBillingEvent;
use Illuminate\Support\Facades\Event;
use App\Listeners\FetchShopifyProduct;
use Illuminate\Auth\Events\Registered;
use App\Events\ChannelUpdateStatusEvent;
use App\Events\FetchShopifyProductEvent;
use App\Listeners\LocationEventListener;
use App\Events\Product\Media\DeleteEvent;
use App\Listeners\Cashier\HandleWebhookListener;
use Laravel\Cashier\Events\WebhookHandled;
use App\Listeners\ChannelUpdateStatusListener;
use App\Listeners\Product\Media\Deletelistener;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        PostBillingEvent::class => [
            FetchShopifyProduct::class,
        ],
        FetchShopifyProductEvent::class => [
            FetchShopifyProduct::class,
        ],
        DeleteEvent::class => [
            Deletelistener::class,
        ],
        'Illuminate\Auth\Events\Login' => [
            'App\Listeners\UserLoginAt',
        ],
        'Illuminate\Auth\Events\Verified' => [
            'App\Listeners\LogVerifiedUser',
        ],
        LocationEvent::class => [
            LocationEventListener::class,
        ],
        ChannelUpdateStatusEvent::class => [
            ChannelUpdateStatusListener::class,
        ],
        WebhookHandled::class => [
            HandleWebhookListener::class,
        ],

    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

    }
}
