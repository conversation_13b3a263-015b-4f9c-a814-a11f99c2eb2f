<?php

namespace App\Providers;

use Laravel\Cashier\Cashier;
use App\Classes\Plan\PlanClass;
use App\View\Components\AppLayout;
use App\Models\Cashier\Subscription;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use App\Models\Cashier\SubscriptionItem;
use App\Models\Organization\Organization;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment('local', 'staging')) {
            /*  $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);*/
            $this->app->register(\Barryvdh\Debugbar\ServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        URL::forceRootUrl(config('app.url'));
        Paginator::useBootstrap();
        Schema::defaultStringLength(191);
        Cashier::useCustomerModel(Organization::class);
        Cashier::useSubscriptionModel(Subscription::class);
        Cashier::useSubscriptionItemModel(SubscriptionItem::class);
        PlanClass::initialize();
        Inertia::share([
            'authUser' => fn () => Auth::check() ? Auth::user() : null,
            'organization' => fn () => Auth::check() ? Auth::user()->organizations()->find(Auth::user()->organization_id) : null,
        ]);

    }
}
