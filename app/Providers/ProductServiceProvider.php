<?php

namespace App\Providers;

use App\Events\Product\CalculateScore;
use App\Events\Product\CurrencySeparatorUpdated;
use App\Events\Product\ManageAttributes;
use App\Events\Product\ManageBrand;
use App\Events\Product\ManageCategory;
use App\Events\Product\ManageChannels;
use App\Events\Product\ManageFamilyAttributes;
use App\Events\Product\ManageFiles;
use App\Events\Product\ManageInvites;
use App\Events\Product\ManageSeoFields;
use App\Events\Product\ManageVariants;
use App\Events\Product\ManageVersion;
use App\Events\Product\SetSeoDescription;
use App\Events\Product\SetSeoUrl;
use App\Events\Product\SyncToShopify;
use App\Listeners\Product\CalculateScoreListener;
use App\Listeners\Product\CurrencySeparatorUpdatedListener;
use App\Listeners\Product\ManageAttributesListener;
use App\Listeners\Product\ManageBrandListener;
use App\Listeners\Product\ManageCategoryListener;
use App\Listeners\Product\ManageChannelsListener;
use App\Listeners\Product\ManageFamilyAttributesListener;
use App\Listeners\Product\ManageFilesListener;
use App\Listeners\Product\ManageInvitesListener;
use App\Listeners\Product\ManageSeoFieldsListener;
use App\Listeners\Product\ManageVariantsListener;
use App\Listeners\Product\ManageVersionListener;
use App\Listeners\Product\SetSeoDescriptionListener;
use App\Listeners\Product\SetSeoUrlListener;
use App\Listeners\Product\SyncToShopifyListener;
use App\Listeners\Product\TouchProductListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class ProductServiceProvider extends ServiceProvider
{
    public static function trigger($eventArray) {
        $product = null;
        foreach ($eventArray as $eventClass) {
            event($eventClass);
            if (!$product) {
                $product = $eventClass->product;
            }
        }
        if ($product){
            $product->touch();
        }
    }

    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        ManageAttributes::class => [
            ManageAttributesListener::class
        ],

        ManageBrand::class => [
            ManageBrandListener::class
        ],

        ManageCategory::class => [
            ManageCategoryListener::class
        ],

        ManageChannels::class => [
            ManageChannelsListener::class
        ],

        ManageFiles::class => [
            ManageFilesListener::class
        ],

        ManageInvites::class => [
            ManageInvitesListener::class
        ],

        ManageVariants::class => [
            ManageVariantsListener::class
        ],

        ManageVersion::class => [
            ManageVersionListener::class
        ],

        ManageFamilyAttributes::class => [
            ManageFamilyAttributesListener::class
        ],

        ManageSeoFields::class => [
            ManageSeoFieldsListener::class
        ],

        SyncToShopify::class => [
            SyncToShopifyListener::class
        ],

        CurrencySeparatorUpdated::class => [
          CurrencySeparatorUpdatedListener::class
        ],
        CalculateScore::class => [
            CalculateScoreListener::class
        ]
    ];

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
