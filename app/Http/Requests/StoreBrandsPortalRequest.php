<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBrandsPortalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; 
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'prohibited',
            'name' => 'required|string|max:255|unique:brands_portals,name,NULL,id,organization_id,' . (auth()->user()->organization_id??auth()->user()->id),
            'url' => 'nullable|string|min:32|unique:brands_portals,url', // URL must be exactly 32 characters
            'primary_color' => 'nullable|string',
            'status' => 'nullable|in:inactive,active',
            'logo_url' => 'nullable|string',
            'file' => 'nullable|file|mimes:txt,pdf,csv,xlsx,jpg,jpeg,png,gif',
            'store' => 'nullable|array',
            'template' => 'nullable|array',
        ];
    }

    /**
     * Custom error messages for validation.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The name field is required.',
            'url.required' => 'The URL field is required and must be exactly 32 characters.',
            'url.size' => 'The URL must be exactly 32 characters.',
            'file_id.exists' => 'The selected file is invalid.',
        ];
    }
}
