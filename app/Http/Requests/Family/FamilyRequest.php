<?php

namespace App\Http\Requests\Family;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniqueManyToMany;
use App\Models\Product\Family;

class FamilyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Modify the data after validation.
     *
     * This method is called after validation runs.
     *
     * We add the organization_id to the validated data.
     */
    protected function passedValidation()
    {
        $this->merge([
            'organization_id' => $this->user()->id, // Assuming the authenticated user is the sender
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $attributes["id"] = isset($this->data["id"]) ? $this->data["id"] : null;
        // $attributes["organization_id"] =  auth()->user()->id??null;
        return [
            'organization_id' => 'prohibited',
            'name' => 'required |max:255 | regex:/^[a-zA-Z0-9_ -]*$/ | unique:families,name,' . $this->route('family') . ',id,organization_id,' . auth()->user()->id,

            // Attribute IDs are optional, but if provided, they should be an array of existing attribute IDs
            'attribute_ids' => 'nullable|array',  // Ensure attribute_ids is an array
            'attribute_ids.*' => 'exists:attributes,id',  // Ensu
        ];
    }

    /**
     * Get the custom messages for validation errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The name field is required.',
            'name.string' => 'The name must be a string.',
            'name.max' => 'The name may not be longer than 255 characters.',
            'name.regex' => 'The name may only contain letters, numbers, spaces, underscores, and hyphens.',
            'attribute_ids.array' => 'The attribute IDs must be an array.',
            'attribute_ids.*.exists' => 'One or more of the attribute IDs are invalid.',
        ];
    }
}
