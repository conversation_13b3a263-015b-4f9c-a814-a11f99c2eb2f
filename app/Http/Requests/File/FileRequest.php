<?php

namespace App\Http\Requests\File;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\ImageDimension;

class FileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        // Determine the request method
        if ($this->isMethod('POST')) { // Store logic (POST)
            if ($this->has('file') ) {
                $fileType = $this->get('type');

                // Conditional validation based on 'type'
                switch ($fileType) {
                    case 'file':
                        return [
                            'file.*' => ['required', 'mimes:pdf,doc,csv,xlsx,txt', new ImageDimension()],
                        ];

                    case 'product_image':
                        return [
                            'file.*' => ['required', 'mimes:jpg,jpeg,webp,png,svg,gif,bmp,ico', new ImageDimension()],
                        ];

                    default:
                        return [
                            'file.*' => ['required', new ImageDimension()],
                        ];
                }
            } else {      // If 'fileUrl' is provided instead of files
                return [
                    'fileUrl' => ['required', 'string'],
                ];
            }

        }

        if ($this->isMethod('PUT')) { // Update logic (PUT)
            return [
                'name' => ['required', 'string'], // Validate name in update request
            ];
        }

        return [];
    }

    /**
     * Get custom messages for validation errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'file.*.required' => 'The file is required.',
            'file.*.mimes' => 'The file must be of a valid type.',
            'fileUrl.required' => 'The file URL is required when no file is uploaded.',
            'fileUrl.url' => 'The file URL must be a valid URL.',
            'name.required' => 'The name field is required for updates.',
            'name.string' => 'The name must be a valid string.',
        ];
    }
}
