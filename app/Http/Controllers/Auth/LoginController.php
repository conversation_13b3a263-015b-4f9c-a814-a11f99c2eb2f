<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Organization\TeamInvite;
use App\Providers\RouteServiceProvider;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Log;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        // Check if user has team invitations
        $teamInvitation = $this->checkForTeamInvitation($user->email);
        if ($teamInvitation) {
            return $this->handleTeamInvitationLogin($user, $teamInvitation);
        }

        // Default behavior - redirect to intended or home
        return redirect()->intended($this->redirectPath());
    }

    /**
     * Check if user has pending team invitations
     *
     * @param string $email
     * @return TeamInvite|null
     */
    private function checkForTeamInvitation($email)
    {
        return TeamInvite::withoutGlobalScopes()
            ->where('email', $email)
            ->first();
    }

    /**
     * Handle login for users with team invitations
     *
     * @param User $user
     * @param TeamInvite $teamInvitation
     * @return \Illuminate\Http\RedirectResponse
     */
    private function handleTeamInvitationLogin(User $user, TeamInvite $teamInvitation)
    {
        try {
            // Set the organization session for the invited user
            session(['organization_id' => $teamInvitation->organization_id]);

            // The UserLoginAt listener will handle the organization assignment
            // and permission transfer when the Login event is fired

            // Redirect directly to dashboard, skipping onboarding
            return redirect()->route('dashboard')->with('success', 'Welcome back! You have been logged into your organization.');

        } catch (\Exception $e) {
            Log::error('Error handling team invitation login: ' . $e->getMessage());
            // Fallback to normal flow if something goes wrong
            return redirect()->intended($this->redirectPath());
        }
    }

    public function test_users() {
        $users =  User::all();
        echo "<br><br><b>All users have same password 'alialiali'</b> and all users have randomized data uploaded to perform better tests.<br><br>";
        foreach ($users as $user) {
            echo $user->email . " <a href='".route("login", ["email"=>$user->email])."'>Login</a><br>";
        }
    }


}
