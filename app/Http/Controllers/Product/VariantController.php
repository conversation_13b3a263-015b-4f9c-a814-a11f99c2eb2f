<?php

namespace App\Http\Controllers\Product;

use App\Models\Setting;
use Illuminate\View\View;
use Illuminate\Http\Request;
use App\Models\Product\Family;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Version;
use App\Models\Product\Attribute;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Events\ChannelUpdateStatusEvent;
use App\Events\Product\CalculateScore;
use App\Models\Product\AttributeFamilyProductVersion;

class VariantController extends Controller
{
    public function pre_product_edit_data($id, $version_id) {
        $products = new Product();
        $product = $products->fetch(function ($product) use ($id) {
            return $product->findOrFail($id);
        });

        $version = Version::findOrFail($version_id);

        return [
            "product" => $product,
            "version" => $version
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index($id , $version_id)
    {
        $default_families = new Family();
        $default_families = $default_families->fetch_data(function ($family){
            return $family->where('is_default',1)->get();
        });
        $data = $this->pre_product_edit_data($id, $version_id);
        $product = $data["product"];
        $version = $data["version"];
        $current_version = $product->versions->find($version->id);
        if(!$current_version){
            $current_version = $version;
        }
        $families = Family::where("is_default", 0)->get();
        $existing_selected_variant = Variant::where(['product_id' => $id , 'version_id' =>  $version_id ])->pluck('option')->toArray();
        $attributes = Attribute::with('attribute_type','attribute_options')->where('organization_id',auth()->user()->organization_id)->where('attribute_type_id','13')->where('is_default', '!=' , 1)->get();
        $variants = new Variant();
        $selected_variants_id = $variants->filter_already_assigned_variants($existing_selected_variant);

        return view('products.variants.step_one',compact('default_families' ,'attributes','id' , 'product','current_version','families','selected_variants_id'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function stepTwo(Request $request)
    {
        if(!$request->has('attribute_id'))
        {
            return back()->withErrors(['main'=>'Select atleast one attribute.']);
        }
        $id = $request->product_id;
        $version_id = $request->version_id;
        $default_families = new Family();
        $default_families = $default_families->fetch_data(function ($family){
            return $family->where('is_default',1)->get();
        });
        $attributes =null;


        if(is_array($request->attribute_id))
        {
            $attributes = Attribute::with('attribute_type','attribute_options')->whereIn('id',$request->attribute_id)->get();
            $existing_selected_options = Variant::where(['product_id' => $id , 'version_id' =>  $version_id ])->pluck('option')->toArray();
            $variant = new Variant();
            $selected_options = $variant->filter_assigned_options($existing_selected_options);

        }
        $data = $this->pre_product_edit_data($id, $version_id);
        $product = $data["product"];
        $version = $data["version"];
        $current_version = $product->versions->find($version->id);
        if(!$current_version){
            $current_version = $version;
        }
        return view('products.variants.step_two',compact('default_families','attributes','id' , 'product','current_version','selected_options'));

    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function stepThree(Request $request)
    {
        $id = $request->get('id');
        $version_id = $request->version_id;
        $organizationId = Auth::user()->organization_id;
        $default_families = new Family();
        $default_families = $default_families->fetch_data(function ($family){
            return $family->where('is_default',1)->get();
        });
        $variants =  Variant::with('file')->where(['product_id' => $id , 'version_id' => $version_id])->get();
        // $variants =  $variants->set_data(['id'=>$id])->fetch();

        $data = $this->pre_product_edit_data($id, $version_id);
        $product = $data["product"];
        $version = $data["version"];
        $current_version = $product->versions->find($version->id);
        if(!$current_version){
            $current_version = $version;
        }
        $variant_settings = Setting::where('organization_id', $organizationId)->pluck('value', 'key');

        if(sizeof($variants) > 0) {
            return view('products.variants.step_three', compact('default_families', 'variants', 'id', 'product', 'current_version', 'variant_settings'));
        }
        else {
            return redirect()->route('variants.step.one', ['id' => $id, 'version_id' => $version_id, 'product' => $product]);
        }

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->only(['product_id', 'version_id', 'attributes']);
        $data = $data+ ['organization_id' => auth()->user()->organization_id];
        $variant = new Variant();
        return $variant->set_data($data)->save_variant_combinations(
            function ($error) {
                return back()->withErrors(['main' => $error])->withInput();
            },
            function ($id) use ($request) {
                $product = Product::find($request->product_id);
                if (isset($product)) {
                    event(new CalculateScore($product));
                    event(new ChannelUpdateStatusEvent(
                        product: $product,
                        isUpdated: true
                    ));
                }
                return redirect()->route('variants.step.three', ['id' => $id, 'version_id' => $request->version_id]);
            }
        );
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $variant = new Variant();
        return $variant->set_data($request->all())->store(
        // when error
            function ($error) {
                //  dd($error);
                return back()->withInput()->withErrors($error);
            },
            // when success
            function () {
                return back()->withSuccess("Variants Update successfully.");
            });

    }

    /**
     * Remove the specified Variant of the product.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $variant = Variant::with('product')->find($id);

        $product = $variant->product ?? null;
        isset($product) ?  event(new ChannelUpdateStatusEvent(
            product: $product,
            isUpdated: true
        )) : '';

        isset($variant) ? $variant->delete() : '';
        isset($product) ? event(new CalculateScore($product)) : '';

        return back()->withSuccess('Variant deleted successfully.');
    }
    /**
     * Remove the specified image from variant.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function file_delete($id)
    {
        $variant = Variant::with('product')->find($id);

        $product = $variant->product ?? null;
        isset($product) ?  event(new ChannelUpdateStatusEvent(
            product: $product,
            isUpdated: true
        )) : '';

        isset($variant) ? $variant->delete_file($id) : '';
        // not needed when we are deleting the image from variant
        // isset($product) ? event(new CalculateScore($product)) : '';

        return back()->withSuccess('Image deleted successfully.');
    }
    /**
     * Remove all the variants of the specified version of the product.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function all_variant_delete($productId , $version_id)
    {
        $product = Product::find($productId);

        $version = Version::find($version_id);

        if (!$product) {
            return back()->withErrors('Product not found.');
        }
        $variantsToDelete = Variant::where(['product_id' => $productId, 'version_id' => $version_id]);
        if ($variantsToDelete->exists()) {
            event(new ChannelUpdateStatusEvent(
                product: $product,
                isUpdated: true
            ));
            $variantsToDelete->delete();
        }

        foreach ($product->versions()->get() as $version) {
            if($version->variants()->where("product_id", $product ->id)->count() === 0) {
                Product::create_default_variant_with_version($product , $version);
            }
        }
        isset($product) ? event(new CalculateScore($product)) : '';

        return back()->withSuccess('All variants for the specified product deleted successfully.');
    }
}
