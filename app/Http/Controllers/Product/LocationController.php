<?php

namespace App\Http\Controllers\Product;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use App\Models\Location\Location;

use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class LocationController extends Controller
{
    //
    public function index()
    {
        if (Gate::denies('SubscriptionAccess', 'location')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $locations = Location::all();
        return view('locations.index', compact('locations'));
    }

    public function create()
    {
        return view('locations.create');
    }

    public function store(Request $request)
    {
        $organizationId = Auth::user()->organization_id;

        $data = $request->validate([
            'name' => [
                'required',
                Rule::unique('locations')->where(function ($query) use ($organizationId) {
                    return $query->where('organization_id', $organizationId);
                })
            ],
            'address' => 'nullable',
            'apartment' => 'nullable',
            'postal_code' => 'nullable',
            'city' => 'nullable',
            'phone_number' => 'nullable',
            'fulfill_online_orders' => 'boolean',
            'default_location' => 'boolean'
        ]);

        // Ensure that the authenticated user's organization_id is set before creating the location
        $data['organization_id'] = $organizationId;
        try {
            Location::create($data);

            return redirect()->route('locations.index')->withSuccess('Location created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->withErrors(['main' => 'Failed to create location. Please check your input and try again.']);
        }
    }


    public function edit(Location $location)
    {

        return view('locations.edit', compact('location'));
    }

    public function update(Request $request, Location $location)
    {
        $data = $request->validate([
            'name' => 'required',
            'address' => 'nullable',
            'apartment' => 'nullable',
            'postal_code' => 'nullable',
            'city' => 'nullable',
            'phone_number' => 'nullable',
            'fulfill_online_orders' => 'boolean',
            'default_location' => 'boolean', // Allow updating the default_location field
        ]);

        // Handle the checkboxes explicitly, providing a default value of false
        $data['fulfill_online_orders'] = $request->input('fulfill_online_orders', '0');

        // If the location is being set as default
//        if (array_key_exists('default_location', $data) && $data['default_location']) {
//            // Unset default_location for other locations in the same organization
//            Location::where('organization_id', $location->organization_id)
//                ->where('id', '!=', $location->id) // exclude the current location
//                ->update(['default_location' => false]);
//        }

        try {
            $location->update($data);
            return redirect()->route('locations.index')->withSuccess('Location updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->withErrors(['main' => 'Failed to update location. Please check your input and try again.']);
        }
    }





    public function destroy(Location $location)
    {
        // Check if the location is the default location for the organization
        if ($location->default_location) {
            // Get the count of default locations for the organization
            $organizationDefaultCount = Location::where('organization_id', $location->organization_id)
                ->where('default_location', true)
                ->count();

            // Check if there is more than one default location
            if ($organizationDefaultCount <= 1) {
                return redirect()->route('locations.index')->withErrors(['main' => 'An organization must have at least one default location.']);
            }
        }

        // Delete the location
        $location->delete();

        return redirect()->route('locations.index')->withSuccess('Location deleted successfully.');
    }

}
