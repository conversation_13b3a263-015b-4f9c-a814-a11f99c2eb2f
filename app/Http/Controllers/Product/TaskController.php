<?php

namespace App\Http\Controllers\Product;

use App\Http\Controllers\Controller;
use App\Models\Schedule\Task;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TaskController extends Controller
{

    public function index()
    {
        $tasks = Task::withTrashed()->paginate(5);
        return view('products.tasks', compact('tasks'));
    }


    public function store(Request $request)
    {
        (new Task())->store($request);
    }

    public function destroy($id)
    {
        // Find the task by ID
        $task = Task::withTrashed()->findOrFail($id);
        $task->forceDelete();
        return redirect()->route('tasks.index')->with('success', 'Task permanently deleted successfully.');
    }
}
