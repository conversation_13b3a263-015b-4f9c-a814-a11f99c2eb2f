<?php

namespace App\Http\Controllers\Webhooks;

use Illuminate\Http\Request;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Location\Location;
use App\Models\Product\Inventory;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Channel\ChannelLocation;
use App\Classes\Shopify\locationShopify;
use App\Classes\Shopify\SyncInventories;
use App\Jobs\Shopify\Webhooks\Inventory\CreateInventoryLevelJob;

class ShopifyInventoryController extends ShopifyWebhookController
{
    public function createInventory(Request $request)
    {
        Log::channel('webhook')->info("Inventory has been Created");
        return $request->all();
    }

    public function updateInventory(Request $request)
    {
        Log::channel('webhook')->info("Inventory item webhook is Started");
        Log::channel('webhook')->error([$request->all()]);
        $channel = $this->channel();
        if (!$channel) {
            return false;
        }
        $request = $request->all();
        $inventory = Inventory::query()
        ->where([
            'organization_id' => $channel->organization_id,
            'store_connect_id' =>  $request["id"]
        ])->first();

        if($inventory){
            $variant = Variant::find($inventory->variant_id);
            if($variant){
                $variant->cost_price = $request["cost"];
                $variant->save();
            }
        }

        return "success";
    }

    public function deleteInventory(Request $request)
    {
        try {
            Log::channel('webhook')->info("Inventory has been deleted");
            return $request->all();
        } catch (\Exception $e) {
            Log::channel('webhook')->error($e);
        }
    }

    public function update($request)
    {
        try {
            $channel = $this->channel();
            if (!$channel) {
                return false;
            }
            Log::channel('webhook')->info("webhook triggered inventory update");
            $location = $this->getChannelLocationId($channel, $request["location_id"]);
            dispatch(new CreateInventoryLevelJob($channel, $location, $request));
            return "success";
            // $inventory = Inventory::query()
            //     ->where([
            //         'organization_id' => $channel->organization_id,
            //         'store_connect_id' =>  $request["inventory_item_id"]
            //     ]);
            // $inventory_data = $inventory->first();
            // if ($location) {
            //     $inventory = $inventory->where("location_id", $location->id);
            // }else{
            //     Log::channel('webhook')->error(['message' => 'Location not found']);
            // }
            // $inventory = $inventory->first();
            //     if (!$inventory) {
            //         Log::channel('webhook')->error(['message' => 'Inventory not found']);
            //         $inventory = new Inventory();
            //         $inventory->organization_id = $channel->organization_id ?? null;
            //         $inventory->store_connect_id = $request["inventory_item_id"] ?? null;
            //         $inventory->location_id = $location->id ?? null;
            //         $inventory->product_id = $inventory_data->product_id ?? null;
            //         $inventory->variant_id = $inventory_data->variant_id ?? null;
            //         $inventory->store_type = 'shopify';
            //     }
            //     $inventory->available_quantity = $request["available"];
            //     $inventory->save();
            //     $channelLocation = ChannelLocation::find($inventory->location_id);
            //     $connectedChannels = ChannelLocation::where('location_id',$channelLocation->location_id)
            //         ->get();
            //     foreach ($connectedChannels as $key => $location) {
            //             $inventories= Inventory::query()
            //             ->where(['variant_id' => $inventory->variant_id, 'location_id' => $location->id])
            //             ->get();
            //                 foreach($inventories as $invy){
            //                     if($invy && $invy->location_id !== $inventory->location_id){
            //                      (new SyncInventories($invy, $location->channel_id))->setForWebhook();
            //                     }
            //                 }
            //         }


            Log::channel('webhook')->info("Inventory has been updated");
        } catch (\Exception $e) {
            Log::channel('webhook')->error($e);
        }
    }

    public function channel()
    {
        try {
            $shopify_channel = $this->getShopifyChannelDetail();
            if (!$shopify_channel) {
                Log::channel('webhook')->error(['message' => 'Shopify channel not found']);
                return null;
            }
            $channel_id = $shopify_channel->channel_id;
            $channel = $this->getChannelDetails($channel_id);
            if (!$channel) {
                Log::channel('webhook')->error(['message' => 'Apimio channel not found']);
                return null;
            }
            return $channel;
        } catch (\Exception $e) {
            Log::channel('webhook')->error($e);
            return null;
        }
    }

    public function createInventoryLevel(Request $request)
    {
        Log::channel('webhook')->info("webhook create triggered");
        $channel = $this->channel();
        if (!$channel) {
            return false;
        }
        $this->update($request->all());
    }

    public function getChannelLocation($channel, $location_id)
    {
        try {
            $location = Location::query()
                ->where(['organization_id' => $channel->organization_id])
                ->whereHas("channelLocation", function ($query) use ($channel, $location_id) {
                    $query->where(["channel_id" => $channel->id, "store_connect_id" => $location_id]);
                })
                ->first();
                if (!$location) {
                    Log::channel('webhook')->error(['message' => 'Location not found']);
                    return null;
                }
            return $location;
        } catch (\Exception $e) {
            Log::channel('webhook')->error($e);
            return null;
        }
    }
    public function getChannelLocationId($channel, $location_id)
    {
        try {
            $location = ChannelLocation::query()->where(["channel_id" => $channel->id, "store_connect_id" => $location_id])->first();
            if (!$location) {
                Log::channel('webhook')->error(['message' => 'Location not found']);
                return null;
            }
            return $location;
        } catch (\Exception $e) {
            Log::channel('webhook')->error($e);
            return null;
        }
    }
}
