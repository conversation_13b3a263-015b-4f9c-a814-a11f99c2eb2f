<?php

namespace App\Http\Controllers\Api\File;

use App\Http\Controllers\Controller;
use App\Http\Requests\File\FileRequest;
use App\Http\Resources\FileResource;
use Illuminate\Http\Request;
use App\Services\FileService;
use File;

class FileController extends Controller
{
    protected $fileService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(FileService $fileService)
    {
        $this->middleware("auth:sanctum");
        $this->fileService = $fileService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:files,id',
                'type' => 'string|in:img,video,file',
                'should_sync' => 'boolean',
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get files without global scopes
            $files = $user->files();

            // Apply filters if provided
            if ($request->filled("id")) {
                $files->where("id", $request->get("id"));
            }


            if ($request->filled("type")) {
                $files->where("type", $request->get("type"));
            }

            if ($request->filled("should_sync")) {
                $files->where("should_sync", $request->get("should_sync"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedFiles = $files->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedFiles->currentPage(),
                'last_page' => $paginatedFiles->lastPage(),
                'per_page' => $paginatedFiles->perPage(),
                'total' => $paginatedFiles->total(),
            ];

            // Return the response with pagination details
            return $this->successResponse(
                'Files retrieved successfully',
                FileResource::collection($paginatedFiles),
                200,
                $pagination
            );
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve files', $e->getCode() != 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FileRequest $request)
    {
        if ( $request->file('file')) {
            // Prepare the file data
            $fileData = $this->fileService->prepareFileData($request->file('file'), 'public');
        } elseif ($request->get('fileUrl')) {
            $fileData = ['link' => $request->get('fileUrl')];
        }
        //save file in database
        $fileObj =  $request->user()->files()->create($fileData);

        return response([
            'message' => 'File created successfully',
            'file' => new FileResource($fileObj)
        ]);
    }

    /**
     * Attach file to product
     */
    public function AttachFileToProduct(Request $request)
    {
        $request->validate([
            'file_id' => 'required|exists:files,id',
            'product_id' => 'required|exists:products,id',
        ]);

        $file = $request->user()->files()->findOrfail($request->get('file_id'));
        $file->products()->attach($request->get('product_id'), ['uploaded_for' => 'product_image']);
        return response([
            'message' => 'File attached to product successfully',
            'file' => new FileResource($file)
        ]);
    }


    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $file = $request->user()->files()->findOrfail($id);
        return response([
            'message' => 'File retrieved successfully',
            'file' => new FileResource($file)
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FileRequest $request, string $id)
    {
        $file = $request->user()->files()->findOrfail($id);
        $file->update($request->get('name'));
        return response([
            'message' => 'File updated successfully',
            'file' => new FileResource($file)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        $file = $request->user()->files()->findOrfail($id);
        //storage already performed in scopes in model

        $file->delete();
        return response([
            'message' => 'File deleted successfully'
        ]);
    }
}
