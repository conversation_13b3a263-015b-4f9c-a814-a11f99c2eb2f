<?php

namespace App\Http\Controllers\Channel;

use DB;
use Illuminate\Http\Request;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Rules\ShopifyProductSync;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Models\Channel\ShopifyChannel;
use Illuminate\Support\Facades\Validator;
use Apimio\Gallery\Classes\SubscribedPlan;

class ShopifyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $channel_id = $request->get('channels');
        $channel = Channel::with('shopify_channels','products')->findOrFail($channel_id[0]);
        $data['published'] = $channel->products()->where('status',1)->count();
        $data['draft'] = $channel->products()->where('status',0)->count();
        $data['plan'] = (new SubscribedPlan())->GetPlan(Auth::user());
        return view('channel.shopify.index',compact('channel','data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $this->authorize( 'add_shopify' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id]);

        return view("channel.shopify.view",compact('id'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return view("channel.shopify.edit",compact('id'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        return $id;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function syncProduct(Request $request)
    {
//        $validator =Validator::make($request->all(),[
//            'product_id' => ['required', new ShopifyProductSync($request->channel_id[0])],
//        ]);
//       // dd($validator->errors);

        $data = [
            'product_id' => $request->get('product_id'),
            'channel_id' => $request->channel_id[0]
        ];
        $channel = new ShopifyChannel();

        $channel->set_data($data)->sync(
        // for error function
            function($errors){
                return back()->withErrors($errors);
            },
            //for success function
            function(){
                return back()->withSuccess('Product synced successfully.');
            }
        );
        return back();
    }
    public function productFetchConfirmation(){
        $channel= Channel::with('shopify_channel')->first();
        return view('channel.shopify.product_fetching_confirmation',compact('channel'));
    }

    public function productFetch($channel_id){
        $channel = new ShopifyChannel();
        return   $channel->set_data(['channel_id'=>$channel_id])->fetch_product();
    }

    public function disconnectShopify($channel_id){
        DB::beginTransaction();
        try{
            Cache::forget('shopify_webhooks_' . $channel_id);
            $channel = new ShopifyChannel();
            $channel = $channel->set_data(['channel_id'=>$channel_id, 'user_id' => Auth::id()])
                ->disconnect(function($error){
                    return back()->withErrors(['main'=>$error]);
                },function(){
                    return back()->withSuccess('Account disconnect successfully.');
                });
            DB::commit();
            return $channel;
        }catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['main'=>'Something went wrong.']);
        }

    }

    /**
     * @deprecated : this function is not using anywhere
     *
     **/
   public function disconnectWebhookRegister(){
        $shopify =  new ShopifyChannel();
        $shopify_channel_ids= $shopify->pluck('channel_id')->toArray();
        foreach ($shopify_channel_ids as $id) {
           // $shopify->defaultDisconnectShopifyWebhook($id);
        }
        return "done";
    }
}
