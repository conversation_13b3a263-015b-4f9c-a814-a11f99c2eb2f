<?php

namespace App\Http\Resources;

use App\Models\Product\ProductVersion;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PortalProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $file = $this->file;
        if ($file) {
            $file->simple = true;
        }

        $scores = ProductVersion::where('product_id',$this->id)->get();
        $totalScore = 0;
        if ($scores->isEmpty()) {
            $this->version_score = 0;
        } else {
            foreach ($scores as $score) {
                $totalScore += $score->score;
            }

            if ($scores->count() > 1) {
                $this->version_score = $totalScore / $scores->count();
            } else {
                $this->version_score = $scores->first()->score;
            }
        }
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'handle' => $this->sku,
            'product_name' => $this->get_name(),
            'status' => $this->get_status(),
            'sku' => isset($this->variants[0])?$this->variants[0]->sku:null,
            'brand' => isset($this->brands[0])?$this->brands[0]->name:null,
            'category' => isset($this->categories[0])?$this->categories[0]->name:null,
            // 'variants_count' => sizeof($this->variants) == 1 ? 0 : sizeof($this->variants),
            'status_badge' => $this->get_status_style(),
            'version_score' => $this->version_score,
            'file' => new FileResource($this->file),
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
    }
}
