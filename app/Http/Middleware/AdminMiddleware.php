<?php

namespace App\Http\Middleware;

use Closure;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (isset($_SERVER['PHP_AUTH_USER']) && isset($_SERVER['PHP_AUTH_PW']))
        {
            if (($_SERVER['PHP_AUTH_USER'] == 'devs' && $_SERVER['PHP_AUTH_PW'] == 'Management@123') || ($_SERVER['PHP_AUTH_USER'] == 'marketing' && $_SERVER['PHP_AUTH_PW'] == 'Marketing@123'))
            {
                return $next($request);
            }
            else {
                echo "You are not eligible to access these credentials";
                header('WWW-Authenticate: Basic realm="Protected area"');
                header('HTTP/1.0 401 Unauthorized');
                die('Login failed!');
                exit();
            }
        }
        else
        {
            header('WWW-Authenticate: Basic realm="Protected area"');
            header('HTTP/1.0 401 Unauthorized');
            die('Login failed!');
            exit();
        }

    }
}
