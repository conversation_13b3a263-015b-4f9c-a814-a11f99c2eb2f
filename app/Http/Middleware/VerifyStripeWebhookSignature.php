<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Stripe;
use Stripe\WebhookSignature;
use Stripe\Webhook;

class VerifyStripeWebhookSignature
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $endpoint_secret = env('STRIPE_WEBHOOK_SECRET');

        $payload = @file_get_contents('php://input');
        $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
        $event = null;

        try {
            $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
        } catch (\UnexpectedValueException $e) {
            // Invalid payload
            http_response_code(400);
            exit();
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            // Invalid signature
            http_response_code(400);
            exit();
        }


//        if (!config('services.stripe.webhook.secret')) {
//            return $next($request);
//        }
//
//        try {
//
//             \Stripe\WebhookSignature::verifyHeader(
//                $request->getContent(),
//                $request->server('HTTP_STRIPE_SIGNATURE'),
//                config('services.stripe.webhook.secret')
//            );
//        } catch (SignatureVerificationException $e) {
//            return abort(404);
//        }


        return $next($request);
    }
}
