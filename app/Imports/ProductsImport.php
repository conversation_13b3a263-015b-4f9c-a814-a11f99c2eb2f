<?php

namespace App\Imports;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;


class ProductsImport implements ToCollection,WithChunkReading, WithHeadingRow,ShouldQueue,WithCustomCsvSettings
{

    use Importable, SkipsFailures;

    private $data;
    public $count = 0;

    public function __construct($attributes = array()) {
        $this->data = $attributes;
    }

    /**
    * @param Collection $collection
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function collection(Collection $collection)
    {


    }

    public function chunkSize(): int
    {
        return 1000;
    }


    public function getCsvSettings(): array
    {
        return [
            'input_encoding' => 'ISO-8859-1',
//            'delimiter' => ',',
//            'enclosure' => '"',
//            'escape_character' => '\\',
//            'skip_empty_rows' => true,
//            'bom' => false, // Disable BOM detection and removal
        ];

    }


}
