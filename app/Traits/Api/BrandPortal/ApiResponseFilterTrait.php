<?php

namespace App\Traits\Api\BrandPortal;

use App\Classes\ProductFilter;
use Illuminate\Database\Eloquent\Builder;

trait ApiResponseFilterTrait
{
    public function filter($products, $request)
    {
        if ($request->filled("handle")) {
            $products->where("sku", "LIKE", "%" . $request->get("handle")  . "%");
        }

        if ($request->filled("tableSearch")) {
            $searchTerm = $request->get("tableSearch");
            $products->where(function ($query) use ($searchTerm) {
                $query->where("sku", "LIKE", "%" . $searchTerm . "%")
                    ->orWhereHas("variants", function ($query) use ($searchTerm) {
                        $query->where("sku", "LIKE", "%" . $searchTerm . "%")
                            ->orWhere("name", "LIKE", "%" . $searchTerm . "%");
                    })
                    ->orWhereHas("values", function ($query) use ($searchTerm) {
                        $query->where("value", "LIKE", "%" . $searchTerm . "%");
                    });
            });
        }

        // Search by multiple IDs for categories, vendors, brands, and versions
        if ($request->filled("category_ids")) {
            if (is_array($request->category_ids)) {
                $categoryIds = $request->get('category_ids');
            } else {
                $categoryIds = explode(',', $request->get('category_ids'));
            }
            $products->whereHas('categories', function (Builder $q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        if ($request->filled("vendor_ids")) {
            if (is_array($request->vendor_ids)) {
                $vendorIds = $request->get('vendor_ids');
            } else {
                $vendorIds = explode(',', $request->get('vendor_ids'));
            }
            $products->whereHas('invites', function (Builder $q) use ($vendorIds) {
                $q->whereIn('invites.id', $vendorIds);
            });
        }

        if ($request->filled("brand_ids")) {
            if (is_array($request->brand_ids)) {
                $brandIds = $request->get('brand_ids');
            } else {
                $brandIds = explode(',', $request->get('brand_ids'));
            }
            $products->whereHas('brands', function (Builder $q) use ($brandIds) {
                $q->whereIn('brands.id', $brandIds);
            });
        }

        if ($request->filled("version_ids")) {
            if (is_array($request->version_ids)) {
                $versionIds = $request->get('version_ids');
            } else {
                $versionIds = explode(',', $request->get('version_ids'));
            }
            $products->whereHas('versions', function (Builder $q) use ($versionIds) {
                $q->whereIn('versions.id', $versionIds);
            });
        }

        if ($request->filled("channel_ids")) {
            if (is_array($request->channel_ids)) {
                $channelIds = $request->get('channel_ids');
            } else {
                $channelIds = explode(',', $request->get('channel_ids'));
            }
            $products->whereHas('channels', function (Builder $q) use ($channelIds) {
                $q->whereIn('channels.id', $channelIds);
            });
        }

        if ($request->filled("status")) {
            $products->where("status", $request->get("status"));
        }


        // filters
        if ($request->filled("filters")) {
            $filters = new ProductFilter();
            $filters->apply_filters($products, $request->get("filters"));
        }
        return $products;
    }

    public function formatPagination($paginatedProducts)
    {
        // Prepare pagination details
        return  [
            'total' => $paginatedProducts->total(),
            'current_page' => $paginatedProducts->currentPage(),
            'last_page' => $paginatedProducts->lastPage(),
            'first_page' => $paginatedProducts->url(1),
            'previous_page' => $paginatedProducts->previousPageUrl(),
            'next_page' => $paginatedProducts->nextPageUrl(),
            'last_page' => $paginatedProducts->url($paginatedProducts->lastPage()),
            'per_page' => $paginatedProducts->perPage(),
            'from' => $paginatedProducts->firstItem(),
            'to' => $paginatedProducts->lastItem(),
            'total_pages' => $paginatedProducts->lastPage(),
        ];
    }
}
