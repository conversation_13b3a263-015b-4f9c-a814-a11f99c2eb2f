<?php
namespace App\Traits\Api;

trait ApiResponseTrait
{
    /**
     * Success response
     *
     * @param string $message
     * @param mixed $data
     * @param int $code
     * @return \Illuminate\Http\JsonResponse
     */
    protected function successResponse($message = 'Success', $data = null, $code = 200, $pagination = null)
    {
        $response = [
            'message' => $message,
            'data' => $data,
        ];

        // If pagination is present, include it in the response
        if ($pagination) {
            $response['pagination'] = $pagination;
        }

        return response()->json($response, $code);
    }

    /**
     * Error response
     *
     * @param string $message
     * @param int $code
     * @param mixed $error
     * @return \Illuminate\Http\JsonResponse
     */
    protected function errorResponse($message = 'Error', $code = 400, $error = null)
    {
        return response()->json([
            'message' => $message,
            'data' => null,
            'error' => $error,
        ], $code);
    }
}
