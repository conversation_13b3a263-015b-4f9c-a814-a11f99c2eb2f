<?php

namespace App\Traits\Shopify;

use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelProductStatus;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Psr\Log\InvalidArgumentException;

/**
 * Shopify API to get products, categories etc.
 *
 * @test php vendor/bin/phpunit tests/Feature/ShopifyTest.php
 * */
trait ShopifyAPI {

    public $base_url               = null;
    public $api_key                = null;
    public $access_token_manual    = null;
    public $shop_url_manual        = null;
    public static $VERSION     = "2024-07";
    public static $INVENTORY_LIMIT     = 250;

    /**
     * Setup api information before making requests.
     *
     * @param string $access_token access token generated for shop after connection.
     * @param string $shop_url
     *
     * @return self
     * */
    public function setupCredentialsManually(string $access_token, string $shop_url) : self {
        $this->api_key              = env("SHOPIFY_API_KEY", "3bda7be4f9789a1f43d84e3d8f13fc22");
        $this->access_token_manual  = $access_token;
        $this->shop_url_manual      = $shop_url;

        return $this;
    }

    /**
     * create base url to send requests.
     *
     * @return void
     * */
    private function createBaseUrl() : void {
        $this->api_key              = env("SHOPIFY_API_KEY", "3bda7be4f9789a1f43d84e3d8f13fc22");

        if($this->access_token_manual && $this->shop_url_manual) {
            $this->base_url         = "https://".$this->api_key.":".$this->access_token_manual."@".$this->shop_url_manual."/admin/api/".self::$VERSION."/";
        }
        else if(isset($this->access_token) && isset($this->shop)) {
            $this->base_url         = "https://".$this->api_key.":".$this->access_token."@".$this->shop."/admin/api/".self::$VERSION."/";
        }
    }
    /**
     *
     * @deprecated we are not using this method anymore.
     *
    **/
    private function fetch($limit, $headers = null) : object {
        $url = $this->base_url . "products.json?limit=" . $limit;

        if(isset($headers["Link"][0])) {
            preg_match_all('#\bhttps?://[^,\s()<>]+(?:\([\w\d]+\)|([^,[:punct:]\s]|/))#', $headers["Link"][0], $match);

            if (sizeof($match[0]) > 1) {
                $components = parse_url($match[0][1]);
            }
            else {
                $components = parse_url($match[0][0]);
            }
            parse_str($components['query'], $results);
            $page_info = $results['page_info'];

            $url = $url . "&page_info=".$page_info;
        }

        return Http::retry(2, 2000)
            ->get($url);
    }

    /**
     * get products from shopify
     * @deprecated we are not using this method anymore.
     * */
    private function getLimit($limit) {

        if(!$limit) {
            if(method_exists($this, "RemainingProductLimit")) {
                $limit = 250;
            }
            else {
                $limit = 250;
            }
        }
        return $limit;
    }

    /**
     * get products from shopify
     *
     * @param callable $callback
     * @param  int|null  $limit
     * @return bool
     * @deprecated we are not using this method anymore.
     */
    public function getProducts(callable $callback, int $limit = null) : bool {
        try {

            $this->createBaseUrl();

            $limit = $this->getLimit($limit);

            do {
                $l = $limit > 250 ? 250 : $limit ;

                if($l < 1) {
                    break;
                }

                $response = $this->fetch($l, $headers ?? null);

                if($response->successful()) {
                    if($response->json()["products"]) {
                        $headers = $response->headers();
                        $callback($response->json());
                    }
                    else {
                        throw new InvalidArgumentException("Products array not found from shopify payload.");
                    }
                }

                $limit -= $l;

                $response->throw();
            } while(isset($headers["Link"][0]) && strpos($headers["Link"][0], 'next') !== false);

            return true;
        }
        catch (\InvalidArgumentException | Exception $e) {
            Log::error($e);
            return false;
        }
    }


    public function disconnectShopify(){
        try {
            $this->createBaseUrl();

            $res = Http::retry(2, 3)
                ->delete($this->base_url . 'api_permissions/current.json');
            if ($res->successful()) {
                return $res->status();
            } elseif ($res->failed()) {
                return $res->status();
            }
        }
        catch (\InvalidArgumentException | Exception $e) {
            Log::error($e);
            return false;
        }

    }

    /**
     * @param $channel_id
     * @return array
     */
    public function getProductsId($channel_id): array
    {
        $channel_product_id = ChannelProduct::query()
            ->where('channel_id',$channel_id)
            ->get()
            ->pluck('id')
            ->toArray();


        $product_ids_response = ChannelProductStatus::query()
            ->whereIn('channel_product_id',$channel_product_id)
            ->where('type','shopify')
            ->whereNot('response',null)
            ->with('channel_product')
            ->get();
        return $this->getIds($product_ids_response);

    }
    /**
     * get the shopify product id and Apimio product id
     * @param $product_ids_response
     * @return array
     */
    public function getIds($product_ids_response): array
    {
        $ids = array();
        foreach($product_ids_response as $key=>$res){
            // $data = json_decode($res->response);
            $ids[$key]['shopify_product_id'] = $res->response->sync_id;
            $ids[$key]['product_id'] = $res->channel_product?$res->channel_product->product_id:0;
        }
        return $ids;
    }
}
