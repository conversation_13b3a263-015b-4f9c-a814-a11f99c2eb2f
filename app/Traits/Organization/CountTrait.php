<?php
namespace App\Traits\Organization;

use App\Models\Channel\Channel;
use App\Models\Organization\Organization;
use App\Models\Product\Product;
use App\Models\Product\Variant;

Trait CountTrait {
    public function count_products()
    {
        $product_ids = Product::withoutGlobalScope('organization_id')->where('organization_id',$this->id)->pluck('id')->toArray();
        $variants = Variant::whereIn('product_id',$product_ids)->count();
        return count($product_ids) + $variants;
    }

    public function count_catalog()
    {
        return Channel::where('organization_id',$this->id)->count();
    }
}
