<?php
namespace App\Traits\Version;

use App\Models\Invite\Invite;
use App\Models\Organization\File;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\BrandProduct;
use App\Models\Product\CategoryProduct;
use App\Models\Product\Family;
use App\Models\Product\FileProduct;
use App\Models\Product\Product;
use App\Models\Product\ProductVendor;
use App\Models\Product\ProductVersion;
use Illuminate\Support\Facades\DB;

trait VersionScoreTrait {

    public function getScoreByProductId($product_id) : int
    {
        $product_version_id = ProductVersion::where("version_id", $this->id)
            ->where("product_id", $product_id)
            ->pluck("id")
            ->first();

        if(!$product_version_id) {
            return 0;
        }

        $family_ids = Family::withoutGlobalScopes()
            ->where("organization_id", $this->organization_id)
            ->pluck("id")
            ->toArray();

        if(!$family_ids) {
            return 0;
        }

        $attribute_family_ids = AttributeFamily::whereIn("family_id", $family_ids)->pluck('id')->toArray();
        $filled_columns = AttributeFamilyProductVersion::whereIn("attribute_family_id", $attribute_family_ids)
            ->where("product_version_id", $product_version_id)
            ->whereNotNull("value")
            ->count();

        $filled_columns += BrandProduct::where('product_id',$product_id)->count();
        $filled_columns += CategoryProduct::where('product_id',$product_id)->count() > 0 ? 1 : 0;
        $filled_columns += ProductVendor::where('product_id',$product_id)->count() > 0 ? 1 : 0;;

        return (int) ($filled_columns*100)/(count($attribute_family_ids) + 4);
    }

    public function getOverallScore($product_id): int
    {
        $versions = ProductVersion::where('product_id',$product_id)->get();
        if (!$versions) {
            return 0;
        }

        $product_score = 0;
        foreach ($versions as $version) {
            $product_score += $this->getScoreByProductId($version->product_id);
        }
        return ceil(($product_score*100)/(12*100));
    }

    public function getFilledFields($product_id)
    {
        $product_version_id = ProductVersion::where("product_id", $product_id)
            ->pluck("id")
            ->toArray();

        if(!$product_version_id) {
            return 0;
        }
        $family_ids = Family::withoutGlobalScopes()
            ->where("organization_id", $this->organization_id)
            ->where(function ($q) {
                $q->where("name", "General");
                $q->orWhere("name", "SEO");
            })
            ->pluck("id")
            ->toArray();

        if(!$family_ids) {
            return 0;
        }

        $attribute_family_ids = AttributeFamily::whereIn("family_id", $family_ids)->pluck("id")->toArray();

        return   $filled_columns = AttributeFamilyProductVersion::whereIn("attribute_family_id", $attribute_family_ids)
            ->whereIn("product_version_id", $product_version_id)
            ->whereNotNull("value")
            ->count();
    }

    public function getEmptyFields($product_id)
    {
        $product_version_id = ProductVersion::where("product_id", $product_id)
            ->pluck("id")
            ->toArray();

        if(!$product_version_id) {
            return 0;
        }
        $family_ids = Family::withoutGlobalScopes()
            ->where("organization_id", $this->organization_id)
            ->where(function ($q) {
                $q->where("name", "General");
                $q->orWhere("name", "SEO");
            })
            ->pluck("id")
            ->toArray();

        if(!$family_ids) {
            return 0;
        }

        $attribute_family_ids = AttributeFamily::whereIn("family_id", $family_ids)->pluck("id")->toArray();


        return  $empty_columns = AttributeFamilyProductVersion::whereIn("attribute_family_id", $attribute_family_ids)
            ->whereIn("product_version_id", $product_version_id)
            ->whereNull("value")
            ->count();
    }

    public function getFilledFieldsByVersionId($product_id)
    {
        $product_version_id = ProductVersion::where("version_id", $this->id)
            ->where("product_id", $product_id)
            ->pluck("id")
            ->first();

        if(!$product_version_id) {
            return 0;
        }
        $family_ids = Family::withoutGlobalScopes()
            ->where("organization_id", $this->organization_id)
            ->pluck("id")
            ->toArray();

        if(!$family_ids) {
            return 0;
        }

        $attribute_family_ids = AttributeFamily::whereIn("family_id", $family_ids)->pluck("id")->toArray();

        $products = new Product();
        $product = $products->fetch(function ($product) use ($product_id) {
            return $product->findOrFail($product_id);
        });

        $filled_columns = 0;
        foreach ($product->versions->find($this->id)->families as $family)
        {
            foreach($family->attributes as $attribute)
            {
                if ($attribute->is_required)
                {
                    $filled_columns += 1;
                }
                //if attribute type is text
                if ($attribute->attribute_type_id == 1)
                {
                    if(strlen($attribute->value) < 255 )
                    {
                        $filled_columns += 1;
                    }
                }
                //if attribute type is price
                elseif($attribute->attribute_type_id == 2)
                {
                    if(is_numeric($attribute->value))
                    {
                        $filled_columns += 1;
                    }
                }
                //if attribute type is text area
                elseif($attribute->attribute_type_id == 3)
                {
                    if(strlen($attribute->value) < 255)
                    {
                        $filled_columns += 1;
                    }
                }
                //if attribute type is multiselect
                elseif($attribute->attribute_type_id == 4)
                {
                    if (is_array($attribute->value))
                    {
                        $filled_columns += 1;
                    }
                }
            }
        }


        return  $filled_columns = AttributeFamilyProductVersion::whereIn("attribute_family_id", $attribute_family_ids)
            ->where("product_version_id", $product_version_id)
            ->whereNotNull("value")
            ->count();

    }

    public function getEmptyFieldsByVersionId($product_id)
    {
        $product_version_id = ProductVersion::where("version_id", $this->id)
            ->where("product_id", $product_id)
            ->pluck("id")
            ->first();
        if(!$product_version_id) {
            return 0;
        }

        $family_ids = Family::withoutGlobalScopes()
            ->where("organization_id", $this->organization_id)
            ->pluck("id")
            ->toArray();

        if(!$family_ids) {
            return 0;
        }

        $attribute_family_ids = AttributeFamily::whereIn("family_id", $family_ids)->pluck("id")->toArray();

        return  $empty_columns = AttributeFamilyProductVersion::whereIn("attribute_family_id", $attribute_family_ids)
            ->where("product_version_id", $product_version_id)
            ->whereNull("value")
            ->count();
    }

    public function getFieldsStateByVersionId($product_id)
    {
        $products = new Product();
        $product = $products->fetch(function ($product) use ($product_id) {
            return $product->findOrFail($product_id);
        });

        $error = 0;
        $success = 0;
        $families = $product->versions->find($this->id);
        if ($families && $families->families) {
            foreach ($families->families as $family)
            {
                foreach($family->attributes as $attribute)
                {
                    if (count($attribute->value) > 0) { //checking if value is empty
                        $rules = json_decode($attribute->rules, true);

                        //if attribute type is text
                        if ($attribute->attribute_type_id == 1) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is price
                        if ($attribute->attribute_type_id == 2) {

                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //description
                        if (($attribute->attribute_type_id == 3)) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //multiselect
                        if (($attribute->attribute_type_id == 4)) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //date and time
                        if (($attribute->attribute_type_id == 5)) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is measurement
                        if ($attribute->attribute_type_id == 7) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);

                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is rating
                        if ($attribute->attribute_type_id == 8) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);

                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is json
                        if ($attribute->attribute_type_id == 9) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);

                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is boolean
                        if ($attribute->attribute_type_id == 10) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);

                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is url
                        if ($attribute->attribute_type_id == 11) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value,$attribute->handle);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }

                        //if attribute type is color
                        if ($attribute->attribute_type_id == 12) {
                            foreach ($attribute->value as $value) {
                                $res = $this->fields_validation($attribute->attribute_type_id, $rules, $value->value);
                                if (!$res) {
                                    $error += 1;
                                } else {
                                    $success += 1;
                                }
                            }
                        }
                    }
                    else {
                        $error += 1;
                    }
                }
            }


            //checking sku
            if (!$product->sku) {
                $error += 1;
            } else {
                $success += 1;
            }

            BrandProduct::where('product_id',$product->id)->count() > 0 ?  $success += 1 :  $error += 1;
            CategoryProduct::where('product_id',$product_id)->count() > 0 ?  $success += 1 :  $error += 1;
//            DB::table('invite_product')->where('product_id',$product_id)->count() > 0 ?  $success += 1 :  $error += 1;
            return ['success' => $success, "error" => $error];
        }
        else {
            return ['success' => 1, "error" => 14];
        }
    }

    public function productVersionCompletnessPercentage($product_id) {
        $data = $this->getFieldsStateByVersionId($product_id);
        return ceil(($data['success']*100)/($data['success']+$data['error']));
    }

    public function fields_validation($attr_type_id,$rules, $value, $slug = null)
    {
        if (((isset($rules['required']) && empty($value)) || (isset($rules['required']) && strlen($value) > 255)) && ($attr_type_id != 3 && $attr_type_id != 2)) //validating all fields except description
        {
            return false;
        }

        if (!isset($rules['required']) && strlen($value) > 255) //checking if value is not required and characters are greater then 255
        {
            return false;
        }

        if ((!isset($rules['required']) && strlen($value) > 255) && ($attr_type_id == 3 || $attr_type_id == 2)) //checking if description and number field is not required and characters are greater then 255
        {
            return false;
        }

        if ((isset($rules['required']) && strlen($value) > 63000) || (isset($rules['required']) && empty($value)) && $attr_type_id == 3) //description validation
        {
            return false;
        }

        if (isset($rules['type'])) //attribute_type_id 2 validation
        {
            if ((($rules['type'] == 'integer' || $rules['type'] == 'price') || $rules['type'] == 'decimal') && !is_numeric($value)) {
                return false;
            }

            if (isset($rules['max']) && $rules['type'] != 'upc_barcode')
            {
                if ($value > $rules['max']) {
                    return false;
                }
            }

            if (isset($rules['min']) && ($rules['type'] != 'date' && $rules['type'] != 'date_and_time'))
            {
                if ($value < $rules['min']) {
                    return false;
                }
            }

            //date and time validation
            if (($rules['type'] == 'date' || $rules['type'] == 'date_and_time'))
            {
                if((isset($rules['required']) && empty($value))) {
                    return false;
                }
            }
        }

        if (isset($rules['max']) && !isset($rules['type'])) //maximum length validation
        {
            if (strlen($value) > $rules['max']) {
                return false;
            }
        }

        if (isset($rules['min']) && !isset($rules['type'])) //minimum length validation
        {
            if (strlen($value) < $rules['min']) {
                return false;
            }
        }

        if (($attr_type_id == 11 && !empty($value) && !isset($rules['type']))) //url validation
        {
            if ($slug == 'seo_url')  //if field is seo field
            {
                if (!preg_match("/^[a-z0-9]+(?:-[a-z0-9]+)*$/",$value)) {
                    return false;
                }
            } else { //if field is from custom attribute
                if (!str_contains($value,'https')) {
                    return false;
                }
            }
        }
        return true;
    }
}

