<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File as F;

class FileService
{
    /**
     * Get a unique filename with a timestamp and random number
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return string
     */
    public function getFileName($file): string
    {
        return time() . rand(10000000, 99999999) . "_" . $file->getClientOriginalName();
    }

    /**
     * Get the file extension from the uploaded file
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return string
     */
    public function getFileExtension($file): string
    {
        return pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
    }

    /**
     * Get the file width
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return int|null
     */
    public function getFileWidth($file): ?int
    {
        $dimensions = getimagesize($file);
        return $dimensions[0] ?? null;
    }

    /**
     * Get the file height
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return int|null
     */
    public function getFileHeight($file): ?int
    {
        $dimensions = getimagesize($file);
        return $dimensions[1] ?? null;
    }

    /**
     * Store the file in storage and return the file path
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $storageType
     * @param string $fileName
     * @return string
     */
    public function storeFile($file, string $storageType, string $fileName): string
    {
        Storage::disk($storageType)->put("/gallery/" . $fileName, F::get($file));
        return "/gallery/" . $fileName;
    }

    /**
     * Get the file size in kilobytes
     *
     * @param string $storageType
     * @param string $filePath
     * @return float
     */
    public function getFileSizeInKb(string $storageType, string $filePath): float
    {
        return round(Storage::size($storageType . $filePath) / 1024, 2);
    }

    /**
     * Determine the file type based on its extension
     *
     * @param string $fileExtension
     * @return string
     */
    public function getFileType(string $fileExtension): string
    {
        $types = [
            'img' => ['jpg', 'jpeg', 'png', 'gif'],
            'video' => ['mp4', 'wmv'],
            'file' => ['txt', 'pdf', 'csv', 'xlsx'],
        ];

        foreach ($types as $key => $extensions) {
            if (in_array(strtolower($fileExtension), $extensions)) {
                return $key;
            }
        }

        return 'unknown';
    }

    /**
     * Prepare file data for database entry
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $storageType
     * @return array
     */
    public function prepareFileData($file, $storageType): array
    {
        $fileName = $this->getFileName($file);
        $fileExtension = $this->getFileExtension($file);
        $filePath = $this->storeFile($file, $storageType, $fileName);

        return [
            'name' => $fileName,
            'link' => url('/' . $storageType . $filePath),
            'ext' => $fileExtension,
            'width' => $this->getFileWidth($file),
            'height' => $this->getFileHeight($file),
            'size' => $this->getFileSizeInKb($storageType, $filePath),
            'type' => $this->getFileType($fileExtension),
            'organization_id' => auth()->user()->organization_id ?? auth()->user()->id,
            'storage_path' => $filePath,
        ];
    }


}
