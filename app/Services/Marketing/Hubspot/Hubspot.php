<?php

namespace App\Services\Marketing\Hubspot;

use App\User;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Support\Facades\Log;
use Psy\Util\Json;

class Hubspot
{
    private User $user;
    private Client $client;
    private bool $verified = true;

    public function __construct(User $user)
    {
        $this->user = $user;
        $apiKey = env("HUBSPOT_ACCESS_TOKEN");
        $this->client = new Client([
            'base_uri' => 'https://api.hubapi.com/crm/v3/',
            'headers' => [
                'Authorization' => "Bearer ".$apiKey,
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    public function notVerified() : static
    {
        $this->verified = false;
        return $this;
    }

    public function verified() : static
    {
        $this->verified = true;
        return $this;
    }

    /**
     * Create deal for user with user email
     * @return string
     */
    public function createDealAndGetId(): string
    {
        try {
            $response = $this->client->post("objects/deals", [
                'json' => [
                    'properties' => [
                        'amount' => "50",
                        "hubspot_owner_id" => "430008351", // Owner id for Sarah
                        "dealname" => $this->user->fname,
                        "dealstage" => "appointmentscheduled",
                        "pipeline" => "default"
                    ],
                ],
            ]);

            $deal = json_decode($response->getBody(), true);
            return $deal["id"];
        } catch (ClientException $e) {
            // Guzzle ClientException handles HTTP 4xx errors
            Log::error($e);
        } catch (ServerException $e) {
            // Guzzle ServerException handles HTTP 5xx errors
            Log::error($e);
        } catch (GuzzleException $e) {
            // Handle other Guzzle exceptions
            Log::error($e);
        }

        return "";
    }

    /**
     * @return string
     */
    public function createContactAndGetId() : string
    {
        $source = "manual";
        if (!empty($this->user->google_id)) {
            $source = "google";
        } elseif ($this->user->shopify_shop_id) {
            $source = "shopify";
        }

        // Create the contact
        try {
            $response = $this->client->post("objects/contacts", [
                'json' => [
                    'properties' => [
                        'firstname' => $this->user->fname,
                        'lastname' => $this->user->lname,
                        'email' => $this->user->email,
                        'phone' => $this->user->phone,
                        'email_verified' =>  $this->verified,
                        "hubspot_owner_id" => "430008351", // Owner id for Sarah
                        "signup_source" => $source,
                        "hs_marketable_status" => true
                    ],
                ],
            ]);

            $contact = json_decode($response->getBody(), true);
            return $contact["id"];
        } catch (ClientException $e) {
            // Guzzle ClientException handles HTTP 4xx errors
            Log::error($e);
        } catch (ServerException $e) {
            // Guzzle ServerException handles HTTP 5xx errors
            Log::error($e);
        } catch (GuzzleException $e) {
            // Handle other Guzzle exceptions
            Log::error($e);
        }

        return "";
    }

    /**
     * Create hubspot contact
     * @return void
     */
    public function create(): void
    {
        try {
            $contactId = $this->createContactAndGetId();
            $dealId = $this->createDealAndGetId();
            $apiKey = env("HUBSPOT_ACCESS_TOKEN");
            $this->client = new Client([
                'base_uri' => 'https://api.hubapi.com/crm-associations/v1/',
                'headers' => [
                    'Authorization' => "Bearer ".$apiKey,
                    'Content-Type' => 'application/json',
                ],
            ]);
            Log::debug($contactId."|". $dealId);
            $response = $this->client->put("associations", [
                    'json' => [
                        'fromObjectId' => $contactId,
                        'toObjectId' => $dealId,
                        "category" => "HUBSPOT_DEFINED",
                        "definitionId" => 4
                    ],
                ]);

            Log::debug(json_decode($response->getBody(), true));
        } catch (ClientException $e) {
            // Guzzle ClientException handles HTTP 4xx errors
            Log::error($e);
        } catch (ServerException $e) {
            // Guzzle ServerException handles HTTP 5xx errors
            Log::error($e);
        } catch (GuzzleException $e) {
            // Handle other Guzzle exceptions
            Log::error($e);
        }
    }

    /**
     * Update contact
     * @param $property
     * @param $value
     * @return void
     */
    public function update($property, $value)
    {
        // Create the contact
        try {
            $this->client->patch("objects/contacts/".$this->user->email."?idProperty=email", [
                'json' => [
                    'properties' => [
                        $property => $value
                    ],
                ],
            ]);
        } catch (ClientException $e) {
            // Guzzle ClientException handles HTTP 4xx errors
            Log::error($e);
        } catch (ServerException $e) {
            // Guzzle ServerException handles HTTP 5xx errors
            Log::error($e);
        } catch (GuzzleException $e) {
            // Handle other Guzzle exceptions
            Log::error($e);
        }
    }
}
