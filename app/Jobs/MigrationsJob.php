<?php

namespace App\Jobs;

use App\Events\Product\CalculateScore;
use App\Models\Product\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MigrationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
//        $products = Product::query()->limit(11747,500)->get();
//        foreach ($products as $product) {
//            event(new CalculateScore($product));
//            info(['product_id' => $product->id]);
//        }
        $attribute_product_family_versions = \App\Models\Product\AttributeFamilyProductVersion::whereNull('product_id')->limit(35000)->get();
        foreach ($attribute_product_family_versions as $attribute_product_family_version) {
            $product_version = \Illuminate\Support\Facades\DB::table("product_version")->find($attribute_product_family_version->product_version_id);
            $attribute_product_family_version->product_id = $product_version->product_id;
            $attribute_product_family_version->version_id = $product_version->version_id;

            $product_version = \Illuminate\Support\Facades\DB::table("attribute_family")->find($attribute_product_family_version->attribute_family_id);
            $attribute_product_family_version->family_id = $product_version->family_id;
            $attribute_product_family_version->attribute_id = $product_version->attribute_id;

            $attribute_product_family_version->save();
        }
    }
}
