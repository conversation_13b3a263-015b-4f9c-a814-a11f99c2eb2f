<?php

namespace App\Jobs\Bulk;

use App\Classes\Bulk\BulkEdit;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class BulkAssignQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $request_data)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $data = $this->request_data;
            (new BulkEdit())->set_data($data)->products_bulk_assign(
                function ($error) use ($data){
                    Log::error("Bulk Assign Queue error for user ". $data['required_data']['user']->id);
                    Log::error($error);
                },
                function ($success){
                    Log::error($success);
                }
            );
        }catch(\Exception $e){
            Log::ERROR("Bulk Assign Main Queue issue");
            Log::ERROR($e->getMessage());
        }
    }
}
