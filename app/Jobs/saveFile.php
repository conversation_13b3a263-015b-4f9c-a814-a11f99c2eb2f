<?php

namespace App\Jobs;

use App\Models\Organization\File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class saveFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $detail;
    private $img = ['jpg', 'jpeg', 'png', 'gif'];
    private $video = ['mp4', 'wmv'];
    private $file = ['txt', 'pdf', 'csv', 'xlsx'];

    public function __construct($detail)
    {
        $this->detail = $detail;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
//        $file_extension = pathinfo($this->detail['file']->getClientOriginalName(), PATHINFO_EXTENSION);
        $obj = new File();
        $obj->organization_id = 1;
        $obj->link = pathinfo($this->detail['file']->getClientOriginalName(), PATHINFO_FILENAME);
//        $obj->ext = $file_extension;
//        if (in_array($file_extension, $this->img) == 'true') {
//            $obj->type = 'img';
//        } elseif (in_array($file_extension, $this->video) == 'true') {
//            $obj->type = 'video';
//        } elseif (in_array($file_extension, $this->file) == 'true') {
        $obj->type = 'file';
      //  }
        $obj->save();
        DB::commit();
//        $file->move(storage_path('app/public'), pathinfo($file->getClientOriginalName(), PATHINFO_BASENAME));
    }
}
