<?php

namespace App\Jobs\Shopify;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Product\Inventory;
use Illuminate\Support\Facades\Log;
use App\Models\Notification\ErrorLog;
use Illuminate\Queue\SerializesModels;
use App\Classes\Shopify\SyncInventories;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class SyncQuantityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $data)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $inventories = Inventory::where("product_id",$this->data["product_id"])
            ->whereNotNull("location_id")
            ->whereNotNull("store_connect_id")
            ->whereRelation("channelLocation" , "channel_id" , $this->data["channel_id"])
            ->get();

            $error = "<ul>";
            $error_check = 0;
            foreach ($inventories as $inventory) {
                $response = (new SyncInventories($inventory, $this->data["channel_id"]))->set();
                if (isset($response)) {
                    if ($response->successful()) {
                        Log::channel("shopify")->info("Inventory sync Success");
                    } else {
                        $json =  $response->json();
                        Log::channel("shopify")->info("Inventory sync Error");
                        Log::channel("shopify")->error($response->json());
                        $error_check++;
                        if (!is_array($json["errors"])) {
                            if ($inventory->variant_id != null) {
                                $error .= '<li><b> variant name : ' . $inventory->variant->name .  '  </b> with <b> location : </b> ' . $inventory->location->name . ' ,  <b> Error :</b>  ' . $json["errors"] .  "</li>";
                            } else {
                                $error .= '<li><b> product sku : ' . $inventory->product->sku .  '  </b> with <b> location : </b> ' . $inventory->location->name . ' ,  <b> Error :</b>  ' . $json["errors"] .  "</li>";
                            }
                        } else {
                            foreach ($json["errors"] as $error_row) {
                                if (is_array($error_row)) {
                                    if ($inventory->variant_id != null) {
                                        $error .= '<li><b> variant name : ' . $inventory->variant->name . '  </b> with <b> location : </b> ' . $inventory->location->name . ' ,  <b> Error :</b>  ' . implode("<br>", $error_row) . "</li>";
                                    } else {
                                        $error .= '<li><b> product sku : ' . $inventory->product->sku .  '  </b> with <b> location : </b> ' . $inventory->location->name . ' ,  <b> Error :</b>  ' . implode("<br>", $error_row) .  "</li>";
                                    }
                                } else {
                                    if ($inventory->variant_id != null) {
                                        $error .= '<li><b> variant name : ' . $inventory->variant->name .  '  </b> with <b> location : </b> ' . $inventory->location->name . ' ,  <b> Error :</b>  ' . $error_row .  "</li>";
                                    } else {
                                        $error .= '<li><b> product sku : ' . $inventory->product->sku .  '  </b> with <b> location : </b> ' . $inventory->location->name . ' ,  <b> Error :</b>  ' . $error_row .  "</li>";
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if ($error_check > 0) {
                $error .= "</ul>";
                $data = [
                    'organization_id' => $inventory->organization_id,
                    'description' => "There is an error in inventories while syncing product back to shopify with <b> sku : {$inventory->product->sku} </b>.<br>  {$error}",
                    'type' => 'shopify syncing',
                    'link' => route('products.edit', $inventory->product_id),
                    'link_text' => 'View Product',
                    'status' => 'error',
                ];

                $error = new ErrorLog();
                $error->setData($data)->store(function ($error) {
                    Log::channel('shopify')->info('Error in saving ErrorLogs.');
                    Log::channel('shopify')->error($error);
                }, function () {
                });
            }
        } catch (\Exception $e) {
            Log::channel('shopify')->error($e);
        }
    }
}
