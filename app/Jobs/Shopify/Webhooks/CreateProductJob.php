<?php

namespace App\Jobs\Shopify\Webhooks;

use App\Classes\Shopify\Metafield;
use App\Classes\Shopify\RetrieveCollection;
use Ghazniali95\ShopifyConnector\App\Classes\Rest\Admin2023_10\Product as ShopifyProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public $channel_id, public $organization_id, public $request)
    {
        //
    }


    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        $product = new ShopifyProduct();
        $product->initializeSession(["channel_id" => $this->channel_id]);
        $response = $product::find($product->session, $this->request['id']);

        if ($response) {
            // Directly assign the returned value from the store method to $product_id
            $product_id = (new \App\Classes\Shopify\StoreProduct($response, $this->channel_id, $this->organization_id))
                ->store(function ($obj) {
                    Log::channel('webhook')->info('Product OBJ Now');
                    Log::channel('webhook')->error($obj);
                    return $obj->id; // Ensure this is the ID you want
                });
        }

        // Ensure product_id is used after being potentially set
        if ($product_id !== null) {
            // for meta-fields and collection saving process
            $ids = [
                'shopify_product_id' => $this->request['id'],
                'product_id' => $product_id
            ];
            Log::channel('webhook')->info('Categories and Metafields');
            Log::channel('webhook')->error($ids);

            // Proceed with operations that depend on $product_id
            (new Metafield($this->channel_id, $this->organization_id))->getMetaFields($ids);
            $product_ids[] = $ids; // Assuming this is declared elsewhere
            (new RetrieveCollection($this->channel_id, $this->organization_id))->getCollectProduct($product_ids);
        } else {
            Log::channel('webhook')->error('Failed to create or retrieve product ID.');
        }
    }
}
