<?php

namespace App\Jobs\Shopify\Webhooks\Location;

use Illuminate\Bus\Queueable;
use App\Models\Location\Location;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class UpdateLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $channel;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct($channel, $request)
    {
        $this->channel = $channel;
        $this->request = $request;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        if(!$this->channel){
            Log::channel('webhook')->error(['message' => 'Apimio channel not found']);
        }
        $data = array();
        $data['name'] = $this->request['name'] ?? null;
        $data['address'] = $this->request['address1'] ?? null;
        $data['apartment'] = $this->request['address2'] ?? null;
        $data['postal_code'] = $this->request['zip'] ?? null;
        $data['city'] = $this->request['city'] ?? null;
        $data['fulfill_online_orders'] = 1 ?? null;
        $data['phone_number'] = $this->request['phone'] ?? null;
        $data['default_location'] = false;
        $data['store_type'] = 'shopify';
        $data['store_connect_id'] = $this->request['id'] ?? null;
        $data['organization_id'] = $this->channel->organization_id ?? null;
        $location = Location::query()
        ->where(['organization_id' => $this->channel->organization_id])
        ->whereHas("channelLocation", function($query) {
            $query->where(["channel_id" => $this->channel->id , "store_connect_id" => $this->request['id'] ?? null]);
        })
        ->first();
        if ($location) {
            $data['id'] = $location->id  ?? null;
            if ($location->default_location == 1) {
                $data['default_location'] = true;
            }
            event(new \App\Events\LocationEvent($this->channel, [$data], true));
            Log::channel('webhook')->info("Location has been updated");
        } else {
            event(new \App\Events\LocationEvent($this->channel, [$data]));
            Log::channel('webhook')->info("Location has been created");
        }
    }
}
