<?php

namespace App\Jobs\Shopify;

use App\Classes\Shopify\Metafield;
use App\Traits\AWSSetting;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class FetchMetaFields implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, AWSSetting;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $event)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $event =  $this->event;

        $metafields = (new Metafield($event->channel_id, $event->organization_id));
        $product_ids = $metafields->getProductsId();
        foreach($product_ids as $id){
            if(env('CUSTOM_QUEUE_WORKER') == 'local'){
                $this->batch()->add(new SaveFetchMatefields($id,$event));
            }else{
                $jobs = [
                    new SaveFetchMatefields($id,$event)
                ];
                $this->dispatchJobsToFifoQueue($jobs, $event->organization_id,$event->organization_id);
            }
        }

    }
}
