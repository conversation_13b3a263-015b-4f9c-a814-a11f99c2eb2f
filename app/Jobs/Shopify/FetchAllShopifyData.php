<?php

namespace App\Jobs\Shopify;

use App\Jobs\FinalNotificationJob;
use App\Models\Channel\ShopifyChannel;
use App\Notifications\ApimioNotification;
use App\Traits\AWSSetting;
use App\User;
use DB;
use Ghazniali95\ShopifyConnector\App\Classes\Services\ProductService;
use Illuminate\Bus\Batch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class FetchAllShopifyData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels , AWSSetting;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $data)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {


      /*  info('queue started');
        $host = 'apimio1.clsqeeg2qimy.us-east-2.rds.amazonaws.com';
        $ip = gethostbyname($host);
        error_log("Resolved IP address of RDS host {$host} is {$ip}");
        try {
            Log::info('Attempting to connect to the database.');
            info('host_name: '. env('DB_HOST'));
            User::query()->first();
            info('Database connection successful.');
        } catch (\Exception $e) {
            Log::error('Could not connect to the database. Please check your configuration.');
            Log::error('Exception: ' . $e->getMessage());
        }
        $user = User::query()->first();
        info('user found');*/
        $event = (object) $this->data;
       /* info([$event]);
        info([$event->channel_id]);
        info('event data: '. json_encode($event));*/
        $channel = ShopifyChannel::query()->where('channel_id', $event->channel_id)->first();
      //  info('channel found');
        if ($channel) {
            info('channel found 112');
            // for getting products from shopify and saving in an array
            $batchArray = [];
            $count = 0;
            info('count:' . $count);
            info(['event'=>$event, 'channel_id'=>$event->channel_id]);
            //fetch and save PRODUCTS
            $productService = new ProductService($event->channel_id);
            info(['productService'=>$productService]);
            $productService->getAll(function ($products) use ($event, &$batchArray, &$count) {
                info('count1:' . $count);
                // Chunk the products into groups of 5
                $productChunks = array_chunk($products, 1);
                foreach ($productChunks as $chunk) {
                    info('count2:' . $count);
                    $processableProducts = [];
                    foreach ($chunk as $product) {
                        $processableProducts[] = $product;
                    }
                    info('count starts,'. $count++);
                    if (!empty($processableProducts)) {
                        $s3Url = $this->saveToS3($processableProducts, 'shopify-products-' . $count.'-'. $event->channel_id);
                        \Log::info('s3Url: ' . $s3Url);
                        $batchArray[] =  new \App\Jobs\Shopify\FetchShopifyProduct($s3Url, $event);
                    }
                }
            }, ['limit' => 250]);

            if(!empty($batchArray)) {
                $path = route('products.index');
                $notification_deatils = [
                    'organization_id' => $event->organization_id,
                    'user_id' => $event->user_id,
                    'details' => [
                        'subject' => 'Shopify Products import to Apimio successfully.',
                        'greeting' => 'Hi ',
                        'body' => "Your import Shopify products queue is processed successfully. Please upgrade plan to add more products.",
                        'thanks' => 'Thank you for using ' . request()->getHttpHost(),
                        'actionText' => 'View',
                        'actionURL' => url($path),
                        'user_id' => $event->user_id,
                        'organization_id' => $event->organization_id,
                    ],
                    'email_notification' => false
                ];
                $batchArray[] = new FetchMetaFieldDefinitions($event);
                $batchArray[] = new FetchCollections($event);
                $batchArray[] = new LocationJob($event);
                $batchArray[] = new FetchMetaFields($event);// need to work on this to remove batching
                $batchArray[] = new UpdateInventoryJob($event); // need to work on this to remove batching
                $batchArray[] = new FinalNotificationJob($notification_deatils);

                info('Batching started.');
                $this->dispatchJobsToFifoQueue($batchArray, $event->organization_id, $event->organization_id);

                /*              $batchArray[] = [
                                  new FetchMetaFieldDefinitions($event),
                                  new FetchMetaFields($event),  // need to work on this to remove batching
                                  new FetchCollections($event),
                                  new LocationJob($event),
                                  new UpdateInventoryJob($event), // need to work on this to remove batching
                                  new FinalNotificationJob($notification_deatils)
                            ];*/


            }



        }
        info('queue ended');
    }
}
