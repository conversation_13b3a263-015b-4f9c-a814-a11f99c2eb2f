<?php

$envPath = __DIR__ . '/../.env';

if (file_exists($envPath)) {
    $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env = [];

    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $env[$name] = $value;
    }

    // Fetch APP_ENV value
    $appEnv = $env['APP_ENV'] ?? 'not set';

    echo "APP_ENV is: " . $appEnv . "\n";
} else {
    echo ".env file not found.\n";
}

if ($appEnv == 'local') {
    // Execute the npm commands
    echo "Executing NPM install and build...\n";
    exec('npm install && npm run build');
} else {
    echo "Skipping NPM install and build on local environment.\n";
}
