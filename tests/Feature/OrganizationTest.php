<?php

namespace Tests\Feature;

use App\Models\Product\AttributeOption;
use App\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class OrganizationTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        // seed the database
    }

    /**
     * Test verification.
     *
     * @return void
     */
    public function test_case_1() {
        $user = User::where("email", "<EMAIL>")->get()->first();

        $this->actingAs($user)
            ->from("organization/create")->post("organization", [
                "name" => "apimio",
                "region" => "AP",
                "units" => "SI",
                "currency" => "USD"
            ])->assertRedirect("organization/create");
    }
}
