<?php

namespace Tests\Feature;

use App\Models\Product\Attribute;
use App\Models\Product\Family;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CRUDAttributesTest extends TestCase
{

    /*
     * creating attribute set
     *
     * */
    public function attribute_set() {
        $attribute_set = new Family();
        $attribute_set->name = "Family 1";
        $attribute_set->organization_id = 1;
        $attribute_set->save();
        return $attribute_set;
    }

    /**
     * Create single line text attribute.
     *
     * @return void
     */
    public function testCreateSingleLineText()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 1,
                "organization_id" => 1,
                "name" => "Single line",
                "attribute_family" => $this->attribute_set()['id'],
                "sl_regular_expression" => "/[https://a-z0-9].com/",
                "sl_max_length" => "1",
                "sl_min_length" => "5",
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create number attribute.
     *
     * @return void
     */
    public function testCreateNumber()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 2,
                "organization_id" => 1,
                "name" => "Number",
                "attribute_family" => $this->attribute_set()['id'],
                "max_number" => "2",
                "min_number" => "2",
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create multi line text attribute.
     *
     * @return void
     */
    public function testCreateMultiLineText()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 3,
                "organization_id" => 1,
                "name" => "Multiline",
                "attribute_family" => $this->attribute_set()['id'],
                "ml_regular_expression" => "/[https://a-z0-9].com/",
                "ml_max_length" => "1",
                "ml_min_length" => "5",
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create date and time attribute.
     *
     * @return void
     */
    public function testCreateDate()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 5,
                "name" => "available until",
                "organization_id" => 1,
                "attribute_family" => $this->attribute_set()['id'],
                "start_date_time" => "12/12/2022",
                "end_date_time" => "12/12/2022",
                "is_required" => false
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create file attribute.
     *
     * @return void
     */
    public function testCreateFile()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 6,
                "organization_id" => 1,
                "name" => "GalleryFile",
                "attribute_family" => $this->attribute_set()['id'],
                "file_type" => "image",
                "width" => "2",
                "height" => "2",
                "size" => "100X200",
                "is_required" => false
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create measurement attribute.
     *
     * @return void
     */
    public function testCreateMeasurement()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 7,
                "organization_id" => 1,
                "name" => "measurement",
                "attribute_family" => $this->attribute_set()['id'],
                "unit" => "kg",
                "max_weight" => "2",
                "min_weight" => "2",
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }


    /**
     * Create rating attribute.
     *
     * @return void
     */
    public function testCreateRating()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 8,
                "organization_id" => 1,
                "name" => "Rating",
                "attribute_family" => $this->attribute_set()['id'],
                "max_rating" => "1",
                "min_rating" => "5",
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }


    /**
     * Create json attribute.
     *
     * @return void
     */
    public function testCreateJson()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 9,
                "organization_id" => 1,
                "name" => "json field",
                "attribute_family" => $this->attribute_set()['id'],
                "is_required" => false
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create true and false attribute.
     *
     * @return void
     */
    public function testCreateTrueAndFalse()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 10,
                "organization_id" => 1,
                "name" => "Boolean",
                "attribute_family" => $this->attribute_set()['id'],
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create url attribute.
     *
     * @return void
     */
    public function testCreateURL()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 11,
                "organization_id" => 1,
                "name" => "URL",
                "attribute_family" => $this->attribute_set()['id'],
            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }

    /**
     * Create color attribute.
     *
     * @return void
     */
    public function testCreateColor()
    {
        $attribute = new Attribute();
        $attribute->set_data(
            [
                "attribute_type_id" => 12,
                "name" => "T-shirt Color",
                "organization_id" => 1,
                "attribute_family" => $this->attribute_set()['id'],
                "is_required" => true

            ]
        )
            ->store(
            // error message
                function ($errors) {
                    $this->assertTrue(false);
                },
                // success message
                function ($obj) {
                    $this->assertTrue(true);
                }
            );
    }
}
