<?php

namespace Tests\Feature;

use App\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * A basic test for 200 response on all pages of authentication.
     *
     * @return void
     */
    public function test_case_1_200_response() {

        // index page.
        $response = $this->get('/');
        $response->assertStatus(200);

        // register page.
        $response = $this->get('/register');
        $response->assertStatus(200);

        // login page.
        $response = $this->get('/login');
        $response->assertStatus(200);

        // forgot password page.
        $response = $this->get('/password/reset');
        $response->assertStatus(200);

        // google redirection
        $response = $this->get('/google/redirection');
        $response->assertRedirect();
    }

    /**
     * A basic test to successfully register a user.
     *
     * @return void
     */
    public function test_case_2_successful_register() {
        $this->from('/register')->post('/create-account', [
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'phone'    => "(+12) *********",
            'password' => "testing_bot",
            'password_confirmation' => "testing_bot"
        ])->assertRedirect('/home');

        // check if user is redirected to verify page.
        $this->get("organization")->assertRedirect("email/verify");
    }

    /**
     * Already existing user should not be able to register.
     *
     * @return void
     */
    public function test_case_3_existing_user() {
        factory(User::class)->create([
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => bcrypt("testing_bot"),
        ]);

        $response = $this->from('/register')->post('/create-account', [
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => "testing_bot",
            'password_confirmation' => "testing_bot"
        ]);

        $response->assertRedirect('/register');
    }

    /**
     * Verification test.
     *
     * @return void
     */
    public function test_case_4_verify() {
        $user = factory(User::class)->create([
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => bcrypt("testing_bot"),
        ]);

        $user->email_verified_at = null;
        $user->save();

        $this->actingAs($user)
            ->get("organization/create")
            ->assertRedirect("email/verify");

        $user->email_verified_at = Carbon::now();
        $user->save();

        $this->actingAs($user)
            ->get("organization/create")
            ->assertOk();
    }

    /**
     * Simple login test.
     *
     * @return void
     */
    public function test_case_5_login() {
        factory(User::class)->create([
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => bcrypt("testing_bot"),
        ]);

        $this->from('/login')->post('/login', [
            'email' => "<EMAIL>",
            'password' => 'testing_bot',
        ])->assertRedirect('/organization');
    }

    /**
     * Fake login test.
     *
     * @return void
     */
    public function test_case_6_fake_login() {
        $this->from('/login')->post('/login', [
            'email' => "<EMAIL>",
            'password' => 'testing_bot',
        ])->assertRedirect('/login');
    }

    /**
     * Forgot password.
     *
     * @return void
     */
    public function test_case_7_forgot_password() {
        factory(User::class)->create([
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => bcrypt("testing_bot"),
        ]);

        $this->from('/password/reset')->post('/password/email', [
            'email' => "<EMAIL>",
        ])->assertSessionHasNoErrors();
    }

    /**
     * Create organization.
     *
     * @return void
     */
    public function test_case_8_create_organization() {
        $this->artisan('db:seed');

        // create user
        $user = factory(User::class)->create([
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => bcrypt("testing_bot"),
        ]);

        //
        $this->actingAs($user)
            ->get("/dashboard")
            ->assertRedirect("organization");

        $this->actingAs($user)
            ->from("organization/create")->post("organization", [
                "name" => "apimio",
                "region" => "AP",
                "units" => "SI",
                "currency" => "USD"
            ])->assertRedirect("organization/checkpoint");

        $this->actingAs($user)
            ->get("/dashboard")
            ->assertOk();
    }
}
