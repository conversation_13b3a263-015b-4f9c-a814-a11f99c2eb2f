<?php

namespace Tests\Unit;

use App\Events\Product\ManageFiles;
use App\Models\Product\Product;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\TestCase;

class FilesUnitTest extends TestCase
{
    public function testEventWithNoObjAndNoFiles()
    {
        // This will prevent actual event dispatching

        $obj = null;
        $filesArray = null;

        $eventsArray = [];
        $eventsArray[] = new ManageFiles($obj, $filesArray);

        Event::assertNotDispatched(ManageFiles::class); // Make sure the event is not dispatched
    }

    public function testEventWithNoObjButWithFiles()
    {
        // This will prevent actual event dispatching

        $obj = null;
        $filesArray = [
            ['id' => 1, 'url' => 'https://example.com/file1'],
            ['id' => 2, 'url' => 'https://example.com/file2'],
            // Add more files as needed
        ];

        $eventsArray = [];
        $eventsArray[] = new ManageFiles($obj, $filesArray);

        Event::assertDispatched(ManageFiles::class); // Make sure the event is dispatched
    }

    public function testEventWithObjAndFiles()
    {
        // This will prevent actual event dispatching

        // Assuming you have an object called $obj defined for testing


        $obj = Product::first();
        $filesArray = [
            ['id' => 1, 'url' => 'https://example.com/file1'],
            ['id' => 2, 'url' => 'https://example.com/file2'],
            // Add more files as needed
        ];

        $eventsArray = [];
        $eventsArray[] = new ManageFiles($obj, $filesArray);

        Event::assertDispatched(ManageFiles::class); // Make sure the event is dispatched
    }
}
