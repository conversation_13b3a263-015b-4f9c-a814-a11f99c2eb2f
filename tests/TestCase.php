<?php

namespace Tests;

use App\User;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setup_account() {
        $this->artisan('migrate:fresh');
        $this->artisan('db:seed');

        $user = factory(User::class)->create([
            'fname' => "Testing",
            'lname' => 'bot',
            'email'    => "<EMAIL>",
            'password' => bcrypt("testing_bot"),
        ]);

        $this->actingAs($user)
            ->from("organization/create")->post("organization", [
                "name" => "apimio",
                "region" => "AP",
                "units" => "SI",
                "currency" => "USD"
            ])->assertRedirect("organization/checkpoint");
    }
}
