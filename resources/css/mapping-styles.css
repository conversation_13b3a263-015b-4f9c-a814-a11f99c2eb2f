/* Main CSS file for mapping components */

/* Import other CSS files */
@import './attribute-modal.css';
@import './element-tags.css';

/* Additional styles for CSVMapping component */
.csv-mapping-container {
  min-height: 100vh;
  background-color: #f8fafc;
}

.csv-mapping-list {
  background-color: transparent;
}

.csv-mapping-list .list-item {
  margin-bottom: 12px;
}

/* Ensure proper virtualized list styling */
.react-window-list {
  outline: none !important;
}

.react-window-list:focus {
  outline: none !important;
}

/* Fix for react-window container */
.react-window-list > div {
  outline: none !important;
}

/* Ensure proper spacing for mapping rows */
.mapping-row-container {
  padding: 8px 0;
}

/* Fix for Ant Design components in virtualized list */
.mapping-row-container .ant-form {
  margin: 0;
}

.mapping-row-container .ant-form-item {
  margin-bottom: 0 !important;
}

.mapping-row-container .ant-form-item-label {
  padding-bottom: 4px !important;
  font-weight: 500;
}

/* Ensure proper button styling */
.mapping-row-container .ant-btn {
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
}

.mapping-row-container .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Fix for select components */
.mapping-row-container .ant-select {
  min-width: 120px;
}

.mapping-row-container .ant-select-selector {
  border-radius: 6px !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .mapping-row-container {
    padding: 4px 0;
  }
  
  .mapping-item .flex {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Loading states */
.mapping-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Error states */
.mapping-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  color: #dc2626;
}

/* Success states */
.mapping-success {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  color: #166534;
}

/* Ensure proper z-index for modals and overlays */
.mapping-modal-overlay {
  z-index: 1000 !important;
}

.mapping-dropdown-overlay {
  z-index: 999 !important;
}

/* Fix for any potential layout issues */
.mapping-layout-fix {
  position: relative;
  overflow: visible;
}

/* Ensure proper text rendering */
.mapping-text {
  font-family: "Proxima Nova", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fix for any missing border styles */
.border-green-500 {
  border-color: #10b981 !important;
}

.border-yellow-500 {
  border-color: #f59e0b !important;
}

.border-l-8 {
  border-left-width: 8px !important;
}

.border-t {
  border-top-width: 1px !important;
}

.border-r {
  border-right-width: 1px !important;
}

.border-b {
  border-bottom-width: 1px !important;
}

/* Ensure proper shadow styles */
.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Ensure proper rounded corners */
.rounded-md {
  border-radius: 0.375rem !important;
}

/* Ensure proper background colors */
.bg-white {
  background-color: #ffffff !important;
}

/* Ensure proper positioning */
.relative {
  position: relative !important;
}

.absolute {
  position: absolute !important;
}

/* Ensure proper flex utilities */
.flex {
  display: flex !important;
}

.flex-col {
  flex-direction: column !important;
}

.justify-start {
  justify-content: flex-start !important;
}

.justify-between {
  justify-content: space-between !important;
}

.items-center {
  align-items: center !important;
}

/* Ensure proper gap utilities */
.gap-1 {
  gap: 0.25rem !important;
}

.gap-6 {
  gap: 1.5rem !important;
}

/* Ensure proper margin and padding */
.px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.pt-4 {
  padding-top: 1rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

/* Ensure proper text utilities */
.text-xs {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}

.text-yellow-500 {
  color: #f59e0b !important;
}

/* Ensure proper z-index */
.z-50 {
  z-index: 50 !important;
}

/* Ensure proper positioning for delete button */
.top-2 {
  top: 0.5rem !important;
}

.right-2 {
  right: 0.5rem !important;
}
