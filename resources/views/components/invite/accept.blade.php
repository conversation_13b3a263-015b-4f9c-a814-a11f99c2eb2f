<div>
    <!-- Modal Accept vendor-->
    <div class="modal fade" id="accept" tabindex="-1" aria-labelledby="AcceptModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title Poppins semibold" id="exampleModalLabel">{{__($title)}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="{{ route("invite.AcceptVendor") }}" id="AcceptVendor" method="POST">
                    @csrf
                    <input type="hidden" value="{{$account->id}}" name="id">
                        <div class="modal-body">
                            <p class="Roboto regular">{{__($text)}}<br> <small class="text-light-grey">*All Products will be shared with retailer.</small></p>
                            <label for="catalog"> {{trans('vendors_create.select_catalog')}}&nbsp;<span style="color: #ff8178">*</span></label>
                            <select placeholder="Select Stores" id="Catalog" multiple="multiple" name="channel_ids[]"
                                    class="form-control btn sumoselect @error('channel_ids') is-invalid @enderror">
                                        @foreach($channels as $key => $channel)
                                            <option value="{{ $channel->id }}"
                                                    class="Poppins regular text-color text-capitalize"
                                                {{ old('channel_ids')
                                                            ?  in_array($channel->id, old("channel_ids"))
                                                            : (isset($data)
                                                            ?(in_array($channel->id, array_column($data['vendor']->channels_without_scope->toArray(),'id'))
                                                            ?'selected'
                                                            :null)
                                                            :null )}}
                                            >{{ $channel->name }}</option>
                                        @endforeach
                            </select><br>
                            @error('channel_ids')
                                        <span class="text-danger" role="alert">
                                              <small>{{ $message }}</small>
                                           </span>
                            @enderror

                            <div class="modal-footer p-0">
                                <button type="button" data-dismiss="modal" id="cancel-btn"
                                        class="btn btn-dark-tertiary float-left shadow"
                                        style="width: 120px;">
                                    {{trans('products_edit.cancel_btn')}}
                                </button>
                            <button type="submit" id="Accept-btn" class=" btn btn-success px-4 hovereffect ripplelink shadow">
                                {{__($button)}}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .SumoUnder{
        position :absolute;
    }
</style>
