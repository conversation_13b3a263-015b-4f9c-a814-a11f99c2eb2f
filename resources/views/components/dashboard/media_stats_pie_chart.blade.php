<div>
    <!-- Simplicity is an acquired taste. - <PERSON><PERSON> -->
</div><div class="card mb-0 shadow-none border border-radius position-relative">
    <!--TODO Loading-->
    {{--<div class="spinner-border position-absolute loaderpopover-hover" role="status"
         style="color: #007BFF;top: 5%; right: 3%;width: 24px;height: 24px"
         data-toggle="popover"
         data-placement="top" data-html="true"
         data-content="Syncing...">
        <span class="sr-only">Loading...</span>
    </div>--}}
    <div class="row no-gutters">
        <div class="col-xl-7 col-lg-6 col-md-7 col-sm-6 col-12">
            <div class="card-body">
                <h5 class="Poppins semibold mb-1 text-dark">{{__('SHOP IMAGE DATA QUALITY')}}

                <!---TODO POPOVER--->
                    <span class="popover-hover-1"
                          style="font-size: 16px;font-weight: normal;"
                          data-toggle="popover" data-placement="right" data-html="true"
                          data-content="

                                                      <ul class='list-group ul-popover border-0'>
                                                          <li class='list-group-item border-0 px-0 pt-2 pb-0'>
                                                              <strong class='Roboto bold'>{{__('Requirements:')}}</strong>
                                                          </li>
                                                        </ul>
                                                        <ul class='ul-popover border-0 pl-3 pb-0 pt-2'>
                                                          <li class='border-0 px-0 py-1 Roboto'>{{__('Image resolution should be 1080x1080')}}
                              </li>
                              <li class='border-0 px-0 py-1 Roboto'>{{__('Image size should be 200kb')}}
                              </li>
                              <li class='border-0 px-0 py-1 Roboto'>{{__('Image format should be JPG')}}
                              </li>
                              <li class='border-0 px-0 py-1 Roboto'>{{__('Image should be square')}}
                              </li>
                            </ul>">
                                                    <img src="{{asset('media/retailer-dashboard/info.png')}}" alt="">
                                                </span>

                </h5>
                <p class="mb-4 Roboto text-light-grey">
                    {{--                                            {{ ($shopify_data['product_count'] >$shopify_data['total_shopify_products'])?$shopify_data['total_shopify_products']:$shopify_data['product_count'] }}
                                                                of {{ $shopify_data['total_shopify_products'] }} --}}
                    {{__(($total_files < 100 ? $total_files : '100').'/'.$total_files.' images scanned.')}}
                </p>

                <ul class="pl-0 custom-list">
                    <li class="card-text mb-2 li-approved Roboto text-dark" >
                        {{__("Good: ".$file_stats["approve"]."%")}}
                    </li>
                    <li class="card-text mb-2 li-warning Roboto text-dark">
                        {{__("Fair: ".$file_stats["warning"]."%")}}
                    </li>
                    <li class="card-text mb-2 li-error Roboto text-dark">
                        {{__("Poor: ".$file_stats["error"]."%")}}
                    </li>
                </ul>
            </div>
        </div>
        @if($file_stats["approve"] == 0 && $file_stats["warning"] == 0 && $file_stats["error"] ==0)
            <x-dashboard.empty-pie-chart/>
        @else
            <div class="col-xl-5 col-lg-6 col-md-5 col-sm-6 col-12 mt-4 mb-4 pr-sm-3">
                <div>
                    <canvas id="pieChart1" width="160" height="260"></canvas>
                </div>
            </div>
        @endif
    </div>
</div>


<script>
    let data1 = {
        datasets: [{
            data:
                [
                    {{$data["file_stats"]["approve"]}},
                    {{$data["file_stats"]["warning"]}},
                    {{$data["file_stats"]["error"]}}
                ]
            ,
            backgroundColor: [
                "#6ECE65",
                "#F7C130",
                "#F72B2B",
            ],
        }],
    };
</script>
