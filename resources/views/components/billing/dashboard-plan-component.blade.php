<div>
    <div class="card border-radius shadow-none">
        <div class="col mt-4 mt-lg-0">
            <div class="card mb-0 border-radius shadow-none border">
                <div class="card-body pb-0">
                    <h3 class="Poppins mt-2 semibold text-dark">
                        {{ trans('apimio_dashboard.billing') }}
                    </h3>
                    <h3 class="mt-4 Roboto bold text-dark">
                        {{$name}}
                    </h3>

                    <p class="mb-4 Roboto regular text-dark">
                        {{$period_end}}
                    </p>

                    <div class="row py-3" style="margin-bottom: 2px">
                       {{-- <div class="col-12 col-md-6">

                            <h3 class="mt-2 Roboto bold black" style="font-size: 30px">
                                ${{ isset($price->amount)  ? $price->amount/100 : (isset($price) ? $price : 0) }}
                                <small class="bold black">{{__(isset($price->interval) ? "/".$price->interval : "")}}</small>
                            </h3>
                        </div>--}}
                        <div class="col-12 {{--col-md-6--}} mt-lg-3">
                            <a href="{{route("billing")}}" style="width: 180px"
                               class="btn btn-outline-primary hovereffect ripplelink" id="update-plans">
                                {{ trans('apimio_dashboard.update_plan_btn') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')
@endpush

