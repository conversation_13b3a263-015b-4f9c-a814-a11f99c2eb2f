<div>
    <div class="d-flex flex-column">
        <div class="p-2 mx-auto">
            <img src="{{asset('media/emptypage/no_table_entries.png')}}" alt="empty page">
        </div>
        <div class="mx-auto">
            <p class="Roboto">
                {{ $descriptions??'' }}
            </p>
        </div>
        @can('add_and_edit_product' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
            <div class="px-2 pb-4 mx-auto">
                <button class="btn btn-primary px-5 ripplelink" data-toggle="modal" data-target="#create_product">
                    {{ $btnName??'' }}
                </button>
            </div>
        @endcan
    </div>
</div>
