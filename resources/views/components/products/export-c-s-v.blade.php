{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="modal fade" id="export_product"
     data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel"
                    style="font-size: 20px">{{trans('products_export_step3.export_modal_title')}}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body pb-4">
                <div class="form-group">
                    <label for="">{{trans('products_export_step3.select_variant_type')}}</label>
                    <select name="nodes[variant][variant_type]"
                            class="form-control m-auto @error('variant') is-invalid @enderror" required>
                        <option value="" class="Poppins regular text-color">{{__("Choose")}}
                        </option>
                        @foreach($export_type as $type_key => $type_value)
                            @if(isset($template_attributes['variant_type']))
                                <option value="{{$type_key}}" {{($type_key == $template_attributes['variant_type']) ? 'selected' : null}}>{{$type_value}}</option>
                            @else
                                <option value="{{$type_key}}">{{$type_value}}</option>
                            @endif
                        @endforeach

                    </select>
                </div>
                <div class="form-group">
                    <label for="version">{{trans('products_export_step3.select_language')}}&nbsp;<span style="color: #ff8178">*</span></label>
                    <select name="version" class="form-control version" id="version" required>
                        <option value="">Choose</option>
                        @foreach($products_versions as $p_version)
                            @if(isset($template_attributes['version_id']))
                                <option value="{{$p_version->id}}" {{ ($template_attributes['version_id'] == $p_version->id) ? 'selected' : null}}>{{$p_version->name}}</option>
                            @else
                                <option value="{{$p_version->id}}">{{$p_version->name}}</option>
                            @endif
                        @endforeach
                    </select>
                </div>

                <div class="form-group mb-4">
                    <label for="catalog">{{trans('products_export_step3.select_catalog')}} &nbsp;<span style="color: #ff8178">*</span></label>
                    <select name="catalog" class="form-control catalog" id="catalog" required>
                        <option value="">Choose</option>
                        @foreach($products_catalog as $p_catalog)
                            @if(isset($template_attributes['channel_id']))
                                <option value="{{$p_catalog->id}}" {{ ($template_attributes['channel_id'] == $p_catalog->id) ? 'selected' : null}}>{{$p_catalog->name}}</option>

                            @else
                                <option value="{{$p_catalog->id}}">{{$p_catalog->name}}</option>

                            @endif
                        @endforeach
                    </select>
                </div>

                <hr class="mt-1 mb-4 divider">

                <div class="d-flex flex-row mb-3">
                    <div>
                        <label class="Roboto bold text-dark">{{trans('products_export_step3.save_template_option')}}</label>
                    </div>
                    <div class="custom-control custom-switch">
                        <label for="draft" class="mr-2 Roboto regular" style="margin-top: 2px;">
                            {{trans('products_export_step3.no')}}
                        </label>
                        <div class="custom-control custom-switch d-inline" id="draft">
                            <input name="temp_status" type="checkbox" class="custom-control-input"
                                   id="customSwitch1" checked>
                            <label class="custom-control-label Roboto regular custom-label"
                                   for="customSwitch1"
                                   style="padding-top: 2px"> {{trans('products_export_step3.yes')}}</label>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4 template-name">
                    <label for="catalog">{{trans('products_export_step3.template_name')}}&nbsp;<span style="color: #ff8178">*</span></label>
                    <input required type="text" class="form-control" name="temp_name" {!! (isset($template_attributes['name']) && isset($template_attributes['id'])) ? 'value="'.$template_attributes['name'].'" readonly' : null !!}>
                    <input type="hidden" class="form-control" name="temp_id" {{(isset($template_attributes['name']) && isset($template_attributes['id'])) ? 'value='.$template_attributes['id'] : null }}>

                    <hr class="mt-4 divider">
                </div>

                <div class="form-group">
                    <input type="hidden" id="btn-text" name="btn-text" value="">
                    <input id="pro_exp_start_btn" type="submit" name="export_csv" class="btn btn-primary mb-1 float-right ripplelink"
                           style="width: 210px;"
                           onclick="startExporting()"
                           value="{{trans('products_export_step3.start_exporting_btn')}}"/>
                    <span id="export_loading_btn" class="form-control btn btn-primary btn-block d-none">
                                    {{trans('products_export_step3.exporting')}}
                        <span class="spinner-border spinner-border-sm ml-2"
                              role="status" aria-hidden="true"></span>
                    </span>

                    <input type="submit" name="save_template" id="template-btn"
                           class="btn btn-outline-primary mr-4 mb-1 float-right template-name hovereffect ripplelink"
                           style="width: 200px;" value="{{trans('products_export_step3.save_template_btn')}}">

                </div>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')
    <script>
        @if(!isset($template_attributes['id']))
        $('input[name="temp_status"]').attr('checked', false);
        $('input[name="temp_status"]').click();
        @endif

        $('input[name="temp_status"]').on('change', function (){
            if ($(this).is(':checked')){
                $(".template-name").show("fast");
                $('input[name="temp_name"]').prop('required', true)
            }else{
                $(".template-name").hide("fast");

                console.log('unchecked')
                $('input[name="temp_name"]').prop('required', false)
            }
        })
        function startExporting() {
            let versionval = $(".version").val();
            let familyval = $(".family").val();
            let catalogval = $(".catalog").val();
            let startexport = $('#pro_exp_start_btn').val();
            let temp_name = $('input[name="temp_name"]').val();


            if (versionval === "" && familyval === "" && catalogval === "") {
                return false;
            }
            if (versionval !== "" && familyval !== "" && catalogval !== "" && startexport !== "") {
                if ($('input[name="temp_status"]').is(':checked')){
                    if (temp_name === ""){
                        return false
                    }
                }
                $('#pro_exp_start_btn').hide();
                $('#template-btn').hide();
                $('#export_loading_btn').removeClass('d-none');
            }
        }
    </script>
@endpush

