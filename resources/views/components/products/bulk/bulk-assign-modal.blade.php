<div>

    <!-- Modal -->
    <div class="modal fade" id="bulkAssign" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
         aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title" id="staticBackdropLabel">Bulk assign</h2>
                    <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0 cancel-button-js" data-bs-dismiss="modal"
                            aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <form action="{{route('bulk.assign')}}" method="POST" class="formStyle" id="bulk_assign_form"
                      data-actiontype="{{$bulk_assign_action}}">
                    @csrf
                    <div class="modal-body">
                        {{-- multi attribute start --}}
                        <div class="row gx-3" id="section1">
                            <div class="col-12">
                                <h3 class="fw-700">Select the attributes</h3>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Categories" data-slug="categories">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Categories">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Categories">Categories</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Attribute" data-slug="attribute_sets">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Attribute">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Attribute">Attribute Set</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Tags" data-slug="tags">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Tags">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Tags">Tags</label>
                                </label>
                            </div>
                            <!-- <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Quantity" data-slug="quantity">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Quantity">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Quantity">Quantity</label>
                                </label>
                            </div> -->
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Weight" data-slug="weight">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Weight">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Weight">Weight</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Brands" data-slug="brands">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Brands">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Brands">Brands</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Vendors" data-slug="vendors">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Vendors">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Vendors">Vendors</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Stores" data-slug="stores">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Stores">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Stores">Stores</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Continue_Selling" data-slug="continue_selling">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Continue_Selling">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Continue_Selling">Continue Selling</label>
                                </label>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4 mb-3 position-relative">
                                <input type="checkbox" class="bulk-input-css" id="Track_Quantity" data-slug="track_quantity">
                                <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2" for="Track_Quantity">
                                    <label class="form-check-label ms-3 px-1 fw-400" for="Track_Quantity">Track Quantity</label>
                                </label>
                            </div>
                        </div>

                        {{-- multi attribute end --}}

                        {{-- section2 attribute input start --}}
                        <div class="row d-none" id="section2">

                            <input type="hidden" class="bulk_productIds" name="products" value="">
                            <input type="hidden" class="bulk_products_filter_array" name="filter_products" value="">

                            <input type="hidden" id="selected_options" name="selected_options"
                                   value="{{ $bulk_assign_action == 'price' ? $bulk_assign_action : '' }}">
                            <div class="col-12">
                                <div class="{{$bulk_assign_action !== "assign" ? 'd-none' : ''}} addremoveattr">
                                    <h3 class="fw-700 mb-3">Do you want to add or remove the attribute values?</h3>
                                    <div class="custom-control custom-switch ms-3 mb-3 formStyle">
                                        <label for="customSwitch1" class="me-2"
                                               title="All the selected attribute values are detached in your selected products.">
                                            Remove
                                        </label>
                                        <div class="form-check form-switch d-inline" id="draft">
                                            <input type="hidden" name="required_data[assign_flag]" value="0"/>
                                            <input name="required_data[assign_flag]" type="checkbox" value="1"
                                                   class="form-check-input assign_flag" checked data-confirm-before-leave="true"
                                                   id="customSwitch1">
                                            <label class="ms-2" for="customSwitch1"
                                                   title="All the selected attribute values are attached in your selected products.">
                                                Add
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="languge-dropdown-bulkassign">

                                    <div class="form-group mb-3">
                                        @if(isset($bulk_data_array['versions']) && count($bulk_data_array['versions']) == 1)
                                            @foreach($bulk_data_array['versions'] as $version_key => $version)
                                                <input type="hidden" class="form-control w-100 bg-white-smoke" name="required_data[version]"
                                                       value="{{$version_key}}" style="pointer-events: none;">
                                                {{$version}}
                                            @endforeach
                                        @else
                                            <h3 class="fw-700 mb-1">In which language you want to
                                                assign attribute values ?</h3>
                                            <select class="form-control w-100 bg-white-smoke" name="required_data[version]" required>
                                                <option value="">Select Language</option>
                                                @if(isset($bulk_data_array['versions']))
                                                    @foreach($bulk_data_array['versions'] as $version_key => $version)
                                                        <option value="{{$version_key}}">{{$version}}</option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        @endif
                                    </div>
                                </div>
                                <div>
                                    <h3 class="fw-700 mb-1">Selected Attributes : </h3>
                                    <div class="form-group mb-2 attribute-set-js d-none">
                                        <label for="name">Attribute set</label>
                                        <select class="form-control w-100 selectize_element" name="attributes[attribute_sets][]" multiple>
                                            @if(isset($bulk_data_array['attribute_sets']))
                                                @if(count($bulk_data_array['attribute_sets']) == 0)
                                                    <option value="" disabled>No attribute sets found</option>
                                                @else
                                                    @foreach($bulk_data_array['attribute_sets'] as $attribute_set)
                                                        <option value="{{$attribute_set['id']}}"
                                                                {{$attribute_set['attributes_count'] <= 0 ? 'disabled' : ''}} title="asasasasa">{{$attribute_set['name']}}</option>
                                                    @endforeach
                                                @endif
                                            @endif
                                        </select>
                                    </div>
                                    <div class="form-group mb-2 category-js d-none">
                                        <label for="name">Category</label>
                                        <select class="form-control w-100 selectize_element" name="attributes[categories][]" multiple>
                                            @if(isset($bulk_data_array['categories']))
                                                @if(count($bulk_data_array['categories']) == 0)
                                                    <option value="" disabled>No categories found</option>
                                                @else
                                                    @foreach($bulk_data_array['categories'] as $category_key => $category)
                                                        <option value="{{$category_key}}">{{$category}}</option>
                                                    @endforeach
                                                @endif
                                            @endif
                                        </select>
                                    </div>
                                    <div class="form-group mb-2 brands-js d-none">
                                        <label for="name">Brands</label>
                                        <select class="form-control w-100 selectize_element" name="attributes[brands][]" multiple>
                                            @if(isset($bulk_data_array['brands']))
                                                @if(count($bulk_data_array['brands']) == 0)
                                                    <option value="" disabled>No brands found</option>
                                                @else
                                                    @foreach($bulk_data_array['brands'] as $brand_key => $brand)
                                                        <option value="{{$brand_key}}">{{$brand}}</option>
                                                    @endforeach
                                                @endif
                                            @endif
                                        </select>
                                    </div>
                                    <div class="form-group mb-2 vendors-js d-none">
                                        <label for="name">Vendors</label>
                                        <select class="form-control w-100 selectize_element" name="attributes[vendors][]" multiple>
                                            @if(isset($bulk_data_array['vendors']))
                                                @if(count($bulk_data_array['vendors']) == 0)
                                                    <option value="" disabled>No vendors found</option>
                                                @else
                                                    @foreach($bulk_data_array['vendors'] as $vendor_key => $vendor)
                                                        <option value="{{$vendor_key}}">{{$vendor}}</option>
                                                    @endforeach
                                                @endif
                                            @endif
                                        </select>
                                    </div>

                                    <div class="form-group mb-2 store-js d-none">
                                        <label for="name">Store</label>
                                        <select class="form-control selectize_element w-100" name="attributes[stores][]" multiple>
                                            @if(isset($bulk_data_array['stores']))
                                                @if(count($bulk_data_array['stores']) == 0)
                                                    <option value="" disabled>No stores found</option>
                                                @else
                                                    @foreach($bulk_data_array['stores'] as $store_key => $store)
                                                        <option value="{{$store_key}}">{{$store}}</option>
                                                    @endforeach
                                                @endif
                                            @endif
                                        </select>
                                    </div>

                                    <div class="form-group mb-3 weight-js d-none">
                                        <div>
                                            <label for="name">Weight</label>
                                            <a class="clr-light ms-1" href="#" data-bs-toggle="tooltip" id="calculate-tooltip"
                                               data-bs-html="true" data-bs-placement="top" title="
                                        <div class='text-start'>
                                        <b>Example values:</b>
                                        <li>50  Replace with existing</li>
                                        <li>+50  Addition</li>
                                        <li>-50  Subtraction</li>
                                        <li>50%  percentage</li>
                                        <li>+50%  add percentage</li>
                                        <li>-50%  sub percentage</li>
                                        </div>">
                                                <i class="fa fa-regular fa-circle-info"></i>
                                            </a>
                                        </div>
                                        <input type="text" class="form-control" name="attributes[weight]" value="" autofocus="">
                                    </div>
                                    <div class="form-group mb-3 tags-js d-none">
                                        <label for="name">Tags</label>
                                        <input type="text" class="form-control dynamic_tags" name="attributes[tags]" value="" autofocus="">
                                        <small class="text-muted fs-10">Simply press Enter to add tags!</small>
                                    </div>
                                    <div class="form-group mb-3 continue_selling-js d-none position-relative">
                                        <input type="hidden" name="attributes[continue_selling]" value="0"/>
                                        <input type="checkbox" name="attributes[continue_selling]" value="1" checked
                                               id="customSwitch_continue_selling" class="bulk-input-css" style="pointer-events: none;"/>
                                        <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2"
                                               for="attributes[continue_selling]">
                                            <label class="form-check-label ms-4 px-1 fw-400" for="attributes[continue_selling]">Continue
                                                Selling</label>
                                        </label>
                                    </div>
                                    <div class="form-group mb-3 track_quantity-js d-none position-relative">
                                        <input type="hidden" name="attributes[track_quantity]" value="0"/>
                                        <input type="checkbox" name="attributes[track_quantity]" value="1" checked
                                               id="customSwitch_track_quantity" class="bulk-input-css" style="pointer-events: none;"/>
                                        <label class="d-flex align-items-center bulk-custom-border bulk-checked px-2"
                                               for="attributes[track_quantity]">
                                            <label class="form-check-label ms-4 px-1 fw-400" for="attributes[track_quantity]">Track
                                                Quantity</label>
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>
                        {{-- section2 attribute input end --}}
                    </div>
                    <div class="modal-footer p-3">
                        <button type="button" class="btn btn-outline-danger" id="attribute-cancel-button" data-bs-dismiss="modal">Cancel
                        </button>
                        <button type="button" class="btn btn-primary disabled"
                                id="attribute-next-button">Next
                        </button>
                        <button type="button" class="btn btn-outline-primary d-none" id="attribute-back-button">Back</button>
                        <button type="submit" class="btn btn-primary bulk_module_js d-none"
                                id="attribute-save-button">Save
                        </button>

                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        $('.apply_calculate_validation_js').on('keyup', function () {
            var m_this = this;
            calculate_input_validate(m_this);
        });
    });

</script>
