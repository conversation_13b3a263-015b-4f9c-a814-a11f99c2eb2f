{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="col-6 col-md-2 col-lg-2">
    <div class="form-group">
        <label for="formula" class="ml-4">{{__("Formula")}}</label>
        <select name="nodes[data][{{$row_count}}][with_formula]" style="width: 105px" class="formula_field form-control ml-4" id="formula" >
            @foreach($formulas as $formula_key => $formula_val)
                @if(isset($row_node['with_formula']))
                    <option value="{{$formula_key}}" class="Poppins regular text-color" {{($row_node['with_formula'] == $formula_key) ? 'selected' : null}}>{{$formula_val}}</option>
                @else
                    <option value="{{$formula_key}}" class="Poppins regular text-color">{{$formula_val}}</option>
                @endif
            @endforeach
        </select>
    </div>
</div>

<div class="col-6 col-md-1 col-lg-1">
    <div class="d-flex align-items-center pt-4">
        <button type="button" class="btn bg-white border-0 p-0" >
            <img src="{{asset('media/new-flow/arrow-forward.png')}}" alt="">
        </button>
    </div>
</div>
