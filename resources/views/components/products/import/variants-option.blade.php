
{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="col-lg-3">
    <div class="row">
        <div class="col-lg-10">
            <div class="form-group">
                <select name="nodes[variant][variant_options][]"
                        class="form-control">
                    <option value="" class="Poppins regular text-color">Select Option
                    </option>
                    @foreach($heading_attributes as $h_attribute)

                        @if(isset($template_option) && $template_option != null)
                            <option value="{{$h_attribute}}" {{($template_option == $h_attribute) ? 'selected' : null}}>{{$h_attribute}}</option>
                        @else
                            <option value="{{$h_attribute}}">{{$h_attribute}}</option>
                        @endif
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-lg-2 p-0">
            <a class="btn btn-delete" onclick="delete_opt(this)" style="vertical-align: sub">
                <img src="{{asset('media/retailer-dashboard/delete.png')}}" alt="" style="vertical-align: text-top" width="20px">
            </a>
        </div>
    </div>
</div>
