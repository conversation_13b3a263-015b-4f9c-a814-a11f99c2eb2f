{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="col-6 col-md-2 col-lg-2">
    <div class="form-group">
        @if($row_count == 0)
            <label for="name_in_import">{{__("Import (Category)")}}</label>
        @elseif($row_count == 1)
            <label for="name_in_import">{{__("Import (SKU)")}}<span class="text-danger-light">*</span></label>
        @elseif($row_count == 2)
            <label for="name_in_import">{{__("Import (Product Name)")}}<span class="text-danger-light">*</span></label>
        @else
            <label for="name_in_import">{{__("Name in Import")}}</label>
        @endif

        <select name="nodes[data][{{$row_count}}][from][]" style="width: 182px"
                class="form-control" {!! ($row_count == 1 || $row_count == 2) ? 'required' : false !!}>
            <option value="" class="Poppins regular text-color">Select Column</option>
            @foreach($csv_heading as $h_attribute)
                @if(isset($row_node['from']))
                    <option value="{{$h_attribute}}" {{($row_node['from'][0] == trim($h_attribute)) ? 'selected' : null}} >{{$h_attribute}}</option>
                @else
                    <option value="{{$h_attribute}}">{{$h_attribute}}</option>
                @endif
            @endforeach
        </select>
    </div>
</div>
