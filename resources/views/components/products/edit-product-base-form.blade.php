<form id="pro_edit_form" class="formStyle" method="post" action="{{route('products.update',$product->id)}}">
    @csrf
    @method('patch')
    <input type="hidden" name="sku" value="{{ $product->sku }}">
    <input type="hidden" name="version_id" value="{{ $version->id }}">

    {{ $slot }}

    @if(isset($product->versions->id))
        @if($product->channels->count() >0)
            @if($product->channels[0]->channel_versions()->first()->version_id == $product->versions->id)
                    <div class="sync-product-bottom-popup popup-width px-3 py-2 border-radius-9 bg-primary-light border-light py-4">
                       <div  class="d-flex justify-content-between align-items-center">
                           <button type="button" class="close border-0 fs-24 bg-white px-2" onclick="closepopup()">
                               <span aria-hidden="true">&times;</span>
                           </button>
                           <h3 class="mb-0">{{trans('products_edit.sync_products_description')}}</h3>
                           @if($product->channels[0]->isShopifyLinked($product->channels[0]->id))
                               <a class="btn btn-outline-primary"
                                  href="{{route('shopify.bulk.sync',['product_id'=>$product->id,'template_id'=>[\App\Classes\Mapping\Conversion::getTemplates(['channel_id'=>$product->channels[0]->id,'type'=>'shopify'])->first()->id??null],'channel_id'=>$product->channels[0]->id])}}">
                                   {{trans('products_edit.sync_products_btn')}}
                               </a>
                           @else
                               <a class="btn btn-outline-primary"
                                  href="{{ route("shopify.show",$product->channels[0]->id) }}">
                                   {{trans('products_edit.sync_products_btn')}}
                               </a>
                           @endif
                       </div>
                    </div>
            @endif
        @endif
    @endif
</form>


<x-assets.delete-modal text="{{trans('products_edit.delete_modal_description')}}"
                       button="{{trans('products_edit.delete_product_btn')}}"
                       title="{{trans('products_edit.delete_modal_title')}}"
                       url="{{route('products.destroy',$product->id)}}"
                       id="{{$product->id}}"
                       type="product"/>

@push('footer_scripts')
    <script type="text/javascript">
        let attrid;
        let attrname;

        $("#delete_product_btn").click(function (e) {
            e.preventDefault();
            attrid = $(this).attr('data-id');
        });

        function del() {
            var form = document.getElementById('delete-brand');
            var url = '{{ route("products.destroy", ":id") }}';
            url = url.replace(':id', attrid);
            form.setAttribute('action', url);
            form.submit();
        }

        function closepopup() {
            $('.sync-product-bottom-popup').css("display", "none");
            $('.card-body').removeClass('mb-5 pb-5');
        }

        setTimeout(function () {
                $('.sync-product-bottom-popup').fadeOut("slow", function () {
                    $('.card-body').removeClass('mb-5 pb-5');
                    $('.sync-product-bottom-popup').css("display", "none");
                })
            }
            , 10000);

    </script>

    @if(session()->has("sync_popup"))
        <script>
            $(document).ready(function () {
                $('.sync-product-bottom-popup').css("display", "block")
                // $('.card-body').addClass('mb-5 pb-5');
            });
        </script>
    @endif
@endpush
