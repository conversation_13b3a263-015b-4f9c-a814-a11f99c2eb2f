<?php ?>
@extends('layouts.app_new')
@section('titles', 'Billing')
@section('content')

    <div class="row ">
        <!--mid space-->
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-9 col-xxl-7">

            <x-products.page-title name="{{ trans('billing.page_title') }}"
                description="{{ trans('billing.page_description') }}" links="false" button="false" />


            <div class="mt-4">
                <span class="Roboto semibold text-dark">
                    {{ trans('billing.current_plan') }}
                </span>
            </div>

            <div class="card mt-3 border-0">
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <p class="card-title mb-1 Roboto bold">
                              {{ isset($plan->name) ? $plan->name : trans('billing.free_plan') }}
                            </p>
                            <small class="text-muted Roboto">
                                {{ trans('billing.lifetime_subscription', ['time' => 'Monthly']) }}
                            </small>
                        </div>
                        <div class="col-6">
                            <div class="d-flex flex-row-reverse">
                                <div class="p-2">
                                    <a href="{{route('billing.plans.view')}}"
                                        class="btn btn-primary float-right ripplelink">
                                        Upgrade Plan
                                    </a>
                                </div>
                                <div class="p-2">
                                    <a href="{{$subscriptionUrl}}"
                                        class="btn btn-outline-primary float-right ripplelink hovereffect">
                                        View Subscription
                                    </a>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            {{-- <div class="mt-4">
                <span class="Roboto semibold text-dark">
                    Payment Method
                </span>
            </div>

            <div class="card mt-3 border-0">
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            @if(isset($organization->pm_last_four))
                            <p class="card-title mb-1 Roboto bold">
                                <span>{{trans('billing.default_card')}}: </span>
                                <img src="{{asset('media/billing/visa-5.png')}}" alt="">
                                **** **** **** {{$organization->pm_last_four}}
                            </p>
                            @else
                                {{trans('billing.empty')}}
                            @endif

                        </div>
                    </div>

                    <hr class="mt-3 mb-3 divider">

                    <p class="Roboto my-2">
                        {{trans('billing.accepted_card_types')}}
                    </p>
                </div>
            </div>
        </div>

        <div class="col-0 mx-3">
            <div class="vertical-divider"></div>
        </div>

        <!--right side space-->
        <div class="col-12 col-sm-12 col-md-12 col-lg-4 col-xl-4 mb-4">

            <hr class="divider mt-1 d-block d-sm-block d-md-block d-lg-none">

            <div class="mt-5">
                <h3 class="Roboto semibold text-dark m-0">
                    {{trans('billing.breakdown_summary')}}
                </h3>
            </div>
            <div class="row mt-3">
                <div class="col-9">
                    <div class="mt-2">
                        <span class="Roboto semibold text-primary">
                            {{ isset($plan->name) ? $plan->name : trans('billing.free_plan') }}
                        </span>
                    </div>
                </div>
                <div class="col-3">
                    <div class="mt-2">
                        <span class="Roboto semibold text-primary float-right">
                            {{ isset($plan->price_per_month) ? $plan->price_per_month : trans('billing.free_plan') }}
                        </span>
                    </div>
                </div>
            </div>

            <hr class="mt-3 divider divider-bold">

            <p class="Roboto mt-3 mb-2">
                {{trans('billing.total_SKU', ['no_of_products' => $plan->no_of_sku])}}
            </p>
            <p class="Roboto mt-3 mb-2">
                {{trans('billing.total_store', ['no_of_channels' => $plan->no_of_channel])}}
            </p>
            <p class="Roboto mb-2">
                {{trans('billing.language')}}
            </p>

            <p class="Roboto mb-2">
                {{trans('billing.integrations')}}
                <img class="ml-1" src="{{asset('media/billing/shopiffy.png')}}" alt="">
            </p>

            <hr class="mt-3 divider divider-bold">

            <div class="row mt-2">
                <div class="col-6">
                    <div class="mt-2">
                        <span class="Roboto semibold text-primary">{{trans('billing.total_price')}}</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="mt-2">
                        <span class="Roboto semibold text-primary float-right">
                            {{ isset($plan->price_per_month) ? $plan->price_per_month : trans('billing.free_plan') }}
                        </span>
                    </div>
                </div>
            </div>

        </div> --}}
    </div>
@endsection
@push('footer_scripts')
@endpush
