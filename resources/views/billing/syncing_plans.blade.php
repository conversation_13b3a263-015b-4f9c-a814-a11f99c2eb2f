@extends('layouts.app_new',['sidebar_display'=>false])
@section('titles','Billing')
@section('content')
    <div class="container">
        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="mt-4">
                            <h3 class="m-0 font-24 Roboto bold text-dark">{{trans('billing.plan_page_title')}} </h3>
                            <div class="my-3 Roboto regular">{{trans('billing.plan_page_description')}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="mt-1 mb-4 divider">
        <div>
            <div class="row row-cols-1 row-cols-md-3 pt-3">
                @foreach($plans as $plan1)
                    <div class="col">
                        <div
                            class="card border {{$plan1->handle == 'community_plan' ? 'bg-light' : ''}}{{$plan1->handle == 'standard_plan' ? 'bg-warning' : ''}}{{$plan1->handle == 'plus_plan' ? 'bg-primary' : ''}}">
                            <div class="card-body">
                                <div class="text-center mb-2">
                                    <h5 class="card-title mb-4 {{$plan1->handle == 'community_plan' || $plan1->handle == 'standard_plan' ? 'text-dark' : ''}}{{$plan1->handle == 'plus_plan' ? 'text-white' : ''}}">
                                        {{ $plan1['name'] }}
                                    </h5>
                                    <img src="{{ asset($plan1['icon_link']) }}" alt="community">
                                </div>

                                <div class="card-text text-center mb-3 mt-3">
                                    <span
                                        class="font-32 Roboto bold {{$plan1->handle == 'community_plan' || $plan1->handle == 'standard_plan' ? 'text-dark' : ''}}{{$plan1->handle == 'plus_plan' ? 'text-white' : ''}}">
                                        {{ $plan1['price_per_year'] }}
                                        <sub style="font-size: 16px">{{ trans('billing.per_month') }}</sub>
                                    </span>
                                    <br>
{{--                                    <span--}}
{{--                                        class="small-text Roboto bold {{$plan1->handle == 'community_plan' || $plan1->handle == 'standard_plan' ? 'text-dark' : ''}}{{$plan1->handle == 'plus_plan' ? 'text-white' : ''}}">--}}
{{--                                        {{ trans('billing.billed_annually') }}--}}
{{--                                        <br>{{ trans('billing.or_month_to_month', ['price_per_month' => $plan1['price_per_month']]) }}--}}
{{--                                    </span>--}}

                                    <div class="px-4 px-sm-0 px-md-0 px-lg-2 mt-4">
                                        @if($plan1->handle == 'community_plan')
{{--                                            <a href="{{$plan1->btn_yearly_link}}"--}}
{{--                                               class="btn btn-block btn-primary yearly-btn">--}}
{{--                                                {{ trans('billing.choose_yearly_btn', ['price_per_year' => '$' . $plan1['price_per_year']]) }}--}}
{{--                                            </a>--}}
                                            <a href="{{$plan1->btn_monthly_link}}"
                                               class="btn btn-block yearly-btn btn-primary ">
                                                {{ trans('billing.choose_monthly_btn', ['price_per_month' => '$' . $plan1['price_per_month']]) }}
                                            </a>
                                        @elseif($plan1->handle == 'standard_plan')
{{--                                            <a href="{{$plan1->btn_yearly_link}}"--}}
{{--                                               class="btn btn-block btn-primary yearly-btn">--}}
{{--                                                {{ trans('billing.choose_yearly_btn', ['price_per_year' => '$' . $plan1['price_per_year']]) }}--}}
{{--                                            </a>--}}
                                            <a href="{{$plan1->btn_monthly_link}}"
                                               class="btn btn-block btn-primary yearly-btn">
                                                {{ trans('billing.choose_monthly_btn', ['price_per_month' => '$' . $plan1['price_per_month']]) }}
                                            </a>
                                        @else
{{--                                            <a href="{{$plan1->btn_yearly_link}}"--}}
{{--                                               class="btn btn-block btn-dark yearly-btn">--}}
{{--                                                {{ trans('billing.choose_yearly_btn', ['price_per_year' => '$' . $plan1['price_per_year']]) }}--}}
{{--                                            </a>--}}
                                            <a href="{{$plan1->btn_monthly_link}}"
                                               class="btn btn-block btn-dark yearly-btn text-white">
                                                {{ trans('billing.choose_monthly_btn', ['price_per_month' => '$' . $plan1['price_per_month']]) }}
                                            </a>
                                        @endif
                                    </div>
                                </div>

                                <div class="row justify-content-center mb-4">
                                    <div class="col-auto">
                                        @if($plan1->handle == 'community_plan' || $plan1->handle == 'standard_plan')
                                            <table class="table table-borderless p-0">
                                                <caption style="visibility: hidden"></caption>
                                                <th style="visibility: hidden"></th>
                                                <tbody>
                                                <tr>
                                                    <td class="p-0" style="width: 0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                                        <span class="ml-2 ml-md-1 Roboto">
                                                            {{ trans('billing.no_of_skus',['no_of_products' => $plan1['no_of_products']]) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                                    <span class="ml-2 ml-md-1 Roboto">
                                                        {{ trans('billing.no_of_retailers',['no_of_retailers' => $plan1['no_of_retailers']]) }}
                                                    </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">
                                                @if($plan1['handle'] == 'community_plan')
                                                                {{ trans('billing.catalogs_included') }}
                                                            @else
                                                                {{ trans('billing.additional_catalogs') }}
                                                            @endif
                                            </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">{{ trans('billing.brand_portal') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">{{ trans('billing.asset_management') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">{{ trans('billing.no_of_variants') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">{{ trans('billing.data_quality') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0" style="width: 0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">{{ trans('billing.storage', ['storage' => $plan1['storage']]) }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto">
                                                @if($plan1['handle'] == 'community_plan')
                                                                {{ trans('billing.language') }}
                                                            @else
                                                                {{ trans('billing.multi_language') }}
                                                            @endif
                                            </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                            <span class="ml-2 ml-md-1 Roboto">
                                                @if($plan1['handle'] == 'community_plan')
                                                    {{ trans('billing.currency') }}
                                                @else
                                                    {{ trans('billing.multi_currency') }}
                                                @endif
                                                </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_blue_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                            <span class="ml-2 ml-md-1 Roboto">
                                              @if($plan1['handle'] == 'community_plan')
                                                    {{ trans('billing.single_user') }}
                                                @elseif($plan1['handle'] == 'standard_plan')
                                                    {{ trans('billing.three_team_members') }}
                                                @else
                                                    {{ trans('billing.ten_team_members') }}
                                                @endif
                                            </span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        @else
                                            <table class="table table-borderless p-0">
                                                <caption style="visibility: hidden"></caption>
                                                <th style="visibility: hidden"></th>

                                                <tbody>
                                                <tr>
                                                    <td class="p-0" style="width: 0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                        {{--                                                    <em class="bi bi-check-circle-fill icon-success"></em>--}}
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                                        <span class="ml-2 ml-md-1 Roboto text-white">
                                                        {{ trans('billing.no_of_skus',['no_of_products' => $plan1['no_of_products']]) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">
                                                            {{ trans('billing.no_of_retailers',['no_of_retailers' => $plan1['no_of_retailers']]) }}
                                            </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">
                                                @if($plan1['handle'] == 'community_plan')
                                                                {{ trans('billing.catalogs_included') }}
                                                            @else
                                                                {{ trans('billing.additional_catalogs') }}
                                                            @endif
                                            </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">{{ trans('billing.brand_portal') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">{{ trans('billing.asset_management') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">{{ trans('billing.no_of_variants') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">{{ trans('billing.data_quality') }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0" style="width: 0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">
                                                            {{ trans('billing.storage', ['storage' => $plan1['storage']]) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1"><span
                                                            class="ml-2 ml-md-1 Roboto text-white">
                                                @if($plan1['handle'] == 'community_plan')
                                                                {{ trans('billing.language') }}
                                                            @else
                                                                {{ trans('billing.multi_language') }}
                                                            @endif
                                            </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                            <span class="ml-2 ml-md-1 Roboto text-white">
                                                @if($plan1['handle'] == 'community_plan')
                                                    {{ trans('billing.currency') }}
                                                @else
                                                    {{ trans('billing.multi_currency') }}
                                                @endif
                                                </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0">
                                                        <img src="{{asset('media/billing/check_white_16.png')}}" alt="">
                                                    </td>
                                                    <td class="p-0 pl-2 pt-1">
                                            <span class="ml-2 ml-md-1 Roboto text-white">
                                              @if($plan1['handle'] == 'community_plan')
                                                    {{ trans('billing.single_user') }}
                                                @elseif($plan1['handle'] == 'standard_plan')
                                                    {{ trans('billing.three_team_members') }}
                                                @else
                                                    {{ trans('billing.ten_team_members') }}
                                                @endif
                                            </span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        @endif
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
@endpush
