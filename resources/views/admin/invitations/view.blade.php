<?php ?>
@extends('layouts.app',['sidebar_display'=>false])
@section('content')
    <!-- Image and text -->
    <nav class="navbar navbar-light bg-light border-bottom">
        <a class="navbar-brand text-dark Poppins bold" href="https://apimio.com/">
            <img src="{{asset('media/sidebar/apimio-small.svg')}}"
                 width="30"
                 height="30"
                 class="d-inline-block align-top"
                 alt=""
                 loading="lazy">
            Apimio
        </a>
    </nav>


    <div class="container mt-3 ">
        <a href="{{url('admin101')}}"
           class="btn btn-outline-primary mt-3 mb-3 pt-1">
            <i class="flaticon flaticon-arrow-previous icon-size"></i>
        </a>
        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-8 col-lg-8 col-xl-9">
                        <div class="mt-2">
                            <h3 class="Poppins semibold m-0 text-dark">User Invitations</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{--        <div class="row mb-3">--}}
        {{--            <div class="col-12 col-md-12 col-lg-12 col-xl-3">--}}
        {{--                <form action="" class="filter-form float-xl-right">--}}
        {{--                    <div class="input-group mb-3">--}}
        {{--                        <input type="search" class="form-control" value="{{request('q')}}" name="q"--}}
        {{--                               placeholder="Search by Email"--}}
        {{--                               aria-label="Search" aria-describedby="search">--}}
        {{--                        <div class="input-group-append">--}}
        {{--                            <button class="btn btn-dark search" type="submit" id="search">--}}
        {{--                                <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">--}}
        {{--                            </button>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                </form>--}}
        {{--            </div>--}}
        {{--            <div class="col-12 col-md-12 col-lg-12 col-xl-9 mb-3">--}}
        {{--                <form action="" class="filter-form float-xl-right">--}}
        {{--                    <select class="btn btn-outline-light regular" name="login_method" id="">--}}
        {{--                        <option value="manual">All Users</option>--}}
        {{--                        <option value="google">Google login</option>--}}
        {{--                        <option value="shopify">Shopify login</option>--}}
        {{--                        <option value="verified">Verified</option>--}}
        {{--                        <option value="non-verified">Non Verified</option>--}}
        {{--                    </select>--}}
        {{--                    <button class="btn btn-outline-dark ml-3">Apply</button>--}}
        {{--                </form>--}}
        {{--            </div>--}}
        {{--        </div>--}}

        <div class="table-responsive">
            <table class="table">
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone Number</th>
                    <th>type</th>
                    <th>Status</th>
                </tr>
                @foreach($all_invitations as $invitation)
                    <tr>
                        <td>{{$loop->iteration}}</td>
                        <td>{{$invitation->fname}} {{$invitation->lname}}</td>
                        <td>{{$invitation->email}}</td>
                        <td>{{$invitation->phone}}</td>
                        <td>{{$invitation->type}}</td>
                        <td>{{$invitation->is_accepted == 0 ? 'pending' : 'Accepted'}}</td>
                    </tr>
                @endforeach
            </table>
            {!! $all_invitations->links() !!}
        </div>

    </div>
@endsection
