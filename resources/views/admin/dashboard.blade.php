<?php ?>
@extends('layouts.app',['sidebar_display'=>false])
@section('content')


    <!-- Image and text -->
    <nav class="navbar navbar-light bg-light border-bottom">
        <a class="navbar-brand text-dark Poppins bold" href="https://apimio.com/">
            <img src="{{asset('media/sidebar/apimio-small.svg')}}"
                 width="30"
                 height="30"
                 class="d-inline-block align-top"
                 alt=""
                 loading="lazy">
            Apimio
        </a>
    </nav>
    <div class="container mt-3">

        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-8 col-lg-8 col-xl-9">
                        <div class="mt-2">
                            <h3 class="Poppins semibold m-0 text-dark">Admin Dashboard</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <h4 class="semibold text-dark mb-4">Total Users: {{count($data['users'])}}</h4>

        <form action=""
              class="filter-form mb-4">
            <div class="row">
                <div class="col-12 col-md-6 col-lg-6 col-xl-4 mb-3">
                    <input type="search"
                           class="form-control"
                           value="{{request('q')}}"
                           name="q"
                           placeholder="Search by Email"
                           aria-label="Search"
                           aria-describedby="search">
                    {{--                        <div class="input-group-append">
                                                <button class="btn btn-dark search" type="submit" id="search">
                                                    <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">
                                                </button>
                                            </div>--}}
                </div>
                <div class="col-12 col-md-6 col-lg-6 col-xl-4 mb-3">
                    <input type="search"
                           class="form-control"
                           value="{{request('r')}}"
                           name="r"
                           placeholder="Hide specific records"
                           aria-label="Search"
                           aria-describedby="search">
                    {{--                        <div class="input-group-append">
                                                <button class="btn btn-dark search" type="submit" id="search">
                                                    <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">
                                                </button>
                                            </div>--}}

                </div>
                <div class="col-12 col-md-12 col-lg-12 col-xl-4 mb-3">

                    <select class="btn select-custom regular"
                            name="login_method"
                            id=""
                            style="width: 12rem;">
                        <option value="manual"
                            {{ isset($_GET['login_method'])  ? $_GET['login_method'] == 'manual' ? 'selected' : '' : ''}}>
                            All Users
                        </option>
                        <option value="google"
                            {{ isset($_GET['login_method']) ?  $_GET['login_method'] == 'google' ? 'selected' : '' : ''}}>
                            Google login
                        </option>
                        <option value="shopify"
                            {{ isset($_GET['login_method']) ? $_GET['login_method'] == 'shopify' ? 'selected' : '' : ''}}>
                            Shopify login
                        </option>
                        <option value="verified"
                            {{ isset($_GET['login_method']) ? $_GET['login_method'] == 'verified' ? 'selected' : '' : ''}}>
                            Verified
                        </option>
                        <option value="non-verified"
                            {{ isset($_GET['login_method']) ?  $_GET['login_method'] == 'non-verified' ? 'selected' : '' : ''}}>
                            Non Verified
                        </option>
                    </select>
                    <button class="btn btn-outline-dark ml-3"
                            style="width: 8rem;">
                        Apply
                    </button>

                </div>
            </div>
        </form>


        <div class="table-responsive">
            <table class="table">
                <tr>
                    <th scope="col">#</th>
                    <th scope="col">Name</th>
                    <th scope="col">Email</th>
                    <th scope="col">Phone Number</th>
                    <th scope="col">IP</th>
                    <th scope="col">Verified</th>
                    <th scope="col">Total Organizations</th>
                    <th scope="col">Total Products</th>
                    <th scope="col">Last Login</th>
                    <th scope="col">Login Type</th>
                    @if($_SERVER['PHP_AUTH_USER'] == 'devs' || $_SERVER['PHP_AUTH_PW'] == 'Apimiodevs123')
                        <th scope="col">Actions</th>
                    @endif
                </tr>
                @foreach($data['users'] as $data_user)
                    <tr>
                        <td>{{$loop->iteration}}</td>
                        <td>{{$data_user->fname}} {{$data_user->lname}}</td>
                        <td>{{$data_user->email}}</td>

                        {{--<td class="text-break">{{$data_user->phone}}</td>--}}

                        <td>{{$data_user->phone}}</td>
                        <td>{{$data_user->ip??'---'}}</td>

                        @if(isset($data_user->email_verified_at))
                            <td style="width: 10%" class="text-success">verified</td>
                        @else
                            <td style="width: 10%" class="text-danger">Not verified</td>
                        @endif
                        <td>
                            <a href="{{route('show.organizations',$data_user->id)}}">
                                {{$data_user->organizations_count}}
                            </a>
                        </td>
                        <td>{{$data_user->products_count}}</td>
                        <td>{{ \Carbon\Carbon::parse($data_user->last_login)->diffForHumans()}}</td>
                        <td>{{$data_user->login_type}}</td>
                        @if($_SERVER['PHP_AUTH_USER'] == 'devs' || $_SERVER['PHP_AUTH_PW'] == 'Apimiodevs123')
                            <td{{--  style="width: 25%"--}}>
                                <a href="{{route('account.switch', $data_user->id)}}"
                                   class="btn btn-outline-success mb-2"
                                   style="width: 8rem">
                                    Login Account
                                </a>
                                <br>
                                <a href="{{route('show.invitations',$data_user->id)}}"
                                   class="btn btn-outline-secondary mb-2"
                                   style="width: 8rem">
                                    Show Invitations
                                </a>
                                <br>
                                <a href="{{route('delete.user',$data_user->id)}}"
                                   class="btn btn-outline-danger mb-2">
                                    Delete User
                                </a>
                            </td>
                        @endif
                    </tr>
                @endforeach
            </table>
        </div>
        {{ $data['users'] }}

    </div>
@endsection
