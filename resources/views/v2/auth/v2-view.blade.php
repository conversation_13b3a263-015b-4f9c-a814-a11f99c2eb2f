@extends('layouts.v2.v2-layout')

@php
    $segment = Request::segment(1); // Get the second URL segment (e.g., "register" or "onboarding")
    $onboardingId = Request::segment(2); // Get the third URL segment for onboarding (e.g., "1" or "2")
@endphp
@section('titles')
    @if ($segment == 'register')
        Register
    @elseif ($segment == 'verify')
        Verify your account
    @elseif ($segment == 'products')
        Products
    @elseif ($segment == 'login')
        Login to your account
    @elseif ($segment == 'dashboard')
        Dashboard
    @elseif ($segment == 'onboarding')
        Onboarding
    @elseif ($segment == 'organizationselect')
        Organization Select
    @elseif ($segment == 'onboardingproducts')
        Onboarding - Products
    @elseif ($segment == 'syncproducts')
        Onboarding - Sync Products
    @else
        Onboarding
    @endif
@endsection

@section('content')
    <div id="v2-{{ $segment }}">
    </div>

    @if ($segment == 'register')
        @vite('resources/js/v2/pages/auth/Register.jsx') <!-- Register page JS -->
    @elseif ($segment == 'login')
        @vite('resources/js/v2/pages/auth/Login.jsx') <!-- Login page JS -->
    @elseif ($segment == 'verify')
        @vite('resources/js/v2/pages/auth/Verify.jsx') <!-- Verify page JS -->
    @elseif ($segment == 'dashboard')
        @vite('resources/js/v2/pages/dashboard/Dashboard.jsx')
    @elseif ($segment == 'products')
        @vite('resources/js/v2/pages/product/ManageProducts.jsx')
    @elseif ($segment == 'onboarding')
        @vite('resources/js/v2/pages/onboarding/Onboarding.jsx')
    @elseif ($segment == 'organizationselect')
        @vite('resources/js/v2/components/OrganizationSelect.jsx')
    @elseif ($segment == 'onboardingproducts')
        @vite('resources/js/v2/components/OnboardingNine.jsx')
    @elseif ($segment == 'syncproducts')
        @vite('resources/js/v2/components/ShopifyProductsSync.jsx')
    @elseif ($segment == 'welcome')
        @vite('resources/js/v2/components/Welcome.jsx')
    @else
        @vite('resources/js/v2/pages/onboarding/DefaultOnboarding.jsx') <!-- Default Onboarding JS -->
    @endif
@endsection
