@extends('layouts.app',['sidebar_display'=>true])
@section('titles','File')
@push('header_scripts')
    <!--For Phone Flags-->
    <link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet" />
    <style>
        button.filepond--file-action-button.filepond--action-revert-item-processing {
            display: none;
        }
        .filepond--file-action-button.filepond--file-action-button {
            height: 1.625em !important;
        }
    </style>
@endpush
@section('content')

    <div class="row">
        <div style="background-color: chocolate" class="container">
            <form action="{{route('files.store')}}" method="post" enctype="multipart/form-data">
                @csrf
                <input name="file[]" type="file" multiple id="files">
                @error('file')
                {{ $message }}
                @enderror

{{--                <input name="file[]" type="file">--}}
{{--                @error('file')--}}
{{--                {{$message}}--}}
{{--                @enderror--}}

            </form>

            <form action="{{route('files.store')}}" method="post" enctype="multipart/form-data">
                @csrf
                <input name="file[]" type="file" multiple>
                @error('file')
                {{ $message }}
                @enderror

                {{--                <input name="file[]" type="file">--}}
                {{--                @error('file')--}}
                {{--                {{$message}}--}}
                {{--                @enderror--}}
                <button type="submit">Submit</button>
            </form>
        </div>
    </div>
@endsection
@push('footer_scripts')
    <script src="https://unpkg.com/filepond@^4/dist/filepond.js"></script>
    <script>
        // Get a reference to the file input element
        const inputElement = document.querySelector('input[id="files"]');

        // Create a FilePond instance
        const pond = FilePond.create(inputElement);
        FilePond.setOptions(
            {
                server:{
                    url:'/upload',
                    headers:{
                        'X-CSRF-TOKEN':'{{ csrf_token() }}'
                    }
                }

            }
        )
    </script>
@endpush
