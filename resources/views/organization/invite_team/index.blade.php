<?php ?>
@extends('layouts.app_new')
@section('titles','Team')
@section('content')
    <div>
        <x-products.page-title name="{{trans('invite_team_index.page_title')}}" description="{{trans('invite_team_index.page_description')}}"
                               links="true" button="true">
            <x-slot name="addbutton">
                <a href="{{route('organization.invite_team.create')}}" id="add-attribute"
                   class="btn btn-primary float-lg-right float-md-right only-disabled w-100">
                    {{trans('invite_team_index.add_team_member_btn')}}
                </a>
            </x-slot>
        </x-products.page-title>

        {{--<div class="row">
            <div class="col-12 col-md-12 col-lg-12 col-xl-3">
                <x-general.search-bar placeholder="Search for attributes"/>
            </div>
        </div>--}}
        <div class="row">
            <div class="col-12 p-0">
                @if(count($invites) > 0)
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th scope="col">
                                    {{ __('First Name') }}
                                </th>
                                <th scope="col">
                                    {{ __('Last Name') }}
                                </th>
                                <th scope="col">
                                    {{ __('Email') }}
                                </th>
                                <th scope="col">
                                    {{ __('Total Permissions') }}
                                </th>
                                <th class="text-end">
                                    {{ __('Actions') }}
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($invites as $invite)
                                <tr>
                                    <td>
                                        {{__($invite->first_name)}}
                                    </td>
                                    <td>
                                        {{$invite->last_name}}
                                    </td>
                                    <td>
                                        {{$invite->email}}
                                    </td>
                                    <td>
                                        {{$invite->permissions->count()}}
                                    </td>
                                    <td class="text-end">
                                        <a href="{{route('organization.invite_team.edit',$invite->id)}}"
                                           class="pro_attr_edit mr-3 edit-btn text-decoration-none">
                                            <i class="fa-regular fa-pen-to-square fs-20"></i>
                                        </a>
                                        <a href="#" id="modal" data-id="{{$invite->id}}"
                                           data-bs-retailer-name=""
                                           data-bs-toggle="modal" data-bs-target="#delete-modal-{{$invite->id}}" class="text-decoration-none">
                                            <i class="fa fa-regular fa-trash-can fs-20 text-danger"></i>
                                        </a>
                                    </td>
                                </tr>
                                <x-assets.delete-modal text="Are you sure you want to delete team member?" id="{{$invite->id}}" button="Delete Member" title="DELETE TEAM MEMBER" url="{{route('organization.invite_team.delete',$invite->id)}}" type="invite"/>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <x-general.empty-page description="{{trans('invite_team_index.page_empty')}}"/>
                @endif
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')

    <script>

        let attrid ;
        let attrname;

        $(".btn-delete").click(function () {
            attrid = $(this).attr('data-id');
            attrname = $(this).attr('data-retailer-name');
            document.getElementById('name').innerHTML = attrname;
        });

        function del() {

            let form = document.getElementById('pro_attr_del_btn ');
            form.setAttribute('action', "delete/" + attrid);
            form.submit();
        }

        $(document).ready(function () {
            $('#AssignCategory').SumoSelect({
                placeholder: 'Select Category',
                csvDispCount: 4,
                captionFormat: '{0} Selected',
                captionFormatAllSelected: 'All Options Selected',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: true,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
        });
    </script>
@endpush
