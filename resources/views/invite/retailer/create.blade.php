@extends('layouts.app_new')
@section('titles','Invite Retailer')
@section('content')
    <div>
        <x-invite.title name="{{trans('retailer_create.page_title')}}"
                               description="{{trans('retailer_create.page_description')}}"
                               badge="true">
            <x-slot name="addbadge">
                @if(isset($data))
                <span>
                     @if($data['retailer']->get_status()==1 )<span class="badge badge-pill badge-success">Accepted</span>
                    @else                  {!! $data['retailer']->get_Status_badge() !!}
                    @endif

                </span>
                @endif
            </x-slot>

        </x-invite.title>


        <form action="{{ isset($data)?route('retailer.update',$data['retailer']->id):route('retailer.store') }}"
              method="POST" class="formStyle"
              id="add_retailer_form">
            @csrf
            @if(isset($data))
                @method('PUT')
                <input type="hidden" value="{{$data['retailer']->id}}" name="id">
            @endif
            <input type="hidden" value="retailer" name="type">
            <div class="row">
                <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                            <div class="form-group mb-3">
                                <label for="fname">
                                    {{trans('retailer_create.first_name')}}
                                    &nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control text-capitalize @error('fname') is-invalid @enderror"
                                       name="fname"
                                       id="fname"
                                       value="{{old("fname",isset($data) ?$data['retailer']->fname :null)}}"
                                       required>
                                @error('fname')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-xl-6 col-12 ">
                            <div class="form-group mb-3">
                                <label for="lname">
                                    {{trans('retailer_create.last_name')}}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control text-capitalize @error('lname') is-invalid @enderror"
                                       name="lname"
                                       id="lname"
                                       value="{{old("lname",isset($data) ?$data['retailer']->lname :null) }}">
                                @error('lname')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="email">
                            {{trans('retailer_create.email')}}&nbsp;<span class="text-danger">*</span>
                        </label>
                        <input id="email" type="email" class="form-control @error('email') is-invalid @enderror"
                               name="email" value="{{old("email",isset($data) ?$data['retailer']->email :null)}}" required>
                        @error('email')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>
                    <div class="form-group mb-3">
                        <label for="phone">
                            {{trans('retailer_create.phone_number')}}
                        </label>
                        <input id="phone" type="tel" name="phone" value="{{old("phone",isset($data) ?$data['retailer']->phone :null) }}"
                               oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                               class="form-control @error('phone') is-invalid @enderror"
                               onkeypress="return searchKeyPress(event);"/>
                        @error('phone')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>

                    <p class="Poppins semibold mb-0 text-light-grey">{{trans('retailer_create.assign_catalog')}}</p>
                    <hr class="divider mt-2 mb-4">
                    <div class="form-group">
                        <label for="catalog">
                            {{trans('retailer_create.select_catalog')}}&nbsp;<span class="text-danger">*</span>
                        </label>
                        <select placeholder="Select Stores" id="Catalog" multiple="multiple" name="channel_ids[]"
                                class="form-control btn sumoselect @error('channel_ids') is-invalid @enderror">
                            @foreach($channels as $channel)
                                <option value="{{ $channel->id }}"
                                        class="Poppins regular text-color text-capitalize"
                                    {{ old('channel_ids')
                                                ?  in_array($channel->id, old("channel_ids"))
                                                : (isset($data)
                                                ?(in_array($channel->id, array_column($data['retailer']->channels_without_scope->toArray(),'id'))
                                                ?'selected'
                                                :null)
                                                :null )}}
                                >{{ $channel->name }}</option>
                            @endforeach
                        </select>
                        @error('channel_ids')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror

                    </div>
                    <div class="d-flex justify-content-end mt-40">

                        <a id="cancel-vendor" href="{{ route('retailer.index') }}"
                           class="btn btn-outline-danger">
                            {{trans('retailer_create.cancel_btn')}}
                        </a>
                        @if(isset($data))
                            <button type="submit" href="{{route('retailer.update', $data['retailer']->id)}}" class="btn btn-primary">
                                {{trans('retailer_create.retailer_update')}}
                            </button>
                        @else
                            <button type="submit" href="" class="btn btn-primary ms-2">
                                {{trans('retailer_create.add_and_invite_btn')}}
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer_scripts')
    <!--Phone Flags-->
    <script>
        let input = document.querySelector("#phone");
        ipLookUp();

        // get current user country
        function ipLookUp() {
            $.ajax({
                dataType: 'json',
                url: 'https://api.hostip.info/get_json.php',
                success: function (data) {
                    let $ip = data['ip'],
                        $city = data['city'],
                        $countryCode = data['country_code'],
                        $countryName = data['country_name'];
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                        initialCountry: $countryCode
                    }));
                },
                error: function (jqXHR, textStatus) {
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                    }));
                }
            });
            return 0;
        }

        function searchKeyPress(e) {
            e = e || window.event;
            if (e.keyCode === 13) {
                document.getElementById('registeration_next_btn_id').click();
                return false;
            }
            return true;
        }
    </script>
    <script>
        $("#country_selector").countrySelect();
        @isset($data)
        @if($data['retailer']->get_Status() != 4)
        document.getElementById("email").setAttribute("readonly" ,"readonly");
        @if($data['retailer']->get_Status() == 1 || $data['retailer']->get_Status() == 5)

        document.getElementById("fname").setAttribute("readonly" ,"readonly")
        document.getElementById("lname").setAttribute("readonly" ,"readonly")
        document.getElementById("phone").setAttribute("readonly" ,"readonly");
        @endif
        @endisset
        @endif
    </script>
@endpush
