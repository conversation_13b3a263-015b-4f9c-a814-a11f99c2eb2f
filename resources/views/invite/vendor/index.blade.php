@extends('layouts.app_new')
@section('titles','All Vendors')
@section('content')
    <div>
        <x-products.page-title name="{{trans('vendors.page_title')}}"
                               description="{{trans('vendors.page_description')}}"
                               links="false"
                               button="true">
            <x-slot name="addbutton">
                <a href="{{route('vendor.create')}}" id="add-retailer"
                   class="btn btn-primary float-lg-right float-md-right only-disabled">
                    {{trans('vendors.invite_vendor_btn')}}
                </a>
            </x-slot>
        </x-products.page-title>

        <form action="" method="GET" id="search-Vendor-listing" class="formStyle">
        <div class="col-12 col-md-12 col-lg-12 col-xl-4">
            <div class="d-flex flex-row">
                <div class="input-group">
                    <input type="text" class="form-control" value="{{request('q')}}" name="q"
                           placeholder="Search by Email"
                           aria-label="Search by Email" aria-describedby="search">
                    @if(request()->has('q'))
                        <a href="{{ \Illuminate\Support\Facades\Request::url() }}"> </a>
                    @endif
                    <div class="input-group-append">
                        <button class="search" type="submit" id="search">
                            <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="search">
                        </button>
                    </div>
                </div>
            </div>
        </div>
        </form>

        @if(count($data["vendors"]) > 0)
            <div class="table-responsive mt-4">
                <table class="table table-hover table-borderless">
                    <thead>
                    <tr>
                        <th scope="col">{{__("NAME")}}</th>
                        <th scope="col">{{__("EMAIL")}}</th>
                        <th scope="col"
                            onclick="reverse({{$data['vendors']}})"
                            id ="Status"
                            name="Status"
                            class="cursor-pointer">{{__("STATUS")}}</th>
                        <th scope="col">{{__("Shared Stores")}}</th>
                        <th scope="col"
                            class=" text-end">{{__("Actions")}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($data["vendors"] as $vendor)
                        <tr onclick="location.href ='{{ route("vendor.show", $vendor->id) }}'"
                            class="cursor-pointer">
                            {{--                                when invite is connected--}}
                            @if($vendor->get_status() == 1)
                                {{--                                when connected invite is sent by user--}}
                                @if($vendor->email != auth()->user()->email)
                                    <td>{{  $vendor->user_sender_without_scope()->pluck("fname")->first() . " " .$vendor->user_sender_without_scope()->pluck("lname")->first() }}</td>
                                    <x-invite.arrow :emailAuth="$vendor->email" :email="$vendor->email"/>
                                    <td>{!! $vendor->get_status_badge() !!}</td>
                                    {{--                                    when connected invite is recieved by user--}}
                                @else
                                    <td>{{ $vendor->organization_sender_without_scope->users_without_scope->first()->fname . " " .$vendor->organization_sender_without_scope->users_without_scope->first()->lname   }}</td>
                                    <x-invite.arrow :emailAuth="$vendor->email" :email="$vendor->organization_sender_without_scope->users_without_scope->first()->email" />
                                   <td>{!! $vendor->organization_sender_without_scope->users_without_scope->first()->get_user_status($vendor) !!}</td>
                                @endif
                            {{--                            When invite is sent--}}
                            @elseif($vendor->get_status() == 4)
                                <td class="text-capitalize">{{ $vendor->fname . " " . $vendor->lname }}</td>
                                <x-invite.arrow :emailAuth="$vendor->email" :email="$vendor->email"/>
                                <td>{!! $vendor->get_status_badge() !!}</td>

                                {{--                                when invite is declined or disconnected--}}
                            @elseif(($vendor->get_status() == 2 || 5) && ($vendor->email != auth()->user()->email) )
                                <td>{{ \App\User::where('email',$vendor->email)->pluck('fname')->first() . " " . \App\User::where('email',$vendor->email)->pluck('lname')->first() }}</td>
                                <x-invite.arrow :emailAuth="$vendor->email" :email="$vendor->email"/>
                                <td>{!! $vendor->get_status_badge() !!}</td>

                            @else
                                <td>{{ $vendor->organization_sender_without_scope->users_without_scope->first()->fname . " " . $vendor->organization_sender_without_scope->users_without_scope->first()->lname }}</td>
                                <x-invite.arrow :emailAuth="$vendor->email" :email="$vendor->organization_sender_without_scope->users_without_scope->first()->email" />
                                <td class="Roboto">{!! $vendor->organization_sender_without_scope->users_without_scope->first()->get_user_status($vendor) !!}</td>

                            @endif
                            {{--                        Edit button when invite is recieved--}}
                            @if( ($vendor->get_status() == 3) || ($vendor->email == auth()->user()->email) )
                                <td>{{ $vendor->get_catalogs() }}</td>
                                <td
                                    style="filter:invert(1) grayscale(100%); cursor: default">
                                    <a class="pro_ven_edit mr-3 text-decoration-none" title="{{trans('vendors.incoming')}}" style="cursor: default">
                                        <i class="fa-regular fa-pen-to-square fs-20"></i>
                                    </a>
                                </td>
                            @else
                                <td>{{ $vendor->get_catalogs() }}</td>
                                <td class="text-end">
                                    <a href="{{route('vendor.edit', $vendor->id)}}"   class="pro_ven_edit mr-3 edit-btn text-decoration-none">
                                        <i class="fa-regular fa-pen-to-square fs-20"></i>
                                    </a>
                                </td>
                            @endif
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <x-general.empty-page description="{{trans('vendors.page_empty')}}"/>
        @endif
    </div>

    <!-- Modal DELETE-->
    <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title Poppins semibold"
                        id="exampleModalLabel">{{trans('vendors.modal_title')}}</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="Poppins regular">{{trans('vendors.modal_description')}}</p>

                    <div class="modal-footer p-0">
                        <button type="button" data-dismiss="modal" id="delete-cancel-btn"
                                class="btn btn-black text-dark Roboto bold float-left shadow"
                                style="width: 120px;">
                            {{trans('vendors.cancel_btn')}}
                        </button>
                        <form action="#" id="delete-family" method="post">
                            @csrf
                            @method('DELETE')
                            <a href="#" id="pro_fam_del_btn" class="ripplelink btn del-btn shadow" onclick="del()">
                                {{trans('vendors.delete_btn')}}
                            </a>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
@endpush

