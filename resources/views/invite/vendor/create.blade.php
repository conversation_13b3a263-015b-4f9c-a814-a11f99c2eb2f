@extends('layouts.app_new')
@section('titles','Invite Vendor')
@section('content')
    <div>

        <x-invite.title name="{{trans('vendors_create.page_title')}}"
                        description="{{trans('vendors_create.page_description')}}"
                        badge="true">
            <x-slot name="addbadge">
                @if(isset($data))
                    <span>
                    @if($data['vendor']->get_status()==1 )<span class="badge badge-pill badge-success">Accepted</span>
                        @else{!! $data['vendor']->get_Status_badge() !!}
                        @endif
                </span>
            </x-slot>
            @endif
        </x-invite.title>


        <form action="{{ isset($data)?route('vendor.update',$data['vendor']->id):route('vendor.store') }}"
              method="POST" class="formStyle"
              id="add_retailer_form">
            @csrf
            @if(isset($data))
                @method('PUT')
                <input type="hidden" value="{{$data['vendor']->id}}" name="id">
            @endif
            <input type="hidden" value="vendor" name="type">
            <div class="row">
            <!--
                <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                    <p class="Poppins semibold mb-0 hex-A5">{{__("COMPANY DETAILS")}}</p>
                    <hr class="divider mt-2 mb-4">
                    <div class="form-group">
                        <label class="ml-1 Roboto bold black" for="cname">
                            {{ __('Company Name') }}
                </label>
                <input name="cname" value="" id="cname" type="text"
                       class="input-height form-control rounded-lg auth-input-fields @error('cname') is-invalid @enderror" autofocus/>
                        @error('cname')
                <span class="text-danger" role="alert">
                    <small>{{ $message }}</small>
                        </span>
                        @enderror
                </div>
                <div class="form-group">
                    <label class="ml-1 Roboto bold black" for="address">
{{ __('Street Address') }}
                </label>
                <input name="address" value="" id="address" type="text"
                       class="input-height form-control rounded-lg auth-input-fields @error('address') is-invalid @enderror" />
                        @error('address')
                <span class="text-danger" role="alert">
                    <small>{{ $message }}</small>
                        </span>
                        @enderror
                </div>
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                        <div class="form-group">
                            <label class="ml-1 Roboto bold black" for="city">
{{ __('City') }}
                </label>
                <input type="text" name="city" value=""
                       class="input-height form-control rounded-lg auth-input-fields @error('city') is-invalid @enderror">
                                @error('city')
                <span class="text-danger" role="alert">
                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-xl-6 col-12 ">
                <div class="form-group">
                    <label class="ml-1 Roboto bold black" for="state">
{{ __('State') }}
                </label>
                <input type="text" name="state" value=""
                       class="input-height form-control rounded-lg auth-input-fields @error('state') is-invalid @enderror">
                                @error('state')
                <span class="text-danger" role="alert">
                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-xl-6 col-12">

                <div class="form-group">
                    <label class="ml-1 Roboto bold black" for="country">
{{ __('Country') }}
                </label>
                <input type="text" name="country" value="" id="country_selector"
                       class="input-height form-control rounded-lg auth-input-fields @error('country') is-invalid @enderror">
                                @error('country')
                <span class="text-danger" role="alert">
                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-xl-6 col-12 ">
                <div class="form-group">
                    <label class="ml-1 Roboto bold black" for="zipcode">
{{ __('Zip code') }}
                </label>
                <input type="text" name="zipcode" value=""
                       class="input-height form-control rounded-lg auth-input-fields @error('zipcode') is-invalid @enderror">
                                @error('zipcode')
                <span class="text-danger" role="alert">
                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                </div>
            </div>
        </div>
    </div>
-->
                <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                            <div class="form-group mb-3">
                                <label for="fname">
                                    {{trans('vendors_create.first_name')}}&nbsp;<span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control @error('fname') is-invalid @enderror"
                                       name="fname"
                                       id="fname"
                                       value="{{old("fname",isset($data) ?$data['vendor']->fname :null)}}"
                                       required>
                                @error('fname')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-xl-6 col-12 ">
                            <div class="form-group">
                                <label for="lname">
                                    {{trans('vendors_create.last_name')}} <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control @error('lname') is-invalid @enderror"
                                       name="lname"
                                       id="lname"
                                       value="{{old("lname",isset($data) ?$data['vendor']->lname :null) }}">
                                @error('lname')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="email">
                            {{trans('vendors_create.email')}}&nbsp;<span style="color: #ff8178">*</span>
                        </label>
                        <input type="email"
                               id="email"
                               class="form-control @error('email') is-invalid @enderror"
                               name="email"
                               value="{{old("email",isset($data) ?$data['vendor']->email :null)}}"
                               required>
                        @error('email')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>
                    <div class="form-group mb-3">
                        <label for="phone">
                            {{trans('vendors_create.phone_number')}}
                        </label>
                        <input id="phone"
                               type="tel"
                               name="phone"
                               value="{{old("phone",isset($data) ?$data['vendor']->phone :null) }}"
                               oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                               class="form-control @error('phone') is-invalid @enderror"/>
                        @error('phone')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>

                    <p class="mb-0 clr-grey fw-700">{{trans('vendors_create.assign_catalog')}}</p>
                    <hr class="divider mt-2 mb-4">
                    <div class="form-group">
                        <label for="catalog">
                            {{trans('vendors_create.select_catalog')}}&nbsp;<span style="color: #ff8178">*</span>
                        </label>
                        <select placeholder="Select Stores" id="Catalog" multiple="multiple" name="channel_ids[]"
                                class="form-control btn sumoselect @error('channel_ids') is-invalid @enderror">
                            @foreach($channels as $key => $channel)
                                <option value="{{ $channel->id }}"
                                        class="Poppins regular text-color text-capitalize"
                                    {{ old('channel_ids')
                                                ?  in_array($channel->id, old("channel_ids"))
                                                : (isset($data)
                                                ?(in_array($channel->id, array_column($data['vendor']->channels_without_scope->toArray(),'id'))
                                                ?'selected'
                                                :null)
                                                :null )}}
                                >{{ $channel->name }}</option>
                            @endforeach
                        </select>
                        @error('channel_ids')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror

                    </div>
                    @if(isset($data))
                        <div class="d-flex justify-content-end mt-3">
                            <a href="{{ route('vendor.index') }}"
                               class="btn btn-outline-danger">
                                {{trans('vendors_create.cancel_btn')}}
                            </a>
                            <button type="submit" class="btn btn-primary ms-2">
                                {{trans('vendors_create.vendor_update')}}
                            </button>
                        </div>

                    @else
                        <div class="d-flex justify-content-end mt-3">
                            <a href="{{ route('vendor.index') }}"
                               class="btn btn-outline-danger" id="cancel-btn">
                                {{trans('vendors_create.cancel_btn')}}
                            </a>
                            <button type="submit" href="" class="btn btn-primary ms-2">
                                {{trans('vendors_create.add_and_invite_btn')}}
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer_scripts')
    <!--Phone Flags-->
    <script>
        let input = document.querySelector("#phone");
        ipLookUp();

        // get current user country
        function ipLookUp() {
            $.ajax({
                dataType: 'json',
                url: 'https://api.hostip.info/get_json.php',
                success: function (data) {
                    let $ip = data['ip'],
                        $city = data['city'],
                        $countryCode = data['country_code'],
                        $countryName = data['country_name'];
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                        initialCountry: $countryCode
                    }));
                },
                error: function (jqXHR, textStatus) {
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                    }));
                }
            });
            return 0;
        }


    </script>
    <script>
        $("#country_selector").countrySelect();

        @isset($data)
        @if($data['vendor']->get_Status() != 4)
        document.getElementById("email").setAttribute("readonly", "readonly");
        @if($data['vendor']->get_Status() == 1 || $data['vendor']->get_Status() == 5)

        document.getElementById("fname").setAttribute("readonly", "readonly")
        document.getElementById("lname").setAttribute("readonly", "readonly")
        document.getElementById("phone").setAttribute("readonly", "readonly");
        @endif
        @endif
        @endisset
    </script>

    <style>
        .SumoUnder{
            position :absolute;
        }
    </style>
@endpush
