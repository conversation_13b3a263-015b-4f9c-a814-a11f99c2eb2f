<!DOCTYPE html>
<html lang="en">

<head>
    @viteReactRefresh

    @if (env('APP_ENV') != 'lambda')
        @vite(['resources/js/app.jsx'])

        @if (isset($page))
            @inertiaHead
        @endif
    @endif
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if (\Illuminate\Support\Facades\App::environment(['staging', 'development', 'local']))
        <meta name="robots" content="noindex">
    @endif

    <title>Apimio - @yield('titles', 'Apimio')</title>
    @stack('meta_tags')

    <link rel="apple-touch-icon" sizes="76x76" href="{{ asset('img/apple-icon.png') }}">
    <link rel="icon" type="image/png" href="{{ asset('1favicon.ico') }}">
    <link rel="icon" href="/assets/favicon.ico" type="image/x-icon" />

    <!-- Style -->
    <script src="https://kit.fontawesome.com/58a77f8c62.js" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    @if (\Illuminate\Support\Facades\App::environment('production'))
        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-TCSKR8R');
        </script>
    @endif
    @stack('header_scripts')
</head>

<body>
    <div>
        @section('content')
        @show
    </div>



</body>

</html>
