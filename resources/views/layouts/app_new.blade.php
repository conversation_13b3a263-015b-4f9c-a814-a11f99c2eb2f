<!DOCTYPE html>
<html lang="en">

<head>

    @viteReactRefresh

    @if (env('APP_ENV') != 'lambda')


        @if (isset($page))
            @vite(['resources/js/app.jsx'])
            @inertiaHead
        @endif
    @endif
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if (\Illuminate\Support\Facades\App::environment(['staging', 'development', 'local']))
        <meta name="robots" content="noindex">
    @endif

    <title>Apimio - @yield('titles', 'Apimio')</title>
    @stack('meta_tags')


    <link rel="apple-touch-icon" sizes="76x76" href="{{ asset('img/apple-icon.png') }}">
    <link rel="icon" type="image/png" href="{{ asset('1favicon.ico') }}">

    <link rel="icon" href="/assets/favicon.ico" type="image/x-icon" />
    <!-- Style -->
    <!-- Bootstrap-->
    <link href="{{ asset('bootstrap-5.0.2-dist/css/bootstrap.min.css') }}" rel="stylesheet" crossorigin="anonymous">
    <!--Fonts Poppins and Roboto-->
    <!--For Phone Flags-->
    <link rel="stylesheet" href="{{ asset('css/plugin/intlTelInput.css?v=' . env('APP_VERSION')) }}" type="text/css" />
    <link rel="stylesheet" href="{{ asset('css/plugin/countrySelect.css?v=' . env('APP_VERSION')) }}" type="text/css" />
    <!-- Fonts and Icon-->

    {{-- https://sweetalert2.github.io/#examples --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    <!--sumoselect-->
    <link rel="stylesheet" href="{{ asset('css/plugin/sumoselect.css?v=' . env('APP_VERSION')) }}">

    <!--tagger-->
    <link href="{{ asset('css/tagger.css?v=' . env('APP_VERSION')) }}" rel="stylesheet">
    <script src="{{ asset('js/tagger.js?v=' . env('APP_VERSION')) }}"></script>

    <!--Icons-->
    <link rel="stylesheet" href="{{ asset('treeselect/style.css?v=' . env('APP_VERSION')) }}">
    <link rel="stylesheet" href="{{ asset('css/sync-popup.css?v=' . env('APP_VERSION')) }}">

    <!--for datatable-->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css">

    <script src="https://kit.fontawesome.com/58a77f8c62.js" crossorigin="anonymous"></script>

    @if (\Illuminate\Support\Facades\App::environment(['staging', 'production']))
        @if (\Illuminate\Support\Facades\Auth::check())
            <script>
                /*
                 * Tags manager authenticated user data
                 * */
                let arguments = {
                    "id": '{{ \Illuminate\Support\Facades\Auth::id() }}',
                    "fname": '{{ \Illuminate\Support\Facades\Auth::user()->fname }}',
                    "lname": '{{ \Illuminate\Support\Facades\Auth::user()->lname }}',
                };
            </script>
        @endif
    @endif
    @if (\Illuminate\Support\Facades\App::environment('staging'))
        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-NNMH4W9');
        </script><!-- End Google Tag Manager -->
    @endif
    @if (\Illuminate\Support\Facades\App::environment('production'))
        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-TCSKR8R');
        </script><!-- End Google Tag Manager -->
    @endif
    @stack('header_scripts')
    <link type="text/css" href="{{ asset('./assets/scss/screen.css?v=' . env('APP_VERSION')) }}" rel="stylesheet">

</head>

<body>

    @php

          $sidebar_displayjs = isset($page) && isset($page['props']['sidebar_display'])
        ? ($page['props']['sidebar_display'] == 1 ? true : false)
        : true;
    @endphp

    <div class="container-fluid">
        @if (\Illuminate\Support\Facades\App::environment('staging'))
            <!-- Google Tag Manager (noscript) --><noscript><iframe
                    src="https://www.googletagmanager.com/ns.html?id=GTM-NNMH4W9" height="0" width="0"
                    style="display:none;visibility:hidden"></iframe></noscript><!-- End Google Tag Manager (noscript) -->
        @endif
        @if (\Illuminate\Support\Facades\App::environment('production'))
            <!-- Google Tag Manager (noscript) --><noscript><iframe
                    src="https://www.googletagmanager.com/ns.html?id=GTM-TCSKR8R" height="0" width="0"
                    style="display:none;visibility:hidden"></iframe></noscript><!-- End Google Tag Manager (noscript) -->
        @endif

        @auth
            @php
                $hideSidebarForUrls = ['on-boarding', 'organization']; // Add other URLs or patterns as needed
                $currentSegment = Request::segment(1);
            @endphp
{{-- @inertia --}}
            @if (in_array($currentSegment, $hideSidebarForUrls))
                <div class="container-fluid h-100 responsive-screen">
                    @section('content')
                    @show
                </div>
            @elseif(isset($page))
                @if (isset($sidebar_displayjs) ? $sidebar_displayjs : true)
                    @if ($currentSegment == 'products' || $currentSegment == 'channel')
                        @include('layouts.navs.product_sidebar')
                    @else
                        @include('layouts.navs.newsidebar')
                    @endif
                    <!-- SIDEBAR CODE END -->
                    <!-- BODY CODE STARTS -->
                    <div class="col py-3 px-1 right-side">
                        <div class="container-fluid px-1 px-md-2 px-lg-3 px-xl-4 responsive-screen">
                            @inertia
                        </div>
                        <div class="mt-80"></div>
                    </div>
                    <!-- BODY CODE END -->
        </div>
    @else
        <div class="container-fluid h-100 responsive-screen">
            @inertia
        </div>
        @endif
        <script src="https://cdn.tailwindcss.com"></script>
    @else
        @if (isset($sidebar_display) ? $sidebar_display : true)
            <div class="row ">
                <!-- SIDEBAR CODE STARTS -->
                @if ($currentSegment == 'products' || $currentSegment == 'channel')
                    @include('layouts.navs.product_sidebar')
                @else
                    @include('layouts.navs.newsidebar')
                @endif
                <!-- SIDEBAR CODE END -->
                <!-- BODY CODE STARTS -->
                <div class="col py-3 px-1 right-side">
                    <div class="container-fluid px-1 px-md-2 px-lg-3 px-xl-4 responsive-screen">
                        @section('content')
                        @show
                    </div>
                    <div class="mt-80"></div>
                </div>
                <!-- BODY CODE END -->
            </div>
        @else
            <div class="container-fluid h-100 responsive-screen">
                @section('content')
                @show
            </div>
        @endif
        @endif
    @else
        <div class="container-fluid h-100 responsive-screen">
            @section('content')
            @show
        </div>
    @endauth
    </div>



    <!-- import alert on file upload Modal -->
    <x-alerts.import-alert />
    {{-- script file --}}

    <!--   Core JS Files   -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <script src="{{ asset('bootstrap-5.0.2-dist/js/bootstrap.bundle.min.js') }}" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>





    {{-- bulk edit modal --}}
    @php
        $versions = new Illuminate\Database\Eloquent\Collection();
        if (Auth::check()) {
            $organization = auth()
                ->user()
                ->organizations()
                ->where('organizations.id', auth()->user()->organization_id)
                ->first();
            if ($organization) {
                $versions = $organization->versions()->get();
            }
        }
    @endphp

    <x-alerts.upgrade-billing-modal />



    <div class="modal fade" id="bulk_edit_modal" data-keyboard="false" tabindex="-1"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="exampleModalLabel">Bulk Edit</h3>
                    <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal"
                        aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="d-flex justify-content-center align-items-center flex-column mb-0 pb-3">
                        <span style="font-size: 18px; font-weight: 700">Choose the language in which you plan to
                            perform bulk editing?</span>
                    </p>
                    <form action="{{ route('fetch.bulk.edit.products') }}" id="bulk_edit_form" method="POST">
                        @csrf
                        <div class="form-group">
                            <input type="hidden" class="bulk_productIds" name="productIds" value="">
                            <input type="hidden" class="bulk_products_filter_array" name="bulk_edit_filter_products"
                                value="">
                            <select name="bulk_edit_version_id" id="bulk_edit_version_id" class="form-control">
                                @foreach ($versions as $version)
                                    <option value="{{ $version->id }}">{{ $version->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-primary w-100" id="bulk_edit_modal_submit">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </footer>

    <x-assets.delete-modal text="Are you sure you want to delete the selected products?" button="Delete Products"
        title="Delete Products" id="bulk-delete" url="{{ route('products.bulk.delete') }}" type="bulk-delete" />

    {{-- This div is dynamic bulk assign modal --}}
    <div id="bulk-assign-modal-div"></div>
    <input type="hidden" id="filter_total_product_count" value="0" />
    <input type="hidden" class="filter_bulk_productIds" value="" />
    <input type="hidden" id="filter_total_product_array" value="" />

    {{-- this div is dynamic bulk price update modal--}}
    <div id="bulk-price-update-modal-div"></div>

    {{-- bulk footer start --}}
    <footer class="bg-light mapping_footer bulk-footer d-none">
        <div class="container-fluid p-4 mt-3">
            <div class="row">
                <div class="col-md-2 col-lg-3 col-xl-4 col-12">
                    <div class="d-flex align-items-center">
                        <h2 class="mb-0">
                            <span id="totalCount">22</span> Selected products
                        </h2>
                    </div>
                    @auth
                        <div class="mt-2">
                            @auth
                                <a href="#" data-count="{{ auth()->user()->get_organization_total_products() }}"
                                    id="org_select_all_products" class="text-decoration-none">Select All</a>
                            @endauth
                        </div>
                    @endauth
                </div>
                @if (request()->segment(2) !== 'export' && request()->segment(3) !== 'step1')
                    <div class="col-md-10 col-lg-9 col-xl-8 col-12 d-flex justify-content-end">
                        <button class="btn btn-outline-danger ms-2 apply-bulk-delete" data-bs-toggle="modal"
                            data-bs-target="#delete-modal-bulk-delete">Delete All</button>
                        <button class="btn btn-outline-primary ms-2 disabled d-none">Sync All</button>
                        <button class="btn btn-outline-primary ms-2 apply-bulk-edit" data-action="edit">Bulk
                            Editing</button>
                        <button class="btn btn-outline-primary ms-2 apply-bulk" data-action="assign">Bulk
                            Assign</button>
                        <button class="btn btn-outline-primary ms-2 apply-bulk" data-action="price">Bulk Price
                            Update</button>
                    </div>
                @endif
            </div>
        </div>
    </footer>
    {{-- bulk footer end --}}

    @error('main')
        <div style="position: fixed; top: 10px; left: 44%;">
            <div class="toast toast_error toast-danger status-danger" role="alert" aria-live="assertive"
                aria-atomic="true" data-bs-delay="10000" style="min-height: 60px; width: 488px;position: relative">
                <div class="toast-body py-2 my-1">
                    <div class="row">
                        <div class="col-2">
                            <img src="{{ asset('media/new-flow/error.png') }}" alt="error">
                        </div>
                        <div class="col-9 p-0 bold" style="padding-top: 6px!important;word-break: break-word;">
                            {!! $message !!}
                        </div>
                        <div class="col-1 pl-0">
                            <button type="button" class="btn-sm border-0 fs-24 px-0 pt-0" data-bs-dismiss="toast"
                                aria-label="Close" style="position: absolute;top: 17px; right: 12px;">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @enderror

    {{-- Other error and success toast notifications continue here: --}}

    @error('limit_reached')
        <div style="position: fixed; top: 10px; left: 44%;">
            <div class="toast limit_reached toast-danger status-warning" role="alert" aria-live="assertive"
                aria-atomic="true" data-bs-delay="10000" style="min-height: 60px; width: 488px;position: relative">
                <div class="toast-body py-2 my-1">
                    <div class="row">
                        <div class="col-2">
                            <img src="{{ asset('media/new-flow/error.png') }}" alt="error">
                        </div>
                        <div class="col-9 p-0 bold" style="padding-top: 1px!important;word-break: break-word;">
                            {!! $message !!}
                        </div>
                        <div class="col-1 pl-0">
                            <button type="button" class="btn-sm border-0 fs-24 px-0 pt-0" data-bs-dismiss="toast"
                                aria-label="Close"
                                style="position: absolute;top: 17px; right: 12px;background-color: unset">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @enderror
    @if (session()->has('success'))
        <div style="position: fixed; top: 10px; left: 44%;">
            <div class="toast toast_success toast-success status-success" role="alert" aria-live="assertive"
                aria-atomic="false" data-bs-delay="10000" style="min-height: 60px; width: 488px;position: relative">
                <div class="toast-body py-2 my-1">
                    <div class="row">
                        <div class="col-2">
                            <img src="{{ asset('media/new-flow/success.png') }}" alt="success">
                        </div>
                        <div class="col-9 p-0 bold" style="padding-top: 10px!important;word-break: break-word;">
                            {!! session()->get('success') !!}
                        </div>
                        <div class="col-1 pl-0">
                            <button type="button" class="btn-sm border-0 fs-24 px-0 pt-0" data-bs-dismiss="toast"
                                aria-label="Close"
                                style="position: absolute;top: 17px; right: 12px;background-color: unset">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    {{-- script file --}}
    {{-- Core JS Files --}}
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <!-- Your script tags for additional JS files would go here -->

    <script src="{{ asset('bootstrap-5.0.2-dist/js/bootstrap.bundle.min.js') }}" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>

    <!--  Notifications Plugin   -->
    <script src="{{ asset('js/plugins/bootstrap-notify.js?v=' . env('APP_VERSION')) }}"></script>
    <!-- Control Center for Now Ui Dashboard: parallax effects, scripts for the example pages etc -->
    <script src="{{ asset('js/light-bootstrap-dashboard.js?v=' . env('APP_VERSION')) }}" type="text/javascript"></script>
    <!-- Light Dashboard DEMO methods, don't include it in your project! -->
    <script src="{{ asset('js/demo.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="{{ asset('js/core/jquery.sumoselect.min.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="{{ asset('js/countrySelect.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="{{ asset('js/auth/intlTelInput.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="{{ asset('js/auth/utils.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ asset('treeselect/comboTreePlugin.js?v=' . env('APP_VERSION')) }}" type="text/javascript"></script>


    <!--for datatable-->
    <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js" type="text/javascript"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap4.min.js" type="text/javascript"></script>


    {{-- https://sweetalert2.github.io/#examples --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>

    {{--used for fetching timezone--}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.43/moment-timezone-with-data.min.js"></script>

    @stack('footer_scripts')

    <!-- toast script -->
    <script>
        @error('main')
            $(document).ready(function() {
                $(".toast_error").toast('show');
            });
        @enderror

        @error('limit_reached')
            $(document).ready(function() {
                $(".limit_reached").toast('show');
            });
        @enderror

        @if (session()->has('success'))
            $(document).ready(function() {
                $(".toast_success").toast('show');
            });
            @php
                session()->remove('success');
            @endphp
        @endif
    </script>
    <script src="{{ asset('assets/js/layoutScript.js?v=' . env('APP_VERSION')) }}"></script>

    <script>
        $(document).ready(function() {
            window.addEventListener("resize", function() {
                const screen_width = document.documentElement.clientWidth;
                if (screen_width <= 991 && screen_width >= 767) {
                    $('.responsive-screen').removeClass('container-fluid');
                    $('.responsive-screen').addClass('container-fluid');
                } else {
                    $('.responsive-screen').addClass('container-fluid');
                    $('.responsive-screen').removeClass('container-fluid');
                }
            });
            $(document).on("click", ".dropdown-toggle", function() {
                $(this).next('.dropdown-menu').toggle("show");
            });
            $(".apply-bulk-delete").click(function(event) {
                event.preventDefault();
                // table ids
                var selectedCheckboxes = $('.bulk_product_check:checkbox:checked');
                var selectedIDs = selectedCheckboxes.map(function() {
                    return $(this).attr('id');
                }).get();

                if ($("#bulk-delete-input").val() != "all") {
                    $("#bulk-delete-input").val(selectedIDs);
                }
            });

            $(document).on("click", '#org_select_all_products', function() {
                $('#totalCount').html($("#filter_total_product_count").val());
                $('.filter_bulk_productIds').val('all');
                $("#bulk-delete-input").val("all");
                $('.bulk_product_check:checkbox').map(function() {
                    return $(this).prop('checked', true).addClass('checked');
                });
                $('#multiselect_products:checkbox').prop('checked', true);

                // $.ajax({
                //     url: "{{ route('products.bulk.select.all') }}",
                //     method: "get",
                //     success: function(data) {
                //         // $("#bulk-delete-input").val(data);
                //     },
                //     error: function(error) {
                //         console.log(error);
                //     }
                // });
            })
        });
    </script>
    <script>
        function readySelectizeElement() {
            $('.selectize_element').selectize({
                plugins: ['remove_button'],
                sortField: 'text'
            });
        }

        function readyTags(tagsEl) {
            if (tagsEl !== null) {
                var t1 = tagger(tagsEl, {
                    allow_duplicates: false,
                    allow_spaces: true,
                    add_on_blur: true,
                    tag_limit: 50,
                    // completion: {list: ['foo', 'bar', 'baz']}
                });
            } else {
                console.warn('Could not find .tags element on the page.');
            }
        }
    </script>

    <script>
        readyTags(document.querySelector('.tags'));
    </script>



    {{-- this code is for batching progress status --}}
    <script src="https://js.pusher.com/7.2/pusher.min.js"></script>
    @auth
        <script>
            // Enable pusher logging - don't include this in production
            Pusher.logToConsole = true;

            var pusher = new Pusher('{{ env('PUSHER_APP_KEY') }}', {
                cluster: '{{ env('PUSHER_APP_CLUSTER') }}'
            });

            var channel = pusher.subscribe(`channel-{{ auth()->user()->organization_id }}`);
            channel.bind('batch-progress', function(data) {
                batches_progress(data);
            });
        </script>
    @endauth

    {{-- select 2 code --}}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/js/standalone/selectize.min.js"
        integrity="sha256-+C0A5Ilqmu4QcSPxrlGpaZxJ04VjsRjKu+G82kl5UJk=" crossorigin="anonymous"></script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/css/selectize.bootstrap3.min.css"
        integrity="sha256-ze/OEYGcFbPRmvCnrSeKbRTtjG4vGLHXgOqsyLFTRjg=" crossorigin="anonymous" />


    {{--    bulk script  --}}
    <script src="{{ asset('assets/js/bulk_editing_script.js?v=' . env('APP_VERSION')) }}"></script>
    <script>
        $(document).ready(function() {

            $(document).on('click', function(event) {
                // Check if the clicked element is not inside a dropdown menu
                if (!$(event.target).closest('.dropdown-menu').length && !$(event.target).is(
                        '.dropdown-toggle')) {
                    // Close any open dropdown menus
                    $('.dropdown-menu').css('display', 'none');
                }
            });



            $(".apply-bulk-edit").click(function() {
                add_selected_products();
                const versionCount = $('#bulk_edit_version_id option').length;

                if (versionCount === 1) {
                    submit_bulk_edit_form_directly();
                } else {
                    $('#bulk_edit_modal').modal('show');
                }
            });

            function submit_bulk_edit_form_directly() {
                const form = $('#bulk_edit_form');
                const versionId = $('#bulk_edit_version_id option:first').val();

                $('<input>').attr({
                    type: 'hidden',
                    name: 'bulk_edit_version_id',
                    value: versionId
                }).appendTo(form);
                form.submit();
            }
            $(".apply-bulk").click(function(event) {
                self = $(this);
                event.preventDefault();
                var prev_text = self.html();
                $.ajax({
                    url: "{{ route('bulk.assign.modal.generate') }}",
                    method: "POST",
                    data: {
                        bulk_assign_action: self.data('action'),
                    },
                    async: false,
                    beforeSend: function() {
                        self.html(
                            '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>'
                        );
                    },
                    success: function(data) {
                        $('#bulk-assign-modal-div').empty();
                        $('#bulk-assign-modal-div').append(data.output);
                        $('#bulkAssign').modal('show');
                    },
                    error: function(error) {
                        console.log(error);
                    },
                    complete: function() {
                        self.html(prev_text);
                        readySelectizeElement();
                        readyTags(document.querySelector('.dynamic_tags'));

                        // add selected products ids
                        add_selected_products();
                    }
                });
            });


            $(".apply-bulk-price").click(function(event) {
                self = $(this);
                event.preventDefault();
                var prev_text = self.html();
                $.ajax({
                    url: "{{ route('bulk.price.update.modal.generate') }}",
                    method: "POST",
                    data: {
                        bulk_assign_action: self.data('action'),
                        task_id: self.data('id') ?? null,
                    },
                    async: false,
                    beforeSend: function() {
                        self.html(
                            '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>'
                        );
                    },
                    success: function(data) {
                        $('#bulk-price-update-modal-div').empty();
                        $('#bulk-price-update-modal-div').append(data.output);
                        $('#bulkPriceUpdate').modal('show');
                    },
                    error: function(error) {
                        console.log(error);
                    },
                    complete: function() {
                        self.html(prev_text);

                        // add selected products ids
                        if (typeof self.data('id') === 'undefined') {
                            add_selected_products();
                        }


                    }
                });
            });



            function add_selected_products() {
                // table ids
                const selectedCheckboxes = $('.bulk_product_check:checkbox:checked');
                const selectedIDs = selectedCheckboxes.map(function() {
                    return $(this).attr('id');
                }).get();
                const totalCount = parseInt($('#totalCount').text(), 10);
                if (totalCount === selectedCheckboxes.length) {
                    $('.bulk_productIds').val(selectedIDs);
                    $('.bulk_products_filter_array').val($('#filter_total_product_array').val());
                    $('.filter_bulk_productIds').val(selectedIDs);
                } else {
                    $('.bulk_productIds').val('all');
                    $('.filter_bulk_productIds').val('all');
                    $('.bulk_products_filter_array').val($('#filter_total_product_array').val());
                }
            }
        });
    </script>
    <script>
        function readySelectizeElement() {
            $('.selectize_element').selectize({
                plugins: ['remove_button'],
                sortField: 'text'
            });
        }

        function readyTags(tagsEl) {
            if (tagsEl !== null) {
                var t1 = tagger(tagsEl, {
                    allow_duplicates: false,
                    allow_spaces: true,
                    add_on_blur: true,
                    tag_limit: 50,
                    // completion: {list: ['foo', 'bar', 'baz']}
                });
            } else {
                console.warn('Could not find .tags element on the page.');
            }
        }
    </script>

    <script>
        readyTags(document.querySelector('.tags'));
    </script>



    {{-- this code is for batching progress status --}}
    <script src="https://js.pusher.com/7.2/pusher.min.js"></script>
    @auth
        <script>
            // Enable pusher logging - don't include this in production
            Pusher.logToConsole = true;

            var pusher = new Pusher('{{ env('PUSHER_APP_KEY') }}', {
                cluster: '{{ env('PUSHER_APP_CLUSTER') }}'
            });

            var channel = pusher.subscribe(`channel-{{ auth()->user()->organization_id }}`);
            channel.bind('batch-progress', function(data) {
                batches_progress(data);
            });
        </script>


        <script>
            function batches_progress(data) {
                var batch_progresses = $('[data-batchId="' + data.batch.id + '"]');
                if (typeof(batch_progresses) != "undefined" && batch_progresses !== null) {
                    batch_progresses.each(function(index, element) {
                        var batch_status_js = $(element).find('.batch-status-js');
                        var batch_progress_js = $(element).find('.batch-progress-js');
                        var main_progress_js = $(element).find('.main-progress-js');
                        var batch_progress_number_js = $(element).find('.batch-progress-number-js');


                        var progress = data.batch.progress + "%";
                        if (data.batch.progress < 100) {
                            batch_status_js.addClass("status-publish");
                            batch_status_js.removeClass("status-success");
                            batch_status_js.html("Processing");
                            batch_progress_js.css('width', progress);
                            batch_progress_number_js.html(progress);
                        } else {
                            batch_progress_js.css('width', progress);
                            batch_progress_number_js.html(progress);

                            setTimeout(function() {
                                batch_status_js.removeClass("status-publish");
                                batch_status_js.addClass("status-success");
                                batch_status_js.html("Processed");
                                main_progress_js.remove();
                                if (batch_progresses.hasClass('col-main-progress-js')) {
                                    batch_progresses.remove();
                                }
                                $(element).find('.reload-page-js').removeClass('d-none');
                            }, 2000);
                        }
                    });
                }

            }
        </script>


        {{-- this is dynamic script for upgrade billing modal --}}
        <script>
            $(document).ready(function() {
                // When the modal is triggered (e.g., the link is clicked)
                $('[data-bs-target="#upgradeBilling"]').on('click', function() {
                    $('#upgradeBilling .upgradeBillingTitle').text($(this).data('billingtitle'));
                    $('#upgradeBilling .upgradeBillingInfo').text($(this).data('billinginfo'));
                });
            });
        </script>

    @endauth

</body>

</html>
