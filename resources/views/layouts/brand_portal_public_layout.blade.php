<!DOCTYPE html>
<html lang="en">

<head>
    @viteReactRefresh

    @if (env('APP_ENV') != 'lambda')
        @vite(['resources/js/app.jsx'])

        @if (isset($page))
            @inertiaHead
        @endif
    @endif
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if (\Illuminate\Support\Facades\App::environment(['staging', 'development', 'local']))
        <meta name="robots" content="noindex">
    @endif

    <title>Apimio - @yield('titles', 'Apimio')</title>
    @stack('meta_tags')

    <link rel="apple-touch-icon" sizes="76x76" href="{{ asset('img/apple-icon.png') }}">
    <link rel="icon" type="image/png" href="{{ asset('1favicon.ico') }}">
    <link rel="icon" href="/assets/favicon.ico" type="image/x-icon" />

    <!-- Style -->
    <link href="{{ asset('bootstrap-5.0.2-dist/css/bootstrap.min.css') }}" rel="stylesheet" crossorigin="anonymous">
    <link rel="stylesheet" href="{{ asset('css/plugin/intlTelInput.css?v=' . env('APP_VERSION')) }}" type="text/css" />
    <link rel="stylesheet" href="{{ asset('css/plugin/countrySelect.css?v=' . env('APP_VERSION')) }}" type="text/css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="{{ asset('css/plugin/sumoselect.css?v=' . env('APP_VERSION')) }}">
    <link href="{{ asset('css/tagger.css?v=' . env('APP_VERSION')) }}" rel="stylesheet">
    <script src="{{ asset('js/tagger.js?v=' . env('APP_VERSION')) }}"></script>
    <link rel="stylesheet" href="{{ asset('treeselect/style.css?v=' . env('APP_VERSION')) }}">
    <link rel="stylesheet" href="{{ asset('css/sync-popup.css?v=' . env('APP_VERSION')) }}">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css">
    <script src="https://kit.fontawesome.com/58a77f8c62.js" crossorigin="anonymous"></script>

    @if (\Illuminate\Support\Facades\App::environment('production'))
        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-TCSKR8R');
        </script>
    @endif
    @stack('header_scripts')
    <link type="text/css" href="{{ asset('./assets/scss/screen.css?v=' . env('APP_VERSION')) }}" rel="stylesheet">
</head>

<body>
    <div class="{{ auth()->check() ? 'container-fluid' : '' }}">
        @if (\Illuminate\Support\Facades\App::environment('staging'))
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNMH4W9" height="0"
                    width="0" style="display:none;visibility:hidden"></iframe></noscript>
        @endif
        @if (\Illuminate\Support\Facades\App::environment('production'))
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TCSKR8R" height="0"
                    width="0" style="display:none;visibility:hidden"></iframe></noscript>
        @endif

        @auth
            @php
                $hideSidebarForUrls = ['on-boarding', 'organization'];
                $currentSegment = Request::segment(1);
            @endphp
            @if (in_array($currentSegment, $hideSidebarForUrls))
                <div class="container-fluid h-100 responsive-screen">
                    @section('content')
                    @show
                </div>
            @else
                @if (isset($sidebar_display) ? $sidebar_display : true)
                    <div class="row">
                        @if ($currentSegment == 'products' || $currentSegment == 'channel')
                            @include('layouts.navs.product_sidebar')
                        @else
                            @include('layouts.navs.newsidebar')
                        @endif
                        <div class="col ">
                            <div class="">
                                @section('content')
                                @show
                            </div>
                            <div class="mt-80"></div>
                        </div>
                    </div>
                @else
                    <div class="container-fluid h-100 responsive-screen">
                        @section('content')
                        @show
                    </div>
                @endif
            @endif
        @else
            <div>
                @section('content')
                @show
            </div>
        @endauth
    </div>

    <!-- import alert on file upload Modal -->
    <x-alerts.import-alert />
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="{{ asset('bootstrap-5.0.2-dist/js/bootstrap.bundle.min.js') }}" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ asset('js/plugins/bootstrap-notify.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="{{ asset('js/light-bootstrap-dashboard.js?v=' . env('APP_VERSION')) }}" type="text/javascript"></script>
    <script src="{{ asset('js/demo.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="{{ asset('js/core/jquery.sumoselect.min.js?v=' . env('APP_VERSION')) }}"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js" type="text/javascript"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap4.min.js" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
    <script src="https://js.pusher.com/7.2/pusher.min.js"></script>
    @auth
        <script>
            Pusher.logToConsole = true;
            var pusher = new Pusher('{{ env('PUSHER_APP_KEY') }}', {
                cluster: '{{ env('PUSHER_APP_CLUSTER') }}'
            });
            var channel = pusher.subscribe(`channel-{{ auth()->user()->organization_id }}`);
            channel.bind('batch-progress', function(data) {
                batches_progress(data);
            });
        </script>
    @endauth
    @stack('footer_scripts')
    <script src="{{ asset('assets/js/layoutScript.js?v=' . env('APP_VERSION')) }}"></script>
    <script>
        $(document).ready(function() {
            $(".apply-bulk-delete").click(function(event) {
                event.preventDefault();
                var selectedCheckboxes = $('.bulk_product_check:checkbox:checked');
                var selectedIDs = selectedCheckboxes.map(function() {
                    return $(this).attr('id');
                }).get();
                $("#bulk-delete-input").val(selectedIDs);
            });

            $(document).on("click", '#org_select_all_products', function() {
                $('#totalCount').html($("#filter_total_product_count").val());
                $('.filter_bulk_productIds').val('all');
                $('.bulk_product_check:checkbox').map(function() {
                    return $(this).prop('checked', true).addClass('checked');
                });
                $('#multiselect_products:checkbox').prop('checked', true);

                $.ajax({
                    url: "{{ route('products.bulk.select.all') }}",
                    method: "get",
                    success: function(data) {
                        $("#bulk-delete-input").val(data);
                    },
                    error: function(error) {
                        console.log(error);
                    }
                });
            });
        });
    </script>
</body>

</html>
