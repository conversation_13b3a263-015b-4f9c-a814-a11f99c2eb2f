

{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}

<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-100">
<head>
@viteReactRefresh
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if(\Illuminate\Support\Facades\App::environment(["staging", "development", "local"]))
        <meta name="robots" content="noindex">
    @endif

    <title>Apimio - @yield('titles','Apimio')</title>
    @stack('meta_tags')

    <script src="https://kit.fontawesome.com/b2d3583b32.js" crossorigin="anonymous"></script>

    <link rel="apple-touch-icon" sizes="76x76" href="{{ asset('img/apple-icon.png') }}">
    <link rel="icon" type="image/png" href="{{ asset('1favicon.ico') }}">

    <!-- Bootstrap-->
    <link href="{{ asset('bootstrap-5.0.2-dist/css/bootstrap.min.css') }}" rel="stylesheet" crossorigin="anonymous">
    <link type="text/css" href="{{ asset('css/light-bootstrap-dashboard.css?v='.env('APP_VERSION')) }}" rel="stylesheet">
    <!--Fonts Poppins and Roboto-->
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <!--For Phone Flags-->
    <link rel="stylesheet" href="{{ asset('css/plugin/intlTelInput.css') }}" type="text/css"/>
    <link rel="stylesheet" href="{{ asset('css/plugin/countrySelect.css') }}" type="text/css"/>
    <!-- Fonts and Icon-->
    {{--    <link rel="stylesheet" href="{{ asset('libs/font-awesome/fontawesome-all.css') }}"/>--}}
    <script src="https://kit.fontawesome.com/58a77f8c62.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
    <!--sumoselect-->
    <link rel="stylesheet" href="{{ asset('css/plugin/sumoselect.css') }}">

    <!--Icons-->
    <link rel="stylesheet" type="text/css" href="{{asset('fonts/flaticon.css?v='.env('APP_VERSION'))}}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">

    <link rel="stylesheet" href="{{asset('treeselect/style.css')}}">
    <link rel="stylesheet" href="{{ asset('css/sync-popup.css') }}">

    <!--for datatable-->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css">



    @if(\Illuminate\Support\Facades\App::environment(["staging", "production"]))
        @if(\Illuminate\Support\Facades\Auth::check())
            <script>
                /*
                * Tags manager authenticated user data
                * */
                let arguments = {
                    "id": '{{ \Illuminate\Support\Facades\Auth::id() }}',
                    "fname": '{{ \Illuminate\Support\Facades\Auth::user()->fname }}',
                    "lname": '{{ \Illuminate\Support\Facades\Auth::user()->lname }}',
                    "email": '{{ \Illuminate\Support\Facades\Auth::user()->email }}',
                    "created_at": '{{ \Illuminate\Support\Facades\Auth::user()->created_at }}',
                };
            </script>
        @endif
    @endif
    @if(\Illuminate\Support\Facades\App::environment("staging"))
        <!-- Google Tag Manager -->
        <script>(function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'});
                var f = d.getElementsByTagName(s)[0], j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-NNMH4W9');</script><!-- End Google Tag Manager -->
    @endif
    @if(\Illuminate\Support\Facades\App::environment("production"))
        <!-- Google Tag Manager -->
        <script>(function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'});
                var f = d.getElementsByTagName(s)[0], j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-TCSKR8R');</script><!-- End Google Tag Manager -->
    @endif
    @stack('header_scripts')

</head>
<body class="h-100">
@if(\Illuminate\Support\Facades\App::environment("staging"))
    <!-- Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNMH4W9" height="0" width="0"
                style="display:none;visibility:hidden"></iframe>
    </noscript><!-- End Google Tag Manager (noscript) -->
@endif
@if(\Illuminate\Support\Facades\App::environment("production"))
    <!-- Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TCSKR8R" height="0" width="0"
                style="display:none;visibility:hidden"></iframe>
    </noscript><!-- End Google Tag Manager (noscript) -->
@endif

<div class="wrapper @guest wrapper-full-page @endguest">
    @auth
        @if(isset($sidebar_display) ? $sidebar_display : true)
            <div class="row m-0">
                <div class="col-12 col-sm-12 col-md-12 col-lg-3 col-xl-2 p-0">
                    @include('layouts.navs.old-sidebar')
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-9 col-xl-10 p-0">
                    <div class="container-lg container-xl py-3">
                        @section('content')
                        @show
                    </div>
                </div>
            </div>
        @else
            <div class="container-fluid h-100">
                @section('content')
                @show
            </div>
        @endif
    @else
        <div class="container-fluid h-100">
            @section('content')
            @show
        </div>
    @endauth
</div>

<!-- Export CSV Modal -->
<x-products.export-c-s-v/>

<!-- Export CSV Modal -->
<x-alerts.import-alert/>

<!-- toast box -->
@error('main')
<div style="position: fixed; top: 10px; left: 44%;">
    <div class="toast toast_error toast-danger" role="alert" aria-live="assertive" aria-atomic="true" data-delay="10000"
         style="min-height: 80px; width: 488px;position: relative">
        <div class="toast-body py-3">
            <div class="row">
                <div class="col-2">
                    <img src="{{asset('media/new-flow/error.png')}}" alt="error">
                </div>
                <div class="col-9 p-0 bold" style="padding-top: 6px!important;word-break: break-word;">
                    {!! $message !!}
                </div>
                <div class="col-1 pl-0">
                    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close"
                            style="position: absolute;top: -13px; right: 12px;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>


        </div>
    </div>
</div>
@enderror
<!-- limit error toast box -->
@error('limit_reached')
<div style="position: fixed; top: 10px; left: 44%;">
    <div class="toast limit_reached toast-danger" role="alert" aria-live="assertive" aria-atomic="true" data-delay="10000"
         style="min-height: 80px; width: 488px;position: relative">
        <div class="toast-body py-3">
            <div class="row">
                <div class="col-2">
                    <img src="{{asset('media/new-flow/error.png')}}" alt="error">
                </div>
                <div class="col-9 p-0 bold" style="padding-top: 1px!important;word-break: break-word;">
                    {!! $message !!}
                </div>
                <div class="col-1 pl-0">
                    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close"
                            style="position: absolute;top: -13px; right: 12px;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@enderror
@if(session()->has("success"))
    <div style="position: fixed; top: 10px; left: 44%;">
        <div class="toast toast_success toast-success" role="alert" aria-live="assertive" aria-atomic="true" data-delay="10000"
             style="min-height: 80px; width: 488px;position: relative">
            <div class="toast-body py-3">
                <div class="row">
                    <div class="col-2">
                        <img src="{{asset('media/new-flow/success.png')}}" alt="error">
                    </div>
                    <div class="col-9 p-0 bold" style="padding-top: 1px!important;word-break: break-word;">
                        {!! session()->get("success") !!}
                    </div>
                    <div class="col-1 pl-0">
                        <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close"
                                style="position: absolute;top: -13px; right: 12px;">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>


            </div>
        </div>
    </div>
@endif

<!--   Core JS Files   -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="{{ asset('bootstrap-5.0.2-dist/js/bootstrap.bundle.js') }}" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>


<!--  Notifications Plugin   -->
<script src="{{ asset('js/plugins/bootstrap-notify.js') }}"></script>

{{--sweet alert--}}
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Control Center for Now Ui Dashboard: parallax effects, scripts for the example pages etc -->
<script src="{{ asset('js/light-bootstrap-dashboard.js?v=2.0.1') }}" type="text/javascript"></script>
<!-- Light Dashboard DEMO methods, don't include it in your project! -->
<script src="{{ asset('js/demo.js?') }}"></script>
<script src="{{ asset('js/core/jquery.sumoselect.min.js') }}"></script>
<script src="{{asset('js/countrySelect.js')}}"></script>
<script src="{{ asset('js/auth/intlTelInput.js') }}"></script>
<script src="{{ asset('js/auth/utils.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{asset('treeselect/comboTreePlugin.js')}}" type="text/javascript"></script>


<!--for datatable-->
<script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap4.min.js" type="text/javascript"></script>

@stack('footer_scripts')

<script>

    $(function () {
        $("body").tooltip({selector: '[data-toggle=tooltip]'});
    })

    /**
     * Ask for confirmation if any thing is changed by user in current page.
     * */
    $("[data-confirm-before-leave]").change(function () {
        $(window).on('beforeunload', function () {
            return confirm("You save some unsaved data, Do you want to leave?");
        });

        $(this).parents("form").submit(function () {
            $(window).unbind("beforeunload");
        });
    });


</script>

<script>
    $(document).ready(function () {
        $('.sumoselect').SumoSelect({
            csvDispCount: 1,
            captionFormat: '({0}) Selected',
            captionFormatAllSelected: '({0}) Selected',
            okCancelInMulti: true,
            isClickAwayOk: true,
            selectAll: true,
            search: false,
            searchText: 'Search here',
            noMatch: 'No matches for {0}',

        });
    });
</script>
<!-- toast script -->
<script>
    @error("main")
    $(document).ready(function () {
        $(".toast_error").toast('show');
    });
    @enderror

    @error("limit_reached")
    $(document).ready(function () {
        $(".limit_reached").toast('show');
    });
    @enderror

    @if(session()->has("success"))
    $(document).ready(function () {
        $(".toast_success").toast('show');
    });
    @php
        session()->remove("success")
    @endphp
    @endif

    //Prevent form submitting multiple times
    $("button[type='submit']").click(function () {
        if (!($(this).hasClass("search"))) {
            var previous_text = $(this).html();
            $(this).attr("disabled", true);
            $(this).closest("form").submit();
            $(this).html("Please wait..");
            let _this = this;
            setTimeout(function () {
                    $(_this).prop("disabled", false);
                    $(_this).html(previous_text);
                },
                5000);
        }

    })

    //Prevent form submitting multiple times
    $(".type_button").click(function () {
        if (!($(this).hasClass("search"))) {
            var previous_text = $(this).html();
            $(this).attr("disabled", true);
            $(this).html("Please wait..");

        }

    })

    $(".disabled-with-text").click(function () {
        if (!($(this).hasClass("search"))) {
            var previous_text = $(this).html();
            $(this).attr("disabled", true);
            $(this).css('pointer-events', 'none');
            $(this).html("Please wait..");
            let _this = this;
            setTimeout(function () {
                    $(_this).prop("disabled", false);
                    $(_this).html(previous_text);
                },
                5000);
        }

    })
    $(".only-disabled").click(function () {
        if (!($(this).hasClass("search"))) {
            var previous_text = $(this).html();
            $(this).attr("disabled", true);
            $(this).css('opacity', '0.5');
            $(this).css('pointer-events', 'none');
            let _this = this;
            setTimeout(function () {
                    $(_this).prop("disabled", false);
                    $(_this).html(previous_text);
                },
                5000);
        }

    })
</script>

<!--Button effects-->
<script>
    let links = document.querySelectorAll('.ripplelink');

    for (let i = 0, len = links.length; i < len; i++) {
        links[i].addEventListener('click', function (e) {
            let targetEl = e.target;
            let inkEl = targetEl.querySelector('.ink');

            if (inkEl) {
                inkEl.classList.remove('animate');
            } else {
                inkEl = document.createElement('span');
                inkEl.classList.add('ink');
                inkEl.style.width = inkEl.style.height = Math.max(targetEl.offsetWidth, targetEl.offsetHeight) + 'px';
                targetEl.appendChild(inkEl);
            }

            inkEl.style.left = (e.offsetX - inkEl.offsetWidth / 2) + 'px';
            inkEl.style.top = (e.offsetY - inkEl.offsetHeight / 2) + 'px';
            inkEl.classList.add('animate');
        }, false);
    }
</script>
{{-- this script is used to detect duplicate attribute options --}}
<script>

    function checkDuplicate(arr) {
        // empty object
        let map = {};
        let result = false;
        for (let i = 0; i < arr.length; i++) {
            if (map[arr[i]]) {
                result = true;
                break;
            }
            map[arr[i]] = true;
        }
        if (result) {
            return true;
        } else {
            return false;
        }
    }

    //    validation for duplicates
    $(document).on('keyup', '.duplicate-validation', function () {
        var values = [];

        var form_button = $(this).parents('form:first').find("button[type='submit']");
        $('.duplicate-validation').each(
            function () {
                if (values.indexOf(this.value) >= 0) {
                    $(this).css("border-color", "red");
                    form_button.prop('disabled', true);
                    form_button.css('pointer-events', 'none');
                } else {
                    $(this).css("border-color", "");
                    form_button.prop('disabled', false);
                    form_button.css('pointer-events', '');//clears since last check
                }
                values.push(this.value);

                if (checkDuplicate(values)) {
                    form_button.prop('disabled', true);
                }
            }
        );

    });
</script>
{{-- select 2 code --}}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/js/standalone/selectize.min.js"
        integrity="sha256-+C0A5Ilqmu4QcSPxrlGpaZxJ04VjsRjKu+G82kl5UJk=" crossorigin="anonymous"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/css/selectize.bootstrap3.min.css"
      integrity="sha256-ze/OEYGcFbPRmvCnrSeKbRTtjG4vGLHXgOqsyLFTRjg=" crossorigin="anonymous"/>

{{--https://sweetalert2.github.io/#examples--}}
{{--<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">--}}
{{--<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>--}}

</body>
</html>
