<?php ?>
@php
    use App\Classes\Unit;
@endphp
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Attribute')
@else
    @section('titles','Add Attributes')
@endif
@section('content')
    <link href="https://unpkg.com/gijgo@1.9.13/css/gijgo.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/css/bootstrap-datepicker.min.css" rel="stylesheet" />
    <style>
        .active2 {
            border: 2px solid #2c4bff !important;
            opacity: 1;
        }

        .gj-textbox-md {
            background: #F8F8F8;
            border: 1px solid #E5E5E5;
            display: block;
            font-family: Roboto, sans-serif;
            font-size: .875rem;
            line-height: 0;
            padding: 4px 12px;
            width: 100%;
            text-align: left;
            color: #000000;
        }

        .gj-datepicker-md [role=right-icon] {
            top: 7px;
        }

        .fas {
            padding-right: 10px;
        }

    </style>
    <div>
        <x-products.add-page-title name="{{trans('products_attributes.page_title')}}" :routes="route('attributes.index')" />
        <div class="row mt-4">
            <div class="col-12{{-- col-md-9 col-lg-9 col-xl-6--}}">
                <form id="pro_attr_create_form" class="formStyle" action="{{isset($attribute) ? route('attributes.update',$attribute->id) : route('attributes.store')}}" method="POST">
                    @csrf
                    @if(isset($attribute))
                        @method('PUT')
                        <input type="hidden" name="id" value="{{$attribute->id}}">
                        <input type="hidden" id="attribute_type_id" name="attribute_type_id" value="{{$attribute->attribute_type_id}}">
                    @endif

                    <div class="row mb-3">
                        <div class="col-3 pr-0">
                            <div class="list-group" id="list-tab" role="tablist">
                                <label for="" onclick="attributeType('input_type_1')" @if(isset($attribute) && $attribute->attribute_type_id != 1) aria-disabled="true" @endif
                                class="list-group-item list-group-item-action border-left-show bold p-3 h-25 {{ isset($attribute) ? '' : 'active' }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 1 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 1 ? "checked" : null }}"
                                       id="input_type_1" data-toggle="list" href="#attribute_type_1" role="tab"
                                       aria-controls="single-line-text">
                                    <input type="radio" name="attribute_type_id" value="1" class="select_attribute_option" style="display: none; width: 0; height: 0" {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 1 ? "checked" : (!old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) ? "checked" : null) }}>
                                    <i class="fa fa-pencil-alt mr-1 bold"></i>
                                    {{trans('products_attributes_create.single_line_text')}}
                                </label>
                                <label for="" onclick="attributeType('input_type_2')" @if(isset($attribute) && $attribute->attribute_type_id != 2) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 2 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 2 ? "checked" : null }}"
                                       id="input_type_2" data-toggle="list" href="#attribute_type_2" role="tab"
                                       aria-controls="number">
                                    <input type="radio" name="attribute_type_id" value="2" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 2 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 2 ? "checked" : null) : null) }}>
                                    <i class="fa fa-hashtag mr-1 bold"></i>
                                    {{trans('products_attributes_create.number')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_3')" @if(isset($attribute) && $attribute->attribute_type_id != 3) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 3 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 3 ? "checked" : null }}"
                                       id="input_type_3"
                                       data-toggle="list" href="#attribute_type_3" role="tab"
                                       aria-controls="multi-line-text">
                                    <input type="radio" name="attribute_type_id" value="3" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 3 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 3 ? "checked" : null) : null) }}>
                                    <i class="fa fa-pencil-alt mr-1 bold"></i> {{trans('products_attributes_create.multi_line_text')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_4')" @if(isset($attribute) && $attribute->attribute_type_id != 4) aria-disabled="true" @endif class="input_type_4 list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 4 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 4 ? "checked" : null }}"
                                       id="input_type_4"
                                       data-toggle="list" href="#select" role="tab"
                                       aria-controls="select">
                                    <input type="radio" name="attribute_type_id" value="4" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 4 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 4 ? "checked" : null) : null) }}>
                                    <i class="fa fa-list-ol mr-1 bold"></i>
                                    {{trans('products_attributes_create.select')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_5')" @if(isset($attribute) && $attribute->attribute_type_id != 5) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 5 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 5 ? "checked" : null }}"
                                       id="input_type_5"
                                       data-toggle="list" href="#attribute_type_5" role="tab"
                                       aria-controls="date-time">
                                    <input type="radio" name="attribute_type_id" value="5" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 5 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 5 ? "checked" : null) : null) }}>
                                    <i class="fa fa-calendar-alt mr-1 bold"></i> {{trans('products_attributes_create.date_and_time')}}
                                </label>

                                {{-- <label for="" onclick="attributeType('input_type_6')" class="list-group-item list-group-item-action border-left-show bold p-3 h-25--}}
                                {{-- {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 6 ? "active" : null }}--}}
                                {{-- {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 6 ? "checked" : null }}"--}}
                                {{-- id="input_type_6"--}}
                                {{-- data-toggle="list" href="#attribute_type_6" role="tab"--}}
                                {{-- aria-controls="file">--}}
                                {{-- <input type="radio" name="attribute_type_id" value="6" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 6 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 6 ? "checked" : null) : null) }}>--}}
                                {{-- <i class="fa fa-file mr-1 bold"></i> {{trans('products_attributes_create.file')}}--}}
                                {{-- </label>--}}

                                <label for="" onclick="attributeType('input_type_7')" @if(isset($attribute) && $attribute->attribute_type_id != 7) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 7 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 7 ? "checked" : null }}"
                                       id="input_type_7"
                                       data-toggle="list" href="#attribute_type_7" role="tab"
                                       aria-controls="measurement">
                                    <input type="radio" name="attribute_type_id" value="7" id="measurement_radio" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 7 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 7 ? "checked" : null) : null) }}>
                                    <i class="fa fa-weight-hanging mr-1 bold"></i> {{trans('products_attributes_create.measurement')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_8')" @if(isset($attribute) && $attribute->attribute_type_id != 8) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 8 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 8 ? "checked" : null }}"
                                       id="input_type_8"
                                       data-toggle="list" href="#attribute_type_8" role="tab"
                                       aria-controls="rating">
                                    <input type="radio" name="attribute_type_id" value="8" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 8 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 8 ? "checked" : null) : null) }}>
                                    <i class="fa fa-star mr-1 bold"></i> {{trans('products_attributes_create.rating')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_9')" @if(isset($attribute) && $attribute->attribute_type_id != 9) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 9 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 9 ? "checked" : null }}"
                                       id="input_type_9"
                                       data-toggle="list" href="#attribute_type_9" role="tab"
                                       aria-controls="json">
                                    <input type="radio" name="attribute_type_id" value="9" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 9 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 9 ? "checked" : null) : null) }}>
                                    <i class="fa fa-code mr-1 bold"></i>
                                    {{trans('products_attributes_create.json')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_10')" @if(isset($attribute) && $attribute->attribute_type_id != 10) aria-disabled="true" @endif class="list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 10 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 10 ? "checked" : null }}"
                                       id="input_type_10"
                                       data-toggle="list" href="#attribute_type_10" role="tab"
                                       aria-controls="true-false">
                                    <input type="radio" name="attribute_type_id" value="10" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 10 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 10 ? "checked" : null) : null) }}>
                                    <i class="fa fa-check mr-1 bold"></i> {{trans('products_attributes_create.true_or_false')}}
                                </label>
                                <label for="" onclick="attributeType('input_type_11')" @if(isset($attribute) && $attribute->attribute_type_id != 11) aria-disabled="true" @endif class=" list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 11 ? "active" : null }}
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 11 ? "checked" : null }}"
                                       id="input_type_11"
                                       data-toggle="list" href="#attribute_type_11" role="tab"
                                       aria-controls="url">
                                    <input type="radio" name="attribute_type_id" value="11" class="select_attribute_option" style="display: none" {{ old("attribute_type_id") ? (old("attribute_type_id") == 11 ? "checked" : null) : (isset($attribute->attribute_type_id) ? ($attribute->attribute_type_id == 11 ? "checked" : null) : null) }}>
                                    <i class="fa fa-link mr-1 bold"></i> {{trans('products_attributes_create.url')}}
                                </label>

                                <label for="" onclick="attributeType('input_type_12')" @if(isset($attribute) && $attribute->attribute_type_id != 12) aria-disabled="true" @endif class=" list-group-item list-group-item-action border-left-show bold p-3 h-25
                                {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 12 ? "active" : null }}"
                                       id="input_type_12"
                                       data-toggle="list" href="#attribute_type_12" role="tab"
                                       aria-controls="attribute_type_12">
                                    <input type="radio" name="attribute_type_id" value="12" class="select_attribute_option" style="display: none" {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 12 ? "checked" : null }}>
                                    <i class="fa fa-paint-brush mr-1 bold"></i>{{trans('products_attributes_create.color')}}
                                </label>
                            </div>
                        </div>
                        <div class="col-9 pl-0">
                            <div class="tab-content" id="nav-tabContent">
                                {{--Attribute family--}}
                                <div class="form-group pt-0 px-3 mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <div>
                                            <label for="name">{{trans('products_attributes_create.attribute_family')}}
                                                &nbsp;<span class="text-danger">*</span></label>
                                        </div>
                                        <div>
                                            <a href="{{ route('family.index') }}" id="add_family" class="text-decoration-none">Add Attribute Set</a>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-12">
                                            <select name="attribute_family[]" id="" @if(isset($attribute)) aria-disabled="true" @endif class="form-control sumoselect bg-white-smoke @error('attribute_family') is-invalid @enderror @if(isset($attribute)) disabled @endif">
                                                @if($families->count() == 0)
                                                    <option value="" disabled>No Attribute Set Found</option>
                                                @else
                                                    @foreach($families as $family)
                                                        <option value="{{$family->id}}" {{(in_array($family->id, old('attribute_family', isset($family_attributes) ? $family_attributes : [] )) ? "selected": '')}}>{{$family->name}} @error('duplicate_attribute_ids') {{in_array($family->id, explode(',',$message)) ? '(Already added)' : ''}} @enderror </option>
                                                        @error('duplicate_attribute_ids') {{in_array($family->id, explode(',',$message)) ? 'This family is already connected' : ''}} @enderror
                                                    @endforeach
                                                @endif
                                            </select>
                                            @error('attribute_family')
                                            <span class="text-danger">
                                            <small>{{$message}}</small>
                                        </span>
                                            @enderror
                                        </div>
                                    </div>
                                    @error('duplicate_attribute_ids')
                                    <span class="text-danger">
                                    <small>Assign unique attribute</small>
                                </span>
                                    @enderror
                                </div>

                                {{--Attribute Name--}}
                                <div class="form-group pt-0 px-3 mb-3">
                                    <label for="name">{{trans('products_attributes_create.attribute_title')}}&nbsp;
                                        <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control  @error('name') is-invalid @enderror" id="name" name="name" minlength="3" value="{{ old('name', isset($attribute) ? $attribute->name : null) }}" required autofocus>
                                    @error('name')
                                    <span class="text-danger error-message-text">
                                    <small class="error-message-text">{{$message}}</small>
                                </span>
                                    @enderror
                                </div>

                                {{--Attribute Description--}}
                                <div class="form-group pt-0 px-3 mb-3">
                                    <label for="name">{{trans('products_attributes_create.description')}}</label>
                                    <input type="text" class="form-control @error('description') is-invalid @enderror" id="description" name="description" value="{{ old('description', isset($attribute) ? $attribute->description : null) }}" autofocus>
                                    @error('description')
                                    <span class="text-danger">
                                    <small class="error-message-text">{{$message}}</small>
                                </span>
                                    @enderror
                                </div>
                                <div class="row px-3 mb-3 {{ in_array(old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null), [3,9,10]) ? "d-none" : '' }}" id="radio-buttons">

                                    <div class="col-lg-4">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" name="value_type" value="single" class="align-middle custom-control-input" id="single" {{isset($attribute) ? ($attribute->attribute_value_type() == 'single' ? "checked='true'" :'') : 'checked="true"'}}>
                                            <label for="single" class="pl-2 font-weight-bold custom-control-label">Single
                                                Value</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" name="value_type" value="list" class="align-middle custom-control-input" id="list" {{isset($attribute) ? ($attribute->attribute_value_type() == 'list' ? "checked='true'" :'') : ''}}>
                                            <label for="list" class="pl-2 font-weight-bold custom-control-label">Multiple
                                                Values</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-4"></div>
                                </div>


                                <div class="px-3 py-0 d-none" id="border-bottom-multivalue">
                                    <div class="border-bottom"></div>
                                </div>

                                <br>

                                {{--Single line Text--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 1 ? "show active" : (empty(old("attribute_type_id")) && !isset($attribute) ? "show active" : null)}}" id="attribute_type_1" role="tabpanel" aria-labelledby="text-list">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3 id="single_line_text_header" class="mb-0">Validation</h3>
                                            <p id="single_line_text_message">Values can contain letters, numbers, and
                                                special characters.</p>
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="col-md-3"></div>
                                    </div>
                                </div>
                                {{--Number--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 2 ? "show active" :''}}" id="attribute_type_2" role="tabpanel" aria-labelledby="number-list">
                                    @if(isset($attribute))
                                        <div class="row">
                                            <div class="col-md-12">
                                                <h3>Validation</h3>
                                                <p id="validation_text">Values must be numbers without a decimal.</p>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h3>Select Type</h3>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="integer" name="type" class="align-middle custom-control-input" value="integer" {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'integer' ? 'checked' : '') : '')  : 'checked'}}>
                                                        <label class="pl-2 font-weight-bold custom-control-label" for="integer">Integer</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="decimal" name="type" class="align-middle  custom-control-input" value="decimal" {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'decimal' ? 'checked' : '') : '') : 'checked'}}>
                                                        <label class="pl-2 font-weight-bold custom-control-label" for="decimal">Decimal</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="price" name="type" class="align-middle  custom-control-input" value="price" {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'price' ? 'checked' : '') : '') : 'checked'}}>
                                                        <label class="pl-2 font-weight-bold custom-control-label" for="price">Price</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4"></div>
                                        <div class="col-md-4"></div>
                                    </div>
                                    <br>
                                    <div class="row" id="precision_field" style="display: {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'decimal' ? 'block' : 'none') : '') : '' }}">
                                        <div class="col-12" class="maximum-precision-js">
                                            <div class="form-group mb-3">
                                                <label for="max_number_precision">{{trans('products_attributes_create.max_number_precision')}}</label>
                                                <input type="number" class="form-control @error('max_number_precision') is-invalid @enderror" id="max_number_precision" min="0" name="max_number_precision" {{--                                                       {{isset($response['type']) == 'integer' ? 'disabled' : ''}}--}} @isset($attribute) @if($attribute->attribute_type_id == 2)
                                                    value="{{old('max_number_precision') ? old('max_number_precision') : (isset($response['precision']) ? $response['precision'] : '')}}"
                                                    @endif
                                                    @endisset>
                                                @error('max_number_precision')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{--Multi line Text--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 3?"show active" :''}}" id="attribute_type_3" role="tabpanel" aria-labelledby="text-list">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3>Validation</h3>
                                            <p>Values can contain letters, numbers, and special characters.</p>
                                        </div>
                                    </div>
                                </div>

                                {{--Multi Select--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 4?"show active" :''}}" id="select" role="tabpanel" aria-labelledby="input_type_4">
                                    <div class="form-group mt-3" id="multiselect-card">
                                        <label for="">{{trans('variant_attribute_update.manage_option')}}</label>
                                        <div class="dynamic-field formStyle">
                                            @if(old("attribute_options"))
                                                @foreach(old("attribute_options") as $key => $attribute_option)

                                                    <div class="d-flex justify-content-between dynamic_fields_js mt-3">
                                                        <input type="text" value="{{$attribute_option["name"]}}" name="attribute_options[][name]" class="form-control form-control-sm duplicate-validation variant-option @error('attribute_options.'.$key.'.name') is-invalid @enderror">
                                                        <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                                                            <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                                        </button>
                                                    </div>
                                                    @error('attribute_options.'.$key.'.name')
                                                    <span class="text-danger">
                                            <small>{{$message}}</small>
                                        </span>
                                                    @enderror
                                                @endforeach
                                            @elseif(isset($attribute))
                                                @foreach($attribute->attribute_options as $key => $attribute_option)
                                                    <div class="d-flex justify-content-between dynamic_fields_js mt-3">
                                                        <input type="text" value="{{$attribute_option->name}}" name="attribute_options[][name]" class="form-control form-control-sm duplicate-validation variant-option @error('attribute_options.'.$key.'.name') is-invalid @enderror">
                                                        <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                                                            <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                                        </button>
                                                    </div>
                                                    @error('attribute_options.'.$key.'.name')
                                                    <span class="text-danger">
                                            <small>{{$message}}</small>
                                        </span>
                                                    @enderror

                                                @endforeach
                                            @endif
                                        </div>
                                        <button type="button" id="add-btn" class="btn btn-sm btn-primary mb-2 mt-2" onclick="addRow()">
                                            {{trans('variant_attribute.add_option_btn')}}

                                        </button>
                                        <br>
                                        @error('attribute_options')
                                        <span class="text-danger"><small>{{$message}}</small></span>
                                        @enderror
                                    </div>

                                </div>

                                {{--Date and Time--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 5?"show active" :''}}" id="attribute_type_5" role="tabpanel" aria-labelledby="date-time-list">

                                    <div class="row">
                                        <div class="col-md-4">
                                            <h3>Select Type</h3>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="date" name="type" value="date" class="align-middle custom-control-input" {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'date' ? 'checked' : '') : '')  : 'checked'}}>
                                                        <label class="pl-2 font-weight-bold custom-control-label" for="date">Date</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="date_time" name="type" class="align-middle custom-control-input" value="date_and_time" {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'date_and_time' ? 'checked' : '') : '')  : 'checked'}}>
                                                        <label class="pl-2 font-weight-bold custom-control-label" for="date_time">Date and time</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4"></div>
                                        <div class="col-md-4"></div>
                                    </div>
                                    <br>
                                    <div class="row" id="date_only" style="display: {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'date' ? 'flex' : 'none') : '') : '' }}">
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="start_date_time">{{trans('products_attributes_create.start_date')}}</label>
                                                <input type="date" class="form-control @error('start_date') is-invalid @enderror" id="start_date" name="start_date" value="{{isset($response['min']) ? date('Y-m-d', strtotime($response['min'])) : ''}}">
                                                @error('start_date')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="end_date_time">{{trans('products_attributes_create.end_date')}}</label>
                                                <input type="date" class="form-control @error('max') is-invalid @enderror" id="end_date" name="end_date" value="{{isset($response['max']) ? date('Y-m-d', strtotime($response['max'])) : ''}}">
                                                @error('end_date')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="date_and_time" style="display: {{isset($response) ? (isset($response['type']) ? ($response['type'] == 'date_and_time' ? 'flex' : 'none') : '') : '' }}">
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="start_date_time">{{trans('products_attributes_create.start_date_and_time')}}</label>
                                                <input type="datetime-local" class="form-control @error('start_date_time') is-invalid @enderror" id="start_date_time" name="start_date_time" value="{{isset($response['min']) ? date('Y-m-d H:i:s', strtotime($response['min'])) : ''}}">
                                                @error('start_date_time')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="end_date_time">{{trans('products_attributes_create.end_date_and_time')}}</label>
                                                <input type="datetime-local" class="form-control @error('max') is-invalid @enderror" id="end_date_time" name="end_date_time" value="{{isset($response['max']) ? date('Y-m-d H:i:s',strtotime($response['max'])) : ''}}">
                                                @error('end_date_time')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{--File--}}
                                {{-- <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 6?"show active" :''}}" id="attribute_type_6" role="tabpanel"--}}
                                {{-- aria-labelledby="file-list">--}}
                                {{-- --}}{{--Validation text and rules div--}}
                                {{-- <div class="row">--}}
                                {{-- <div class="col-md-4">--}}
                                {{-- <h6>Validation</h6>--}}
                                {{-- <p id="file_validation_text">Accept specific file types.</p>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-4"></div>--}}
                                {{-- <div class="col-md-4"></div>--}}
                                {{-- </div>--}}
                                {{-- <br>--}}
                                {{-- --}}{{--File type rules div--}}
                                {{-- <div class="row">--}}
                                {{-- <div class="col-md-8">--}}
                                {{-- <h6>Rules</h6>--}}
                                {{-- <div class="row">--}}
                                {{-- <div class="col-md-5">--}}
                                {{-- <div class="custom-control custom-radio">--}}
                                {{-- <input type="radio" id="image_videos" name="file_type" class="align-middle custom-control-input" value="specific_files">--}}
                                {{-- <label class="custom-control-label" for="image_videos">Accept specific file types</label>--}}
                                {{-- </div>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-5">--}}
                                {{-- <div class="custom-control custom-radio">--}}
                                {{-- <input type="radio" id="all_files" name="file_type" class="align-middle custom-control-input" value="all_files">--}}
                                {{-- <label class="custom-control-label" for="all_files">Accept all file types--}}
                                {{-- </label>--}}
                                {{-- </div>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-2"></div>--}}
                                {{-- </div>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-2"></div>--}}
                                {{-- <div class="col-md-2"></div>--}}
                                {{-- </div>--}}
                                {{-- <br>--}}
                                {{-- --}}{{--File type div--}}
                                {{-- <div class="row" id="other_files">--}}
                                {{-- <div class="col-md-4">--}}
                                {{-- <h6>Select Type</h6>--}}
                                {{-- <div class="row">--}}
                                {{-- <div class="col-md-4">--}}
                                {{-- <div class="custom-control custom-radio">--}}
                                {{-- <input type="checkbox" id="image" name="file_choice[0]" class="align-middle custom-control-input" value="image">--}}
                                {{-- <label class="custom-control-label" for="image">Image</label>--}}
                                {{-- </div>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-4">--}}
                                {{-- <div class="custom-control custom-radio">--}}
                                {{-- <input type="checkbox" id="video" name="file_choice[1]" class="align-middle custom-control-input" value="video">--}}
                                {{-- <label class="custom-control-label" for="video">Video</label>--}}
                                {{-- </div>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-4"></div>--}}
                                {{-- </div>--}}
                                {{-- </div>--}}
                                {{-- <div class="col-md-4"></div>--}}
                                {{-- <div class="col-md-4"></div>--}}
                                {{-- </div>--}}
                                {{-- <br>--}}
                                {{-- </div>--}}

                                {{--Measurement--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 7?"show active" :''}}" id="attribute_type_7" role="tabpanel" aria-labelledby="measurement-list">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3>Select Type</h3>
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="weight" name="type" class="align-middle custom-control-input" value="weight">
                                                        <label class="custom-control-label" for="weight">Weight</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="volume" name="type" class="align-middle custom-control-input" value="volume">
                                                        <label class="custom-control-label" for="volume">Volume</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="custom-control custom-radio">
                                                        <input type="radio" id="dimension" name="type" class="align-middle custom-control-input" value="dimension">
                                                        <label class="custom-control-label" for="dimension">Dimension</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <div class="row" id="weight_fields">
                                            <div class="col-2">
                                                <label for="weight_min_select">{{trans('products_attributes_create.all_unit')}}</label>
                                                <select name="min_unit" data-confirm-before-leave="true" class="form-control" style="border: 1px solid #d7d7d7; background-color: #F2F2F3; width:100%" id="weight_min_select">
                                                    @foreach (Unit::$WEIGHT_UNITS as $key => $unit_value)
                                                        <option value="{{ $key }}" {{ (isset($response['min_unit']) && $response['min_unit'] == $key) ? 'selected' : '' }}>{{ Unit::formatUnitKey($key) }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-5">
                                                <label for="min_weight">{{trans('products_attributes_create.min_weight')}}</label>
                                                <input type="number" class="form-control @error('min_weight') is-invalid @enderror" id="min_weight" name="min" min="0" @isset($attribute) @if($attribute->attribute_type_id == 7)
                                                    value="{{isset($response['min']) ? $response['min'] : ''}}"
                                                    @endif
                                                    @endisset>
                                            </div>
                                            <div class="col-5">
                                                <label for="max_weight">{{trans('products_attributes_create.max_weight')}}</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend" style="display: none;">
                                                        <select name="max_unit" data-confirm-before-leave="true" class="form-control input-prepend-select pr-2" style="border: 1px solid #d7d7d7; background-color: #F2F2F3; margin-right: 1px;width:70px" id="weight_max_select">
                                                            @foreach (Unit::$WEIGHT_UNITS as $key => $unit_value)
                                                                <option value="{{ $key }}" {{ (isset($response['max_unit']) && $response['max_unit'] == $key) ? 'selected' : '' }}>{{ Unit::formatUnitKey($key) }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <input type="number" class="form-control @error('max_weight') is-invalid @enderror" id="max_weight" name="max" min="0" @isset($attribute) @if($attribute->attribute_type_id == 7)
                                                        value="{{isset($response['max']) ? $response['max'] : ''}}"
                                                        @endif
                                                        @endisset>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" id="volume_fields">
                                            <div class="col-2">
                                                <label for="volume_min_select">{{trans('products_attributes_create.all_unit')}}</label>
                                                <select name="min_unit" data-confirm-before-leave="true" class="form-control" style="border: 1px solid #d7d7d7; background-color: #F2F2F3; width:100%" id="volume_min_select">
                                                    @foreach (Unit::$VOLUME_UNITS as $key => $unit_value)
                                                        <option value="{{ $key }}" {{ (isset($response['min_unit']) && $response['min_unit'] == $key) ? 'selected' : '' }}>{{ Unit::formatUnitKey($key) }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-5">
                                                <label for="min_volume">{{trans('products_attributes_create.min_volume')}}</label>
                                                <input type="number" class="form-control @error('min_volume') is-invalid @enderror" id="min_volume" name="min" min="0" @isset($attribute) @if($attribute->attribute_type_id == 7)
                                                    value="{{isset($response['min']) ? $response['min'] : ''}}"
                                                    @endif
                                                    @endisset>
                                            </div>
                                            <div class="col-5">
                                                <label for="max_weight">{{trans('products_attributes_create.max_volume')}}</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend" style="display: none;">
                                                        <select name="max_unit" data-confirm-before-leave="true" class="form-control input-prepend-select pr-2" style="border: 1px solid #d7d7d7; background-color: #F2F2F3; margin-right: 1px;" id="volume_max_select">
                                                            @foreach (Unit::$VOLUME_UNITS as $key => $unit_value)
                                                                <option value="{{ $key }}" {{ (isset($response['max_unit']) && $response['max_unit'] == $key) ? 'selected' : '' }}>{{ Unit::formatUnitKey($key) }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <input type="number" class="form-control @error('max_volume') is-invalid @enderror" id="max_volume" name="max" min="0" @isset($attribute) @if($attribute->attribute_type_id == 7)
                                                        value="{{isset($response['max']) ? $response['max'] : ''}}"
                                                        @endif
                                                        @endisset>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" id="dimension_fields">
                                            <div class="col-2">
                                                <label for="dimension_min_select">{{trans('products_attributes_create.all_unit')}}</label>
                                                <select name="min_unit" data-confirm-before-leave="true" class="form-control" style="border: 1px solid #d7d7d7; background-color: #F2F2F3; width:100%" id="dimension_min_select">
                                                    @foreach (Unit::$DIMENSION_UNITS as $key => $unit_value)
                                                        <option value="{{ $key }}" {{ (isset($response['min_unit']) && $response['min_unit'] == $key) ? 'selected' : '' }}>{{ Unit::formatUnitKey($key) }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-5">
                                                <label for="min_dimension">{{trans('products_attributes_create.min_dimension')}}</label>
                                                <input type="number" class="form-control @error('min_dimension') is-invalid @enderror" id="min_dimension" name="min" min="0" @isset($attribute) @if($attribute->attribute_type_id == 7)
                                                    value="{{isset($response['min']) ? $response['min'] : ''}}"
                                                    @endif
                                                    @endisset>
                                            </div>
                                            <div class="col-5">
                                                <label for="max_weight">{{trans('products_attributes_create.max_dimension')}}</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend" style="display: none;">
                                                        <select name="max_unit" data-confirm-before-leave="true" class="form-control input-prepend-select pr-2" style="border: 1px solid #d7d7d7; background-color: #F2F2F3; margin-right: 1px;width:70px" id="dimension_max_select">
                                                            @foreach (Unit::$DIMENSION_UNITS as $key => $unit_value)
                                                                <option value="{{ $key }}" {{ (isset($response['max_unit']) && $response['max_unit'] == $key) ? 'selected' : '' }}>{{ Unit::formatUnitKey($key) }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <input type="number" class="form-control @error('max_dimension') is-invalid @enderror" id="max_dimension" name="max" min="0" @isset($attribute) @if($attribute->attribute_type_id == 7)
                                                        value="{{isset($response['max']) ? $response['max'] : ''}}"
                                                        @endif
                                                        @endisset>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <br>
                                </div>

                                {{--Rating--}}
                                <div class="tab-pane px-3 py-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 8?"show active" :''}}" id="attribute_type_8" role="tabpanel" aria-labelledby="rating-list">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h3>Validation</h3>
                                            <p>Values must be numbers.</p>
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="col-md-3"></div>
                                    </div>
                                </div>

                                {{--JSON--}}
                                <div class="tab-pane p-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 9?"show active" :''}}" id="attribute_type_9" role="tabpanel" aria-labelledby="json-list">
                                </div>

                                {{--True or False--}}
                                <div class="tab-pane p-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 10?"show active" :''}}" id="attribute_type_10" role="tabpanel" aria-labelledby="true-false-list">
                                </div>

                                {{--URL--}}
                                <div class="tab-pane p-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 11?"show active" :''}}" id="attribute_type_11" role="tabpanel" aria-labelledby="url-list">
                                </div>

                                {{--Color--}}
                                <div class="tab-pane p-0 fade {{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 12?"show active" :''}}" id="attribute_type_12" role="tabpanel" aria-labelledby="color-list">
                                </div>

                                {{-- Regular Expression --}}
                                <div class="px-3 py-0
                                 {{ in_array(old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null), [1, 3]) ?
                                    "d-block" :
                                    (empty(old("attribute_type_id")) && !isset($attribute) ? "d-block" : "d-none") }}" id="regular_expression">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group mb-3">
                                                <label for="regular_expression">{{trans('products_attributes_create.regular_expression')}}</label>
                                                <input type="text" class="form-control @error('regular_expression') is-invalid @enderror" placeholder="e.g /$\{[a-zA-Z1-9_]+\}/" name="regular_expression" id="regular_expression_input" value="{{ old("regular_expression", isset($response['regex']) ? $response['regex'] : '') }}">
                                                <span class="text-danger in-valid-text-main d-none">
                                                <small class="errorMsg"></small>
                                            </span>
                                                @error('regular_expression')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{--Min/Max inputs--}}
                                <div class="px-3 py-0" id="min_max_inputs" style="{{ in_array(old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null), [4,5,6,7,9,10,11,12]) ? "display:none" : null }}">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="min_length" id="attribute_min_length">{{trans('products_attributes_create.min_length')}}</label>
                                                <input type="number" class="form-control @error('min') is-invalid @enderror" id="min_length" name="min" min="0" @isset($attribute) value="{{old('min') ? old('min') :( isset($response['min']) ? $response['min'] : '')}}" @endisset {{ in_array(old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null), [4,5,6,7,10,11,12]) ? "disabled" : null}}>
                                                @error('min')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="max_length" id="attribute_max_length">{{trans('products_attributes_create.max_length')}}</label>
                                                <input type="number" class="form-control @error('max') is-invalid @enderror" id="max_length" name="max" min="0" @isset($attribute) value="{{old('max') ? old('max') : (isset($response['max']) ? $response['max'] : '')}}" @endisset {{ in_array(old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null), [4,5,6,7,10,11,12]) ? "disabled" : null}}>
                                                @error('max')
                                                <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{--is Required--}}
                                <div class="form-group px-3 mt-3">
                                    <label for="" class="mb-2">{{trans('products_attributes_create.is_required')}}</label>
                                    <div class="custom-control custom-switch">
                                        <label for="customSwitch2" class="me-2 Roboto regular">
                                            No
                                        </label>
                                        <div class="form-check form-switch d-inline" id="draft">
                                            <input name="is_required" type="checkbox" value="1" {{ old("is_required") ? "checked" : (isset($response['required']) ? "checked" : null )}} class="form-check-input" data-confirm-before-leave="true" id="customSwitch2">
                                            <label class="ms-2" for="customSwitch2">
                                                {{trans('products_attributes_create.yes')}}
                                            </label>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="form-group mt-40 float-end me-2">
                        <div class="d-flex">
                            <a href="{{route('attributes.index')}}" class="btn btn-outline-danger" id="cancel-btn">
                                {{trans('products_attributes_create.cancel_btn')}}
                            </a>
                            <button type="submit" id="pro_attr_create_btn" class="btn btn-primary ms-2">
                                {{trans('products_attributes_create.save_btn')}}

                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

@endsection
@push('footer_scripts')
    <script>
        function attributeType(id) {
            $("#" + id).find('.select_attribute_option').prop("checked", true);
            if (id == 'input_type_5') {
                $("#date").click()
            }
        }

        @if(old('attribute_type_id', (isset($attribute) && ($attribute->attribute_type_id != 1))))
        $("#input_type_1").removeClass("active");
        @endif

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const regexInput = document.getElementById('regular_expression_input');
            const saveButton = document.getElementById('pro_attr_create_btn');

            function toggleSaveButton() {
                // Disable the button only if the regex input has a value and it's marked as invalid
                if (regexInput.value.trim() !== "" && regexInput.classList.contains('is-invalid')) {
                    saveButton.disabled = true;
                } else {
                    saveButton.disabled = false;
                }
            }

            // Listen for input changes to adjust button state accordingly
            regexInput.addEventListener('input', function() {
                // You can implement a specific regex pattern validation here if needed
                const regexPattern = /^\/.+\/$/; // Example: pattern should start and end with /

                if (regexInput.value.trim() !== "" && !regexPattern.test(regexInput.value)) {
                    regexInput.classList.add('is-invalid');
                } else {
                    regexInput.classList.remove('is-invalid');
                }
                toggleSaveButton();
            });
        })

    </script>
    <script>
        $("#date").click(function() {
            $("#start_date").prop('disabled', false)
            $("#end_date").prop('disabled', false)
            $("#start_date_time").prop('disabled', true)
            $("#end_date_time").prop('disabled', true)

        })

        $("#date_time").click(function() {
            $("#start_date").prop('disabled', true)
            $("#end_date").prop('disabled', true)
            $("#start_date_time").prop('disabled', false)
            $("#end_date_time").prop('disabled', false)
        })

    </script>

    <script>
        function activeClass() {
            const tabs = document.querySelectorAll('.list-group-item');
            const contentSections = document.querySelectorAll('.list-group-item');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active state from all the tabs
                    tabs.forEach(tab => tab.classList.remove('active'));
                    // Remove active state from all the content sections
                    contentSections.forEach(section => section.classList.remove('active'));

                    // Add active state to the clicked tab and corresponding content section
                    tab.classList.add('active');
                    const contentId = tab.dataset.contentId;
                    const correspondingSection = document.getElementById(contentId);
                    correspondingSection.classList.add('active');
                });
            });
        }
        $(document).ready(function() {
            activeClass();
        });

        $("#input_type_1").click(function() {
            $("#min_max_inputs").css('display', 'block');
            $("#min_length").prop('disabled', false);
            $("#max_length").prop('disabled', false);
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#attribute_min_length').text('Minimum Character Length')
            $('#attribute_max_length').text('Maximum Character Length')
            $('#single_line_text_message').text('Values can contain letters, numbers, and special characters.');
            $('#single_line_text_message').removeClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("#regular_expression").addClass('d-block');
            $("#regular_expression").removeClass('d-none');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").addClass('d-none');

        });

        $("#input_type_2").click(function() {
            $("#integer").click()
            $("#min_max_inputs").css('display', 'block');
            $("#min_length").prop('disabled', false);
            $("#max_length").prop('disabled', false);
            $('#select').removeClass('active show');
            $('#attribute_type_2').addClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#single_line_text_message').text('Values must be numbers with a decimal.');
            $('#single_line_text_message').removeClass('d-none');
            $('#attribute_min_length').text('Minimum Value')
            $('#attribute_max_length').text('Maximum Value')
            $("#regular_expression").removeClass('d-block');
            $("#regular_expression").addClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');

        })

        $('#price').on('click', function() {
            $('#precision_field').addClass('d-none')
            $('#radio-buttons').addClass('d-none')
            $('input#single').prop('checked', true);
            $('input#list').prop('checked', false);

        })
        $('#decimal').on('click', function() {
            $('#precision_field').removeClass('d-none')
            $('#radio-buttons').removeClass('d-none')
        })

        $("#input_type_3").click(function() {
            $("#min_max_inputs").css('display', 'block');
            $("#min_length").prop('disabled', false);
            $("#max_length").prop('disabled', false);
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_min_length').text('Minimum Character Length')
            $('#attribute_max_length').text('Maximum Character Length')
            $('#single_line_text_message').text('Values can contain letters, numbers, and special characters.');
            $('#single_line_text_message').removeClass('d-none');
            $("#regular_expression").addClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-block');
            $("#radio-buttons").addClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

        $(".input_type_4").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $("#min_length").prop('disabled', false);
            $("#max_length").prop('disabled', false);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $('#single_line_text_message').text('Values can contain letters, numbers, and special characters.');
            $('#single_line_text_message').removeClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("input#name").removeClass("is-invalid");
            $('#select').addClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").addClass('d-none');
        })

        $("#input_type_5").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').addClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#single_line_text_message').text('Values must be dates.');
            $('#single_line_text_message').removeClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

        $("#input_type_6").click(function() {
            $("#image_videos").click();
            $("#min_max_inputs").css('display', 'none');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#single_line_text_message').text('Values must be numbers.');
            $('#single_line_text_message').removeClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

        $("#input_type_7").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_7').addClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#single_line_text_message').text('Values must be numbers.');
            $('#single_line_text_message').removeClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

        $("#input_type_8").click(function() {
            $("#min_max_inputs").css('display', 'block');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#single_line_text_message').text('Values must be numbers.');
            $('#single_line_text_message').removeClass('d-none');
            $('#single_line_text_header').removeClass('d-none');
            $("#min_length").prop('disabled', false);
            $("#max_length").prop('disabled', false);
            $('#attribute_min_length').text('Minimum Value')
            $('#attribute_max_length').text('Maximum Value')
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

        $("#input_type_9").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $('#single_line_text_message').addClass('d-none');
            $('#single_line_text_header').addClass('d-none');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").addClass('d-none');
            $("#border-bottom-multivalue").addClass('d-none');
        })

        $("#input_type_10").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $('#single_line_text_message').addClass('d-none');
            $('#single_line_text_header').addClass('d-none');
            $("#radio-buttons").addClass('d-none');
            $("#border-bottom-multivalue").addClass('d-none');
        })

        $("#input_type_11").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $('#single_line_text_message').addClass('d-none');
            $('#single_line_text_header').addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

        $("#input_type_12").click(function() {
            $("#min_max_inputs").css('display', 'none');
            $('#select').removeClass('active show');
            $('#attribute_type_2').removeClass('active show');
            $('#attribute_type_5').removeClass('active show');
            $('#attribute_type_7').removeClass('active show');
            $("#min_length").prop('disabled', true);
            $("#max_length").prop('disabled', true);
            $("#regular_expression").addClass('d-none');
            $("#regular_expression").removeClass('d-block');
            $('#single_line_text_message').addClass('d-none');
            $('#single_line_text_header').addClass('d-none');
            $("input#name").removeClass("is-invalid");
            $("small.error-message-text").addClass('d-none');
            $("#radio-buttons").removeClass('d-none');
            $("#border-bottom-multivalue").removeClass('d-none');
        })

    </script>

    <script src="https://unpkg.com/gijgo@1.9.13/js/gijgo.min.js" type="text/javascript"></script>

    {{--Number attribute js--}}
    <script>
        $("#integer").click(function() {
            $("#precision_field").css("display", 'none')
            $("#max_number_precision").attr('disabled', true)
            $('#radio-buttons').removeClass('d-none')
            // $("#validation_text").text("Values must be numbers without a decimal.")
        });

        $("#decimal").click(function() {
            $("#precision_field").css("display", 'block')
            $("#max_number_precision").removeAttr('disabled')
            // $("#validation_text").text("Values must be numbers with a decimal.")

        });

    </script>

    {{--Date and time attribute js--}}
    <script>
        @if(isset($attribute) && $attribute->attribute_type_id == 5)
        $("#min_max_inputs").css('display', 'none')
        @endif

        $("#date").click(function() {
            $("#date_only").css("display", 'flex')
            $("#date_and_time").css("display", 'none')
            $("#date_time").prop('checked', false)
            $("#date-validation_text").text("Values must be dates.")
        });

        $("#date_time").click(function() {
            $("#date_only").css("display", 'none')
            $("#date_and_time").css("display", 'flex')
            $("#date").prop('checked', false)
            $("#date-validation_text").text("Values must include a date and a time.")

        });

    </script>

    {{--File attribute tab Js--}}
    <script>
        $("#image_videos").click(function() {
            $("#image").click()
            $("#file_validation_text").text("Accept specific file types.")
            $("#other_files").css("display", 'flex')
            //enabling image and video inputs
            $("#image").prop("disabled", false)
            $("#video").prop("disabled", false)
        });

        $("#all_files").click(function() {
            $("#file_validation_text").text("Accept all file types.")
            $("#other_files").css("display", 'none')
            //disabling image and video inputs
            $("#image").prop("disabled", true)
            $("#video").prop("disabled", true)
        });

    </script>

    {{--Measurement attribute tab Js--}}
    <script>
        $("#input_type_7").click(function() {
            $("#weight").click();
        })

        $("#weight").click(function() {
            $("#weight_fields").css('display', 'flex')
            $("#volume_fields").css('display', 'none')
            $("#dimension_fields").css('display', 'none')

            //enabling weight unit select
            $("#weight_max_select").removeAttr('disabled')
            $("#weight_min_select").removeAttr('disabled')
            $("#volume_max_select").attr('disabled', 'disabled')
            $("#volume_min_select").attr('disabled', 'disabled')
            $("#dimension_max_select").attr('disabled', 'disabled')
            $("#dimension_min_select").attr('disabled', 'disabled')


            $("#min_weight").removeAttr('disabled')
            $("#max_weight").removeAttr('disabled')
            $("#min_volume").attr('disabled', 'disabled')
            $("#max_volume").attr('disabled', 'disabled')
            $("#min_dimension").attr('disabled', 'disabled')
            $("#max_dimension").attr('disabled', 'disabled')
        })

        $("#volume").click(function() {
            $("#weight_fields").css('display', 'none')
            $("#volume_fields").css('display', 'flex')
            $("#dimension_fields").css('display', 'none')

            //enabling volume unit select
            $("#weight_max_select").attr('disabled', 'disabled')
            $("#weight_min_select").attr('disabled', 'disabled')
            $("#volume_max_select").removeAttr('disabled')
            $("#volume_min_select").removeAttr('disabled')
            $("#dimension_max_select").attr('disabled', 'disabled')
            $("#dimension_min_select").attr('disabled', 'disabled')

            $("#min_weight").attr('disabled', 'disabled')
            $("#max_weight").attr('disabled', 'disabled')
            $("#min_volume").removeAttr('disabled')
            $("#max_volume").removeAttr('disabled')
            $("#min_dimension").attr('disabled', 'disabled')
            $("#max_dimension").attr('disabled', 'disabled')
        })

        $("#dimension").click(function() {
            $("#weight_fields").css('display', 'none')
            $("#volume_fields").css('display', 'none')
            $("#dimension_fields").css('display', 'flex')

            //enabling dimensions unit select
            $("#weight_max_select").attr('disabled', 'disabled')
            $("#weight_min_select").attr('disabled', 'disabled')
            $("#volume_max_select").attr('disabled', 'disabled')
            $("#volume_min_select").attr('disabled', 'disabled')
            $("#dimension_max_select").removeAttr('disabled')
            $("#dimension_min_select").removeAttr('disabled')

            $("#min_weight").attr('disabled', 'disabled')
            $("#max_weight").attr('disabled', 'disabled')
            $("#min_volume").attr('disabled', 'disabled')
            $("#max_volume").attr('disabled', 'disabled')
            $("#min_dimension").removeAttr('disabled')
            $("#max_dimension").removeAttr('disabled')
        })

        @if(old("type", isset($response["type"])))
        @if(old("type", isset($response["type"]) ? $response["type"] : null) == "dimension")
        $("#dimension").click();
        @elseif(old("type", isset($response["type"]) ? $response["type"] : null) == "volume")
        $("#volume").click();
        @elseif(old("type", isset($response["type"]) ? $response["type"] : null) == "weight")
        $("#weight").click();
        @elseif(old("type", isset($response["type"]) ? $response["type"] : null) == "integer")
        $("#integer").click();
        @elseif(old("type", isset($response["type"]) ? $response["type"] : null) == "decimal")
        $("#decimal").click();
        @elseif(old("type", isset($response["type"]) ? $response["type"] : null) == "date")
        $("#date").click();
        @elseif(old("type", isset($response["type"]) ? $response["type"] : null) == "date_and_time")
        $("#date_time").click();
        @endif
        @endif

        @if(old("file_type", isset($response["type"])))
        @if(old("file_type", isset($response["type"]) ? $response["type"] : null) == "specific_files")
        $("#image_videos").click();
        @elseif(old("file_type", isset($response["type"]) ? $response["type"] : null) == "all_files")
        $("#all_files").click();
        @endif
        @endif

    </script>

    <script>
        @isset($attribute)
        //date and time
        @if($attribute->attribute_type_id == 5)
        $(document).ready(function() {
            @if($response['type'] == 'date')
            $("#start_date").prop('disabled', false)
            $("#end_date").prop('disabled', false)
            $("#start_date_time").prop('disabled', true)
            $("#end_date_time").prop('disabled', true)
            @else
            $("#start_date").prop('disabled', true)
            $("#end_date").prop('disabled', true)
            $("#start_date_time").prop('disabled', false)
            $("#end_date_time").prop('disabled', false)
            @endif

        });
        @endif
        @endisset

    </script>
    {{--! add variant option script !--}}
    <script src="{{ asset('assets/js/add-attribute-options.js?v='.env("APP_VERSION")) }}"></script>
    <script>
        let i = 1;
        let addbutton;

        //Showing multiple select card on clicking
        $("input[name='attribute_type_id']").click(function() {
            if ($(this).val() == 4) {
                $("#multiselect-card").fadeIn();
            } else {
                $("#multiselect-card").hide();
            }
        });

        //opening mutliselect card on editing multiselet type attribute
        {
            {
                --@if(old("attribute_options", isset($attribute) ? count($attribute->attribute_options) > 0 : false)) --
            }
        } {
            {
                --$("input[name='attribute_type_id']").click();
                --
            }
        } {
            {
                --@endif--
            }
        }

        @if(!old("attribute_type_id"))
        $("#single-line-text-list").click();
        @endif
        @if(old("attribute_type_id") == 4)
        $("#input_type_4").click();
        @endif

    </script>
    {{-- Apply frontend validation on regular expression input field   --}}
    <script>
        $(document).ready(function() {
            const input_reg = $('input[name=regular_expression]');
            const main_div = $(".in-valid-text-main");
            const errorMsg = $(".errorMsg");
            const regex = /^\/(.+)\/([a-z]*)$/;
            input_reg.each(function() {
                if (this.value) {
                    regex.test(this.value) ? input_reg.removeClass('is-invalid') : input_reg.addClass('is-invalid');
                } else {
                    input_reg.removeClass('is-invalid');
                }
                if (this.value && !regex.test(this.value)) {
                    main_div.removeClass('d-none');
                    errorMsg.text('Sorry, but the format you provided is in an invalid.')
                } else {
                    main_div.addClass('d-none');
                }
            });
            input_reg.on('keyup', function(e) {
                regex.test(this.value) ? input_reg.removeClass('is-invalid') : input_reg.addClass('is-invalid');
                if (this.value && !regex.test(this.value)) {
                    main_div.removeClass('d-none');
                    errorMsg.text('Sorry, but the format you provided is in an invalid.')
                } else {
                    main_div.addClass('d-none');
                }
            });
            input_reg.on('blur', function(e) {
                if (this.value) {
                    regex.test(this.value) ? input_reg.removeClass('is-invalid') : input_reg.addClass('is-invalid');
                } else {
                    input_reg.removeClass('is-invalid');
                }
                if (this.value && !regex.test(this.value)) {
                    main_div.removeClass('d-none');
                    errorMsg.text('Sorry, but the format you provided is in an invalid.')
                } else {
                    main_div.addClass('d-none');
                }

            });
            $('select.sumoselect').click(function(e) {

                if ($(".sumoselect").attr("aria-disabled") == "true") {
                    $(".optWrapper").remove();
                }
            })
        });

    </script>
@endpush
