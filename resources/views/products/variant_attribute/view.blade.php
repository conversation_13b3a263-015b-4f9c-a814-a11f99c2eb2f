@extends('layouts.app_new')
@section('titles','Variant Attributes')
@section('content')
    <div>

        <x-products.page-title name="{{trans('variant_attribute.page_title')}}" description="{{trans('variant_attribute.page_description')}}"
                               links="true" button="true">
            <x-slot name="addbutton">
                <a href="" style="width: 194px" id="add-category"
                   class="btn btn-primary ripplelink
                               float-lg-right float-md-right" data-bs-toggle="modal" data-bs-target="#addAttributeModel">{{trans('variant_attribute.add_category_btn')}}</a>
            </x-slot>
        </x-products.page-title>

        <div class="row">
            <div class="col-12 col-md-6 col-xl-3">
                @if(isset($data) && count($data["variant_attribute"]) > 0)
                    <x-general.search-bar placeholder="{{trans('variant_attribute.search_placeholder')}}"/>
                @endif
            </div>
        </div>

        <div class="row mt-2">
            <div class="col-12">
                @if(isset($data) && count($data["variant_attribute"]) > 0)
                    <table class="table">
                        <thead >
                        <tr class="custom-border-bottom-css">
                            <th scope="col">
                                {{ __('Variant Option Name') }}
                            </th>
                            <th scope="col" class=" text-end">{{ __('Actions') }}
                            </th>
                        </tr>
                        </thead>
                        <tbody>

                        @foreach($data["variant_attribute"] as $attr)
                        @if(!($attr->attribute_type_id == 13 && $attr->handle == 'title' && $attr->is_default == 1))
                            <tr>
                                <td>
                                    {{__(substr($attr->name,0,50))}}
                                </td>
                                <td class="text-end">
                                    <a href="{{route('variant_attribute.edit',$attr->id)}}"
                                       class="pro_cat_edit text-decoration-none">
                                        <i class="fa-regular fa-pen-to-square fs-20"></i>
                                    </a>
                                    {{-- @if(!$attr->is_default)
                                        <a href="#" data-id="{{$attr->id}}" data-retailer-name=""
                                           data-bs-toggle="modal" data-bs-target="#delete-modal-{{$attr->id}}" class="btn-delete text-decoration-none">
                                            <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                        </a>
                                    @endif --}}
                                </td>

                            </tr>
                            <x-assets.delete-modal id="{{$attr->id}}" text="Are you sure you want to delete this Variant?" button="Delete Variant" title="Delete Variant" url="{{route('attributes.destroy',$attr->id)}}" type="brand"/>
                        @endif
                        @endforeach

                        </tbody>
                    </table>
                    {!! $data["variant_attribute"]->appends($request->all())->links() !!}
                @else
                    <x-general.empty-page description="{{trans('variant_attribute.page_empty')}}"/>
                @endif
            </div>

        </div>
    </div>






    {{--Add Attribute model--}}
    <div class="modal fade" id="addAttributeModel" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form action="{{route('attributes.store')}}" method="post">
                    @csrf
                    <div class="modal-header">
                        <h3 class="modal-title"
                            id="exampleModalLabel">{{trans('variant_attribute.add_option_model')}}</h3>
                        <button type="button" class="btn-sm border-0 bg-white close fs-24" data-bs-dismiss="modal"
                                aria-label="Close">

                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="attribute_type_id" value="13">
                        {{--Attribute Name--}}
                        <div class="formStyle pt-0">
                            <label for="name" class="mb-0">{{trans('products_variants_step1.attribute_title')}}
                                &nbsp;<span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name"
                                   value="{{ old('name') }}" required
                                   autofocus>
                            @error('name')
                            <span class="text-danger">
                                        <small>{{$message}}</small>
                                    </span>
                            @enderror
                        </div>
                        {{--Multi Select--}}
                        <div class="tab-pane py-0 fade show active {{--{{ old("attribute_type_id", isset($attribute) ? $attribute->attribute_type_id : null) == 4 ? "show active" : null }}--}}" id="select" role="tabpanel"
                             aria-labelledby="input_type_4">
                            <div class="form-group mt-3" id="multiselect-card">
                                <label for="">{{trans('variant_attribute_update.manage_option')}}</label>
                                <div class="dynamic-field formStyle">
                                    @if(old("attribute_options"))
                                        @foreach(old("attribute_options") as $key => $attribute_option)
                                            <div class="d-flex justify-content-between dynamic_fields_js mt-3" >
                                                <input type="text" value="{{$attribute_option["name"]}}"
                                                       name="attribute_options[][name]"
                                                       class="form-control form-control-sm duplicate-validation variant-option @error('attribute_options.'.$key.'.name') is-invalid @enderror">
                                                <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                                                    <img src="http://localhost:8000/media/retailer-dashboard/delete.png"
                                                         alt="">
                                                </button>
                                            </div>
                                            @error('attribute_options.'.$key.'.name')
                                            <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                            @enderror
                                        @endforeach
                                    @endif
                                </div>
                                <button type="button" id="add-btn"
                                        class="btn btn-sm btn-primary mb-2 mt-2" onclick="addRow()">
                                    {{trans('variant_attribute.add_option_btn')}}

                                </button>
                                <br>
                                @error('attribute_options')
                                <span class="text-danger"><small>hello{{$message}}</small></span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer py-3 px-3">
                        <button type="button" class="btn btn-light border" data-bs-dismiss="modal">Close</button>

                        <button type="submit" class="btn btn-primary">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
    {{--!  add variant option script !--}}
    <script src="{{ asset('assets/js/add-attribute-options.js') }}"></script>

    <script>

        let attrid;
        let attrname;

        $(".btn-delete").click(function () {
            attrid = $(this).attr('data-id');
            attrname = $(this).attr('data-retailer-name');
            document.getElementById('name').innerHTML = attrname;
        });

        function del() {
            var form = document.getElementById('pro_attr_del_btn ');
            form.setAttribute('action', 'attributes/' + attrid);
            form.submit();
        }

        $(document).ready(function () {
            $('#AssignCategory').SumoSelect({
                placeholder: 'Select Category',
                csvDispCount: 4,
                captionFormat: '{0} Selected',
                captionFormatAllSelected: 'All Options Selected',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: true,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
        });
    </script>

    {{--add attribute model js, model opening on validation error--}}
    <script>
        $(document).ready(function() {
            @error('name')
            $('#addAttributeModel').modal('show');
            @enderror

            @error('attribute_family')
            $('#addAttributeModel').modal('show');
            @enderror

            @error('attribute_options')
            $('#addAttributeModel').modal('show');
            @enderror
        })
    </script>

    <script>
        let i = 1;
        let addbutton;

        //Showing multiple select card on clicking
        $("input[name='attribute_type_id']").click(function () {
            if ($(this).val() == 4) {
                $("#multiselect-card").fadeIn();
            } else {
                $("#multiselect-card").hide();
            }
        });

        //opening mutliselect card on editing multiselet type attribute
        {{--        @if(old("attribute_options", isset($attribute) ? count($attribute->attribute_options) > 0 : false))--}}
        {{--        $("input[name='attribute_type_id']").click();--}}
        {{--        @endif--}}

        @if(!old("attribute_type_id"))
        $("#single-line-text-list").click();
        @endif
        @if(old("attribute_type_id") == 4)
        $("#input_type_4").click();
        @endif
    </script>

@endpush

