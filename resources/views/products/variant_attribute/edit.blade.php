@extends('layouts.app_new')
@section('titles','Edit Variant Attributes')
@section('content')
        <x-products.add-page-title name="{{trans('variant_attribute.page_title')}}" :routes="route('variant_attribute.index')"/>
        <div class="row mt-4">
            <div class="col-12 col-lg-8 col-xxl-6">
                <form action="{{route('attributes.update',$attribute->id)}}" method="post" class="formStyle">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="attribute_type_id" name="attribute_type_id" value="{{$attribute->attribute_type_id}}">
                    <input type="hidden" name="id" value="{{$attribute->id}}">
                    {{--Attribute Name--}}
                    <div class="form-group mb-3">
                        <label for="name">{{trans('variant_attribute_update.attribute_title')}}&nbsp;<span
                                class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                               id="name" name="name"
                               value="{{ old('name', isset($attribute) ? $attribute->name : null) }}" required
                               autofocus>
                        @error('name')
                        <span class="text-danger"><small>{{$message}}</small></span>
                        @enderror
                    </div>
                    {{--Multi Select--}}
                        <div class="form-group ">
                            <label for="">{{trans('variant_attribute_update.manage_option')}}</label>
                            <div class="dynamic-field formStyle">
                                @if(old("attribute_options"))
                                    @foreach(old("attribute_options") as $key => $attribute_option)
                                        <div class="d-flex justify-content-between dynamic_fields_js mt-3">
                                            <input type="text"  value="{{$attribute_option["name"]}}" name="attribute_options[][name]"
                                                   class="form-control form-control-sm duplicate-validation variant-option @error('attribute_options.'.$key.'.name') is-invalid @enderror">
                                            @error('attribute_options.'.$key.'.name')
                                            <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                            @enderror
                                            <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                                                <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                            </button>
                                        </div>

                                    @endforeach
                                @elseif(isset($attribute))
                                    @foreach($attribute->attribute_options as $attribute_option)
                                        <div class="d-flex justify-content-between dynamic_fields_js mt-3">
                                            <input type="hidden" name="attribute_options[{{$attribute_option->id}}][id]" value="{{$attribute_option->id}}">
                                            <input type="text" value="{{$attribute_option->name}}" name="attribute_options[{{$attribute_option->id}}][name]" class="form-control form-control-sm duplicate-validation variant-option @error('attribute_options.'.$attribute_option->id.'.name') is-invalid @enderror">
                                            {{-- <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                                                <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                            </button> --}}
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                            <button type="button" id="add-btn"
                                    class="btn btn-sm btn-primary my-3" onclick="addRow()">
                                {{trans('variant_attribute_update.add_option_btn')}}

                            </button>
                            @error('attribute_options')
                            <span class="text-danger">
                                <small>{{$message}}</small>
                            </span>
                            @enderror
                        </div>
                    <hr class="mt-0">
                    <div class="col-12 d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">Save changes</button>
                    </div>
                </form>
            </div>
        </div>


@endsection
@push('footer_scripts')
{{--!  add variant option script !--}}
    <script src="{{ asset('assets/js/add-attribute-options.js') }}"></script>
@endpush
