<?php ?>
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Category')
@else
    @section('titles','Add Categories')
@endif
@section('content')
    @push("header_scripts")
        <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://cdn.materialdesignicons.com/5.0.45/css/materialdesignicons.min.css">
        <link rel="stylesheet" href="{{asset('treeselect/style.css')}}">

            <link rel="stylesheet" href="{{ asset('treeSelectJs/treeselectjs.css') }}" />

    @endpush
    <div>
        <x-products.add-page-title name="{{trans('products_categories.page_title')}}"
                                   type="false"
                                   :routes="route('categories.index')"/>
        <div class="row mt-4">
            <div class="col-12 col-md-9 col-lg-9 col-xl-5">
                <form id="pro_cat_create_form" class="formStyle"
                      action="{{isset($category) ? route('categories.update',$category->id) : route('categories.store')}}"
                      method="post">
                    @csrf
                    @if(isset($category))
                        @method('PUT')
                        <input type="hidden" name="id" value="{{$category->id}}">
                    @endif
                    <div class="form-group">
                        <label for="name">{{trans('products_categories_create.category_name')}}
                            &nbsp;<span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{isset($category) ? $category->name : old('name')}}" autofocus required>
                        @error('name')
                        <span class="text-danger">
                        <small>{{$message}}</small>
                        </span>
                        @enderror
                    </div>
                    <div class="form-group mt-3 mb-0">
                        <label for="description">{{trans('products_categories_create.description')}}</label>
                        <textarea type="text" class="form-control h-120 @error('description') is-invalid @enderror" id="description" name="description">{{isset($category)? $category->description : old('description')}}</textarea>
                        @error('description')
                        <span class="text-danger">
                        <small>{{$message}}</small>
                        </span>
                        @enderror
                    </div>

                    <label class="mt-3"
                           for="status1">{{trans('products_categories_create.category_toggle')}}</label>
                    <div class="custom-control custom-switch mt-2 d-flex align-items-center">
                        <label for="status1" class="me-2 Roboto regular">
                            No
                        </label>
                        <div class="form-check form-switch d-inline" id="draft">
                            <input name="status" type="checkbox" value="1" class="form-check-input Assign-parent-category" data-confirm-before-leave="true"  id="status1" {{isset($category) ? (isset($category->category_id)? 'checked' : '') : 'checked'}}>
                            <label class="ms-2" for="status1">
                                {{trans('products_attributes_create.yes')}}
                            </label>
                        </div>
                    </div>
                    <div class="form-group mt-3 mb-0 parent-category-div">
                        <label for="description">{{trans('products_categories_create.parent_category')}}</label>
                        <div class="categoryList" >
                        </div>
                        <input id="category_id" name="category_id" type="hidden" />
                           
                    </div>
                    <div class="form-group mt-40 mb-4">
                        <div class="d-flex justify-content-end">
                            <a href="{{route('categories.index')}}"
                                   class="btn btn-outline-danger" id="cancel-btn">
                                    {{trans('products_categories_create.cancel_btn')}}
                            </a>
                            <button type="submit" id="pro_cat_create_btn"
                                    class="btn btn-primary ms-2">
                                {{trans('products_categories_create.save_btn')}}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @push('footer_scripts')
        <script src="{{asset('treeselect/comboTreePlugin.js')}}" type="text/javascript"></script>
        <script src="{{ asset('treeSelectJs/treeselectjs.umd.js') }}" ></script>
        <script type="text/javascript">

            const options = @json($category_data)

            const treeselect = new Treeselect({
                parentHtmlContainer: document.querySelector('.categoryList'),
                options: options,
                //isGroupedValue: true,
                clearable: false,
                isIndependentNodes: true,
                isSingleSelect: true,
                value: [{{isset($category)?$category->category_id:""}}],
                searchable: false,
            })

            

            treeselect.srcElement.addEventListener('input', (e) => {
                $("#category_id").val(e.detail);
            })

            if (treeselect.value) {
                $("#category_id").val(treeselect.value);
            }


            var SampleJSONData = @json($category_data);
            var comboTree2;
            $(document).ready(function ($) {
                comboTree2 = $('#justAnotherInputBox').comboTree({
                    source: SampleJSONData,
                    isMultiple: false,
                    selected: ['{{isset($category)?$category->category_id:""}}']
                });
                $("#justAnotherInputBox").change(function () {
                    let id = comboTree2.getSelectedIds();
                    if( $("#justAnotherInputBox").val()){
                        $('#category_id').val(id);
                    }else {
                        $('#category_id').val(null);
                    }
                });

            });

            $(document).ready(function () {
                @if(isset($category) && isset($category->category_id))
                $('.parent-category-div').css('display','block')
                @elseif(isset($category) && !isset($category->category_id))
                $('.parent-category-div').css('display','none')
                @else
                $('.parent-category-div').css('display','block')
                @endif
            })

            $('.Assign-parent-category').on('change',function () {
                if (!($(this).is(":checked"))) {
                    $('#category_id').val(null);
                    $('.parent-category-div').css('display','none')
                }else{
                    $('.parent-category-div').css('display','block')
                }
            })

        </script>
    @endpush
@endsection
