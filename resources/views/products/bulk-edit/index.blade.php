@extends('layouts.app_new')

@section('Title', 'Bulk Edit')

@push('header_scripts')
@endpush

@section('content')
<div id="app" data-page="{{ json_encode($page) }}"></div>

@vite('resources/js/app.jsx')

<style>
    .container, .container-fluid, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl { padding: 0px !important; }
</style>

<script>
    var bulkeditdata = @json($bulk_edit);
    window.mappingData = bulkeditdata;
</script>


@endsection

@push('footer_scripts')
@endpush
