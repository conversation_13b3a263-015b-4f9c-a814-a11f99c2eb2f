<div class="row">
    <div class="col-12">
        <div class="mb-3 d-flex align-items-center p-2" style="background:#e7ecff;">
            <img class="border me-3 ps-2" src="{{ $product->files[0]->link ?? asset('img/apimio_default.jpg') }}"
                alt="{{ $product->id }}" style="max-width:40px">
            <h2 class="mt-2"> {{ $product->sku ?? "" }} </h2>
        </div>
        <div class="px-3">
            <div class="form-group form-check">
                            @if($variant->settings()->first() !== null)
                            <input type="checkbox" class="form-check-input mt-0 check_box_check"
                                id="track_id_{{$variant->id}}" data-variant-id="{{ $variant->id }}"
                                name="track_quantity[{{$variant->id}}]" style="cursor:pointer;opacity:0.7;"
                                {{$variant->settings()->first()->track_quantity == 1 ? 'checked' : ''}}>
                            @else
                            <input type="checkbox" class="form-check-input mt-0 check_box_check"
                                id="track_id_{{$variant->id}}" data-variant-id="{{ $variant->id }}"
                                name="track_quantity[{{$variant->id}}]" style="cursor:pointer;opacity:0.7;" checked>
                            @endif
                            <label class="form-check-label" for="exampleCheck1">Track quantity</label>
                        </div>
                        <div class="form-group form-check">
                            <input type="checkbox" class="form-check-input mt-0 check_box_check"
                                id="continue_id_{{$variant->id}}" name="continue_selling[{{$variant->id}}]"
                                data-variant-id="{{$variant->id}}" style="cursor:pointer;opacity:0.7;" {{ ( (
                                isset($variant->settings()->first()->continue_selling) &&
                            $variant->settings()->first()->continue_selling == 1) ? 'checked' : '' ) }}>
                            <label class="form-check-label" for="exampleCheck1">Continue selling when out of
                                stock</label>
                        </div>
        </div>

        <table class="table table-responsive">
            <thead>
                <tr>
                    <th>SKU</th>
                    <th>Location</th>
                    <th style="text-align: center;">Quantity</th>
                </tr>
            </thead>
            <tbody>
                @foreach($channel->channelWiseLocation as $location)
                <tr>
                    <td>{{ $product->sku ?? "" }}</td>
                    <td>{{ $location->location->name ?? "" }}</td>
                    <td style="display: flex; align-items: center; justify-content: center;">
                        @php
                        $available_quantity = $location->inventory->first() !== null ?
                        $location->inventory->first()->available_quantity : "";
                        @endphp
                        <input type="number" class="form-control form-control-sm w-25 quantity-adjustable"

                            oninput="this.value = Math.abs(this.value)" name="quantities[{{$location->id}}][{{ $variant->id }}]"
                            value="{{ $available_quantity ?? '' }}" data-variant-id="{{$variant->id}}" data-location-id="{{$location->id}}" data-product-id="{{$products->first()->id}}">
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

