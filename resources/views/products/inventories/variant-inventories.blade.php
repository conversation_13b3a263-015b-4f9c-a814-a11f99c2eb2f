<div class="col-12">
    <div class="p-3" style="background:#e7ecff;box-shadow:0px 6px 5px -6px rgba(0,0,0,0.2);position:relative">
        <h2>{{$products->first()->sku}}</h2>
    </div>
</div>
<div class="row p-4">
    <div class="col-3">
        <nav class="block">
            <div class="nav nav-tabs mb-3" style="max-height:400px;overflow:auto;" id="nav-tab" role="tablist">
                @foreach($variants as $index => $variant)
                <button class="nav-link text-start block col-12 border   {{ $index === 0 ? 'active ' : 'border-top-0
                ' }}" id="inventory-nav-{{ $channel->id }}{{ $variant->id }}" data-bs-toggle="tab"
                    data-bs-target="#inventory-{{ $channel->id }}{{ $variant->id }}" type="button" role="tab"
                    aria-controls="inventory-{{ $channel->id }}{{ $variant->id }}" aria-selected="true">
                    <img class="border me-3" src="{{ $variant->file->link ??  asset('img/apimio_default.jpg') }}"
                        alt="{{ $variant->id }}" style="max-width:30px">{{ $variant->name }}</button>
                @endforeach
            </div>
        </nav>
    </div>
    <div class="col-9">
        <div class="tab-content p-3 border bg-light" id="nav-tabContent" style="max-height:400px;overflow:auto;">
            @foreach($variants as $index => $variant)
            <div class="tab-pane fade {{ $index === 0 ? 'active show' : ' ' }}"
                id="inventory-{{ $channel->id }}{{ $variant->id }}" role="tabpanel"
                aria-labelledby="inventory-nav-{{ $channel->id }}{{ $variant->id }}">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group form-check">

                            @if($variant->settings()->first() !== null)

                            <input type="checkbox" class="form-check-input mt-0 check_box_check"
                                id="track_id_{{$variant->id}}" data-variant-id="{{ $variant->id }}"
                                name="track_quantity[{{$variant->id}}]" style="cursor:pointer;opacity:0.7;"
                                {{$variant->settings()->first()->track_quantity == 1 ? 'checked' : ''}}
                                data-variant-id="{{ $variant->id }}">
                            @else
                            <input type="checkbox" class="form-check-input mt-0 check_box_check"
                            id="track_id_{{$variant->id}}" data-variant-id="{{ $variant->id }}"
                                name="track_quantity[{{$variant->id}}]" style="cursor:pointer;opacity:0.7;" checked
                                data-variant-id="{{ $variant->id }}">
                            @endif
                            <label class="form-check-label" for="exampleCheck1">Track quantity</label>
                        </div>
                        <div class="form-group form-check">
                            <input type="checkbox" class="form-check-input mt-0 check_box_check"
                                id="continue_id_{{$variant->id}}" name="continue_selling[{{$variant->id}}]"
                                data-variant-id="{{$variant->id}}" style="cursor:pointer;opacity:0.7;" {{ ( (
                                isset($variant->settings()->first()->continue_selling) &&
                            $variant->settings()->first()->continue_selling == 1) ? 'checked' : '' ) }}
                                data-variant-id="{{ $variant->id }}">
                            <label class="form-check-label" for="exampleCheck1">Continue selling when out of
                                stock</label>
                        </div>
                    </div>
                </div>
                <table class="table table-responsive">
                    <thead>
                        <tr>
                            <th>SKU</th>
                            <th>Location</th>
                            <th style="text-align: center;">Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($channel->channelWiseLocation as $location)
                        <tr>
                            <td>{{ $variant->sku ?? "" }}</td>
                            <td>{{ $location->location->name ?? "" }}</td>
                            <td style="display: flex; align-items: center; justify-content: center;">
                                @php
                                $available_quantity = $location->inventory->where('variant_id' , $variant->id)->first()
                                !== null ? $location->inventory->where('variant_id' ,
                                $variant->id)->first()->available_quantity : "";
                                @endphp
                                <input type="number" class="form-control form-control-sm w-25 quantity-adjustable" 
                                    name="quantities[{{$location->id}}][{{ $variant->id }}]"
                                    oninput="this.value = Math.abs(this.value)" value="{{ $available_quantity ?? '' }}" data-variant-id="{{$variant->id}}" data-location-id="{{$location->id}}" data-product-id="{{$products->first()->id}}">
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endforeach

        </div>
    </div>
</div>

