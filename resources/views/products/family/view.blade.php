@extends('layouts.app_new')
@section('titles','Attribute Set')
@section('content')
    <div>
        <x-products.page-title name="{{trans('products_family.page_title')}}" description="{{trans('products_family.page_description')}}" links="true" button="true">
            <x-slot name="addbutton">
                <a href="{{route('family.create')}}" id="add-family" class="btn btn-primary disabled-with-text">{{trans('products_family.add_attribute_set_btn')}}</a>
            </x-slot>
        </x-products.page-title>
        @if(count($data["family"]) > 0)
            <div class="row">
                <div class="col-12 col-md-6 col-xl-3">
                    <x-general.search-bar placeholder="{{trans('products_family.search_placeholder')}}"/>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <table class="table">
                        <thead>
                        <tr class="custom-border-bottom-css">
                            <th scope="col">
                                Name
                            </th>
                            <th scope="col">
                                Attributes
                            </th>
                            <th scope="col" class="text-end">
                                Actions
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($data["family"] as $family)
                            <tr>
                                <td>
                                    {{substr($family->name,0,50)}}
                                </td>
                                <td>
                                    @foreach($family->attributes as $attribute)
                                        {{$attribute->name}},
                                        @if($loop->iteration > 4)
                                            @break
                                        @endif
                                    @endforeach
                                </td>
                                <td class="text-end">
                                    @if(!$family->is_default)
                                        <a href="{{route('family.edit',$family->id)}}"
                                           class=" mr-3 text-decoration-none">
                                            <i class="fa-regular fa-pen-to-square fs-20"></i>
                                        </a>

                                        <a href="#" id="modal" data-id="{{$family->id}}" data-retailer-name=""
                                           data-bs-toggle="modal" data-bs-target="#delete-modal-{{$family->id}}" class="text-decoration-none btn-delete">
                                            <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                        </a>
                                    @endif

                                </td>
                                <x-assets.delete-modal id="{{$family->id}}" text="Are you sure you want to delete attribute set?" button="Delete Attribute Set" title="Delete Attribute Set" url="{{route('family.destroy',$family->id)}}" type="family"/>
                            </tr>
                        @endforeach

                        </tbody>

                    </table>
                    {!! $data["family"]->appends($request->all())->links() !!}

                </div>
            </div>
        @else
            <x-general.empty-page description="{{trans('products.empty_table_description')}}"/>
        @endif

    </div>

@endsection
@push('footer_scripts')
    <script type="text/javascript">
        @error('name')
        $('#add-modal').modal('show')
        @enderror
    </script>
@endpush
