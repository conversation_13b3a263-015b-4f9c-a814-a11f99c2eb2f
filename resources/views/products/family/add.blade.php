<?php ?>
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Attribute Set')
@else
    @section('titles','Add Attribute Set')
@endif
@section('content')
    {{--  old code--}}
    <div>
        {{--    <div>--}}
        {{--        <x-products.add-page-title name="{{trans('products_family.page_title')}}" type="false" :routes="route('family.index')"/>--}}

        {{--        <hr class="mt-0 mb-4 divider">--}}

        {{--        <form id="pro_fam_create_form" method="POST" action="{{isset($family) ? route('family.update',$family->id) : route('family.store')}}">--}}
        {{--            @csrf--}}
        {{--            @if(isset($family))--}}
        {{--                @method('PUT')--}}
        {{--                <input type="hidden" name="id" value="{{$family->id}}">--}}
        {{--            @endif--}}
        {{--            <div class="row">--}}
        {{--                <div class="col-12 col-md-9 col-lg-9 col-xl-6">--}}

        {{--                    <div class="form-group mt-4">--}}
        {{--                        <label for="name" >{{trans('products_family_create.attribute_set_name')}}&nbsp;<span class="text-danger-light">*</span></label>--}}
        {{--                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name"--}}
        {{--                               name="name" value="{{ isset($family->name) ? $family->name : old('name') }}" autofocus>--}}
        {{--                        @error('name')--}}
        {{--                        <span class="text-danger">--}}
        {{--                        <small>{{$message}}</small>--}}
        {{--                        </span>--}}
        {{--                        @enderror--}}
        {{--                    </div>--}}
        {{--                    @if(count($attributes) > 0)--}}
        {{--                        <div class="form-group mt-4">--}}
        {{--                            <label for="list-of-attributes">{{trans('products_family_create.attributes')}}&nbsp;--}}{{--<span class="text-danger-light">*</span>--}}{{--</label>--}}
        {{--                            <select placeholder="Select Attributes" class="form-control sumoselect @error('attribute_ids') is-invalid @enderror" id="list-of-attributes"--}}
        {{--                                    name="attribute_ids[]" multiple="multiple" style="height: 38px" >--}}
        {{--                                @foreach($attributes as $key => $attribute)--}}
        {{--                                    <option value="{{ $attribute->id }}"--}}
        {{--                                            {{ isset($family->attributes) && count($family->attributes) > 0 ? (in_array($attribute->id, $family->attributes) ? "selected" : null) :--}}
        {{--                                                (old('attribute_ids') ? (in_array($attribute->id, old('attribute_ids')) ? "selected" : null) : null)  }} id="{{$key}}">--}}
        {{--                                        {{ $attribute->name }}--}}
        {{--                                    </option>--}}
        {{--                                @endforeach--}}
        {{--                            </select>--}}
        {{--                            @error('attribute_ids')--}}
        {{--                            <span class="text-danger">--}}
        {{--                        <small>{{$message}}</small>--}}
        {{--                        </span>--}}
        {{--                            @enderror--}}
        {{--                        </div>--}}
        {{--                    @endif--}}
        {{--                    <div class="form-group my-4">--}}
        {{--                        <div class="d-flex flex-row-reverse">--}}
        {{--                            <div class="p-2" style="width: 159px">--}}
        {{--                                <button type="submit" id="pro_fam_create_btn"--}}
        {{--                                        class="form-control btn btn-primary ripplelink">--}}
        {{--                                    {{trans('products_family_create.save_btn')}}--}}

        {{--                                </button>--}}
        {{--                            </div>--}}
        {{--                            <div class="p-2" style="width: 159px">--}}
        {{--                                <a href="{{route('family.index')}}"--}}
        {{--                                   class="form-control btn btn-outline-primary hovereffect ripplelink" id="cancel-btn">--}}
        {{--                                    {{trans('products_family_create.cancel_btn')}}--}}

        {{--                                </a>--}}
        {{--                            </div>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </form>--}}
        {{--    </div>--}}
    </div>

    {{-- new code attribute set start--}}
    <x-products.add-page-title name="{{trans('products_family.page_title')}}" type="false" :routes="route('family.index')"/>
    <div class="row mt-4">
        <form id="pro_fam_create_form" method="POST" action="{{isset($family) ? route('family.update',$family->id) : route('family.store')}}" class="formStyle">
            @csrf
            @if(isset($family))
                @method('PUT')
                <input type="hidden" name="id" value="{{$family->id}}">
            @endif
            <div class="col-12 col-xl-5">
                <div class="form-group">
                    <label for="name" >{{trans('products_family_create.attribute_set_name')}}&nbsp;<span class="text-danger">*</span></label>
                    <input type="text" class="form-control bg-white @error('name') is-invalid @enderror" id="name"
                           name="name" value="{{ isset($family->name) ? $family->name : old('name') }}" autofocus>
                    @error('name')
                    <span class="text-danger">
                        <small>{{$message}}</small>
                        </span>
                    @enderror
                </div>
            </div>
            <div class="col-12 col-xl-5">
                @if(count($attributes) > 0)
                    <div class="form-group mt-4">
                        <label for="list-of-attributes">{{trans('products_family_create.attributes')}}&nbsp;{{--<span class="text-danger-light">*</span>--}}</label>
                        <select placeholder="Select Attributes" class="form-control bg-white-smoke sumoselect @error('attribute_ids') is-invalid @enderror" id="list-of-attributes"
                                name="attribute_ids[]" multiple="multiple" style="height: 38px" >
                            @foreach($attributes as $key => $attribute)
                                <option value="{{ $attribute->id }}"
                                        {{ isset($family->attributes) && count($family->attributes) > 0 ? (in_array($attribute->id, $family->attributes) ? "selected" : null) :
                                            (old('attribute_ids') ? (in_array($attribute->id, old('attribute_ids')) ? "selected" : null) : null)  }} id="{{$key}}">
                                    {{ $attribute->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('attribute_ids')
                        <span class="text-danger">
                        <small>{{$message}}</small>
                        </span>
                        @enderror
                    </div>
                @endif
            </div>
            <div class="col-12 col-xl-5">
                <div class="form-group mb-3 mt-40">
                    <div class="d-flex justify-content-end">
                        <a href="{{route('family.index')}}"
                           class="btn btn-outline-danger" id="cancel-btn">
                            {{trans('products_family_create.cancel_btn')}}

                        </a>
                        <button type="submit" id="pro_fam_create_btn"
                                class="btn btn-primary ms-2">
                            {{trans('products_family_create.save_btn')}}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    {{-- new code attribute set end--}}
    @push('footer_scripts')
        <script src="{{ asset('assets/js/add-attribute.js') }}"></script>
    @endpush
@endsection
