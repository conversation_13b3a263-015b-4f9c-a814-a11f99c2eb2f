<?php ?>
@extends('layouts.app')
@section('titles','Media Library')
@section('content')

    <div>

        <x-products.page-title name="Media Library" description="Create a brand once, assign as many times as you like."
                               links="false" button="false">

        </x-products.page-title>

        <div class="card border-radius shadow-none mb-0">
            <div class="card-body pt-0">
                <div class="row">
                    <div class="col-12 col-lg-9 col-xl-10"></div>
                    <div class="col-2">
                            <label for="" class="Roboto bold text-dark float-left">{{__("View")}}</label>
                            <div class="dropdown">
                                <a class="btn btn-outline-primary ripplelink Poppins semibold dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                                   data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                   style="width: 160px">
                                    {{__("All")}}
                                </a>

                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <a class="dropdown-item Roboto regular" href="#">{{("Product Images")}}</a>
                                    <a class="dropdown-item Roboto regular" href="#">{{__("Social Images")}}</a>
                                    <a class="dropdown-item Roboto regular" href="#">{{__("Files")}}</a>
                                </div>
                            </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="card border-radius shadow-none">
            <div class="card-body pt-1">
                <div class="row">
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;" alt="empty">
                        </div>
                    </div>
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;"  alt="empty">
                        </div>
                    </div>
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;"  alt="empty">
                        </div>
                    </div>
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;"  alt="empty">
                        </div>
                    </div>
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;"  alt="empty">
                        </div>
                    </div>
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;"  alt="empty">
                        </div>
                    </div>
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 p-2">
                        <div class="border-radius image-grid">
                            <img src="" width="100%" class="border-radius" style="max-height: 185px;"  alt="empty">
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>


@endsection
@push('footer_scripts')

@endpush
