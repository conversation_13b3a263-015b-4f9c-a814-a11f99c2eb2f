<?php ?>
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Brand')
@else
    @section('titles','Add Brands')
@endif
@section('content')
    <div>

        <x-products.add-page-title name="{{trans('products_brands.page_title')}}" type="false" :routes="route('brands.index')"/>
        <div class="row mt-4">
            <div class="col-12 col-md-9 col-lg-9 col-xl-5">
                <form id="pro_bran_create_form" class="formStyle"
                      action="{{isset($brand) ? route('brands.update',$brand->id) : route('brands.store')}}"
                      method="POST">
                    @csrf
                    @if(isset($brand))
                        @method('PUT')
                        <input type="hidden" name="id" value="{{$brand->id}}">
                    @endif
                    <div class="form-group">
                        <label for="name">{{trans('products_brands_create.brand_name')}}&nbsp;
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               class="form-control @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{isset($brand) ? $brand->name : old('name')}}" autofocus>
                        @error('name')
                            <span class="text-danger">
                            <small>{{$message}}</small>
                            </span>
                        @enderror
                    </div>
                    <div class="form-group mb-4 mt-40">
                        <div class="d-flex justify-content-end">
                            <a href="{{route('brands.index')}}" class="btn btn-outline-danger" id="cancel-btn">
                                    {{trans('products_brands_create.cancel_btn')}}
                            </a>
                            <button type="submit" id="pro_bran_create_btn" class="btn btn-primary ms-2">
                                {{trans('products_brands_create.save_btn')}}
                            </button>

                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
@endpush
