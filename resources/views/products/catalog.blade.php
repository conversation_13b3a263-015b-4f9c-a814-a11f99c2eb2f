@extends('layouts.app_new')

@if(request()->has('id'))
    @section('titles','Edit Products')
@else
    @section('titles','Add Product')
@endif
@section('content')


    <form method="post" action="{{route('shopify.bulk.sync')}}" id="save_form">
        @csrf
        <input name="channel_id" id="channel_id" type="hidden">
        <input name="product_id" id="product_id" type="hidden">
        <input name="template_id[]" id="template_id" type="hidden">
    </form>

    <x-products.edit-product-base-form :product="$product" :version="$version">
        <x-products.edit-product-header :product="$product" :buttons="false" :version="$version"/>
        <x-products.edit-product-header-navs :product="$product" :version="$version"/>


        <div class="row">
            <div class="col-12">
                <table class="table">
                    <thead>
                    <tr>
                        <th scope="col">Store name</th>
                        <th scope="col">{{__("Language")}}</th>
                        <th scope="col" class="text-center">Status</th>
                        @can('sync_product_to_shopify' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
                            <th scope="col" class="text-end">Sync product</th>
                        @endcan
                    </tr>
                    </thead>
                    <tbody>
                        @foreach($product->channels as $channel)
                            <tr>
                                <td class="text-capitalize">{{$channel->name}}</td>
                                <td>
                                    @foreach($channel->versions()->get() as $version)
                                        {{$version->name}}
                                    @endforeach
                                </td>
                                <td class="text-center">
                                    @php
                                        $status = $channel->syncAction($channel->id,$product);
                                    @endphp
                                    @if( $status == 'sync'  )
                                        <span class="status status-synced"> {{trans('products_catalog.synced_badge')}} </span>
                                    @elseif($status == 'updated' || $status == 'publish' || $status == 'draft'  )
                                        <span class="status status-publish"> {{trans('products_catalog.update_badge')}} </span>
                                    @elseif($status == "in_queue")
                                        <span class="status status-warning">In Process</span>
                                    @else
                                        <span class="status status-publish">{{$status}}</span>
                                    @endif

                                </td>
                                @can('sync_product_to_shopify' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
                                    <td class="text-end">
                                        @if($channel->isShopifyLinked($channel->id))
                                            @php
                                                $status = $channel->syncAction($channel->id,$product);
                                            @endphp
                                            @if( $status == 'sync'  )
                                                <span class="btn btn-sm btn-success">{{trans('products_catalog.synced_badge')}}</span>
                                            @elseif($status == 'updated' || $status == 'publish' || $status == 'draft'  )
                                              @if(Gate::allows('primeOrPaidUser'))
                                                <input type="hidden"
                                                       value="{{\App\Classes\Mapping\Conversion::getTemplates(['channel_id'=>$channel->id,'type'=>'shopify'])->first()->id??null}}"
                                                       id="mapping_{{$channel->id}}">
                                                <span class="btn btn-sm btn-primary"
                                                        onclick=" syncForm('{{$product->id}}','{{$channel->id}}')">
                                                    {{trans('products_catalog.update_badge')}}
                                                </span>
                                                @else
                                                <a class="btn btn-sm btn-primary " style="display: inline-flex; align-items: center; justify-content: center;" data-bs-toggle="tooltip" data-bs-placement="right" title="Syncing products feature is available for paid plans only. Upgrade to a paid plan to enable this feature.">{{trans('products_catalog.update_badge')}} <i class="fa fa-lock text-white mt-1 ms-2 mb-1"></i></a>
                                                @endif
                                            @elseif($status == "in_queue")
                                                <span class="btn btn-sm btn-warning">In Process</span>
                                            @else
                                                <span class="btn btn-sm btn-primary ">
                                                    {{$status}}
                                                </span>
                                            @endif
                                        @else
                                            <a class="btn btn-sm btn-success text-decoration-none" href="{{ route("shopify.show",$channel->id) }}">
                                                {{trans('products_catalog.connect_shopify_btn')}}
                                            </a>
                                        @endif
                                    </td>
                                @endcan
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

        </div>
    </x-products.edit-product-base-form>
    @push('footer_scripts')

        <script src="{{asset('js/delete_product.js')}}" ></script>

        <script>
            function  syncForm(product_id, channel_id){
                $('#product_id').val(product_id);
                $('#channel_id').val(channel_id);
                let template_id = $('#mapping_'+channel_id).val();
                $('#template_id').val(template_id);
                $('#save_form').submit();
            }
        </script>


    @endpush
@endsection
