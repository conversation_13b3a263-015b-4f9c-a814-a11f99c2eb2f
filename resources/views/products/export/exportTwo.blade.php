<?php ?>
@extends('layouts.app_new')
@section('titles','Export Products')
@section('content')
    @push('header_scripts')

        <link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet" />
        <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css" rel="stylesheet">

        <style>
            .line {
                width: 80%;
                top: 24px;
                position: absolute;
                border-bottom: 1px solid #e9e6e6;
            }
            input[type="file"] {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0,0,0,0);
                border: 0;
            }


        </style>
    @endpush

    <div>
        <x-products.page-title name="{{trans('products_export_step1.page_title')}}" description="{{trans('products_export_step1.page_description')}}" links="false" button="false" buttonname="null"/>

        <div class="card border-radius shadow-none mb-5">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-around">
                            <div class="line"></div>
                            <!--Tick-->
                            <div id="greenOne" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-success text-center Roboto">{{__("✔")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step1_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_export_step1.step1_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--blue2-->
                            <div id="blueTwo" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-primary text-center Roboto">{{__("2")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step2_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_export_step1.step2_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--grey3-->
                            <div id="greyThree" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-outline-primary text-center Roboto">{{__("3")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step3_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_export_step1.step2_description')}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <form action="{{ route("export.exportThree") }}" method="post">
            @csrf
            <input type="hidden" id="filter-query-export-two" name="filter_query" value="{{$filter_query}}">

            @if(count($templates))
                <x-products.import-template :templates="$templates" />
                <div class="row">
                    <div class="col-lg-6 mx-auto text-center">
                        <h3 class="font-20 Poppins semibold m-0 text-dark">{{trans('products_export_step2.or')}}</h3>
                    </div>
                </div>
            @endif

            <div class="container">
                <div class="row">
                    <div class="col-lg-6 mx-auto">
                        <div class="d-flex flex-row mt-4">
                            <div class="card border-radius shadow-none border-color card-template mr-3 mx-auto">
                                <button class="btn card-body cursor-pointer" id="create-template" name="create_template" value="create">
                                <span class="circle circle-primary text-center Roboto">
                                    <i class="flaticon flaticon-add d-flex justify-content-center icon-size" style="padding-top: 1px"></i>
                                </span>
                                    <br>
                                    <label class="mt-3">{{trans('products_export_step2.create_new_template')}}</label>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="d-flex flex-row-reverse mt-5">
                        <div class="p-2" style="width: 159px">
                            <button type="submit" id="next-btn"
                                    class="form-control btn btn-primary ripplelink">
                                {{trans('products_export_step1.next_btn')}}
                            </button>
                        </div>
                        <div class="p-2" style="width: 159px">
                            <a href="{{route('products.index')}}" id="cancel-btn"
                               class="form-control btn btn-outline-primary hovereffect ripplelink">
                                {{trans('products_export_step1.cancel_btn')}}

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('footer_scripts')
    <script>
        $(document).ready(function () {

            /*
            * Open product popup if their is any exception
            * */
            @error("sku")
            $("#create_product").modal("show");
            @enderror
            @error("name")
            $("#create_product").modal("show");
            @enderror


            $('#AttributeSet').SumoSelect({
                placeholder: 'Attribute Set',
                csvDispCount: 1,
                captionFormat: 'Attribute Set ({0})',
                captionFormatAllSelected: 'Attribute Set ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Vendor').SumoSelect({
                placeholder: 'Vendor',
                csvDispCount: 1,
                captionFormat: 'Vendor ({0})',
                captionFormatAllSelected: 'Vendor ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Brand').SumoSelect({
                placeholder: 'Brand',
                csvDispCount: 1,
                captionFormat: 'Brand ({0})',
                captionFormatAllSelected: 'Brand ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Category').SumoSelect({
                placeholder: 'Category',
                csvDispCount: 1,
                captionFormat: 'Category ({0})',
                captionFormatAllSelected: 'Category ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Channel').SumoSelect({
                placeholder: 'Channel',
                csvDispCount: 1,
                captionFormat: 'Channel ({0})',
                captionFormatAllSelected: 'Channel ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Status').SumoSelect({
                placeholder: 'Status',
                csvDispCount: 1,
                captionFormat: 'Status ({0})',
                captionFormatAllSelected: 'Status ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
        });
    </script>
@endpush
