<?php ?>
@extends('layouts.app_new')
@section('titles','Export Products')
@section('content')
    @push('header_scripts')

        <link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet" />
        <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css" rel="stylesheet">

        <style>
            .line {
                width: 80%;
                top: 24px;
                position: absolute;
                border-bottom: 1px solid #e9e6e6;
            }
            input[type="file"] {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0,0,0,0);
                border: 0;
            }


        </style>
    @endpush

    <div>
        <x-products.page-title name="{{trans('products_export_step1.page_title')}}" description="{{trans('products_export_step1.page_description')}}" links="false" button="false" buttonname="null"/>

        <div class="card border-radius shadow-none mb-5">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-around">
                            <div class="line"></div>
                            <!--Tick-->
                            <div id="greenOne" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-success text-center Roboto">{{__("✔")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step1_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_export_step1.step1_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <div id="blueTwo" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-success text-center Roboto">{{__("✔")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step2_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_export_step1.step2_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--grey3-->
                            <div id="greyThree" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-primary text-center Roboto">{{__("3")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step3_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_export_step1.step3_description')}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

{{--        <div class="container mt-2">
            <div class="row">


                <div class="col-lg-6 mx-auto text-center">

                    <div class="p-2">
                        <div class="form-group">
                            <label for="">{{__("Please choose Export CSV Variants Type")}}</label>
                            <select form="pro_exp_form" name="nodes[variant][variant_type]" style="width: 282px"
                                    class="form-control m-auto @error('variant') is-invalid @enderror">
                                <option value="" class="Poppins regular text-color">Select Column
                                </option>
                                @foreach($export_type as $type_key => $type_value)
                                    @if(isset($template['temp_create']))
                                        <option value="{{$type_key}}" {{($type_key == $template['temp_create']) ? 'selected' : null}}>{{$type_value}}</option>
                                    @else
                                        @if(isset($template['variant']['variant_type']))
                                            <option value="{{$type_key}}" {{($template['variant']['variant_type'] == $type_key) ? 'selected' : null}}>{{$type_value}}</option>
                                        @else
                                            <option value="{{$type_key}}">{{$type_value}}</option>
                                        @endif
                                    @endif
                                @endforeach

                            </select>
                        </div>
                        @error('variant')
                        <div class="text-danger">
                            {{$message}}
                        </div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <hr class="mt-1 mb-4 divider">--}}


        <form id="pro_exp_form" method="POST" action="{{ route("products.export") }}">
            @csrf
            <input type="hidden" id="filter-query-export-three" name="filter_query" value="{{$filter_query}}">
            <input type="hidden" name="template_method_type" value="export">
            @if(isset($template['temp_create']))
                <input type="hidden" name="nodes[temp_create]" value="{{$template['temp_create']}}">
            @endif
            <div class="card border-radius shadow-none">
                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">

                    <li class="nav-item">
                        <a class="nav-link text-dark active Roboto bold" id="product-tab" data-toggle="tab" href="#product"
                           role="tab" aria-controls="profile" aria-selected="false">
                            {{trans('products_export_step3.product_row_mapping')}}
                        </a>
                    </li>
                </ul>
                <div class="tab-content" id="myTabContent">

                    <!--products tab panel-->
                    <div class="tab-pane fade show active" id="product" role="tabpanel" aria-labelledby="profile-tab">
                        <div id="row_container" class="card-body p-0">
                            <div style="">
                                {{--dynamic rows fetch from ajax call--}}

                                <div id="add_row"></div>
                            </div>
                        </div>
                        <div id="">
                            <div class="d-flex justify-content-start">
                                <div class="p-2">
                                    <button id="add_operation" type="button" class="btn btn-primary-tertiary">
                                        <div class="media">
                                            <img src="{{asset('media/new-flow/add another.png')}}" class="mr-1" alt="">
                                            <div class="media-body">
                                                <h5 class="m-0 p-2 Roboto regular">{{trans('products_export_step3.add_operation')}}</h5>
                                            </div>
                                        </div>

                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>

               {{--                 @dd($template['variant'])
                <!--variants tab panel-->
                                        <div class="tab-pane fade" id="variant" role="tabpanel" aria-labelledby="contact-tab">
                                            <div>
                                                <div>
                                                    <div class="d-flex justify-content-start">
                                                        <div class="p-2">
                                                            <div class="form-group">
                                                                <label for="">{{__("Choose Export Type")}}</label>
                                                                <select name="nodes[variant][variant_type]" style="width: 182px"
                                                                        class="form-control @error('variant') is-invalid @enderror">
                                                                    <option value="" class="Poppins regular text-color">Select Column
                                                                    </option>
                                                                    @foreach($export_type as $type_key => $type_value)
                                                                        @if(isset($template['temp_create']))
                                                                            <option value="{{$type_key}}" {{($type_key == $template['temp_create']) ? 'selected' : null}}>{{$type_value}}</option>
                                                                        @else
                                                                            @if(isset($template['variant']['variant_type']))
                                                                                <option value="{{$type_key}}" {{($template['variant']['variant_type'] == $type_key) ? 'selected' : null}}>{{$type_value}}</option>
                                                                            @else
                                                                                <option value="{{$type_key}}">{{$type_value}}</option>
                                                                            @endif
                                                                        @endif
                                                                    @endforeach

                                                                </select>
                                                            </div>
                                                            @error('variant')
                                                            <div class="text-danger">
                                                                {{$message}}
                                                            </div>
                                                            @enderror
                                                        </div>
                                                        <div class="p-2">
                                                            <div class="form-group">
                                                                <label for="">{{__("Variant options 1")}}</label>
                                                                <select name="nodes[variant][variant_options][]" style="width: 182px"
                                                                        class="form-control">
                                                                    <option value="" class="Poppins regular text-color">Select Column</option>
                                                                    <optgroup label="Apimio Variants">
                                                                        @foreach($variant_attributes as $var_attr)
                                                                            @if(isset($template['variant']['variant_options'][0]))
                                                                                <option value="{{$var_attr->handle}}" {{($template['variant']['variant_options'][0] == $var_attr->handle || $template['variant']['variant_options'][0] == $var_attr->name) ? 'selected' : null}}>{{$var_attr->name}}</option>
                                                                            @else
                                                                                <option value="{{$var_attr->handle}}">{{$var_attr->name}}</option>

                                                                            @endif
                                                                        @endforeach
                                                                    </optgroup>
                                                                    <optgroup label="Default">
                                                                        @foreach($apimio_attributes as $ap_attr_key => $ap_attr)
                                                                            @if(isset($template['variant']['variant_options'][0]))
                                                                                <option value="{{$ap_attr_key}}" {{($template['variant']['variant_options'][0] == $ap_attr_key || $template['variant']['variant_options'][0] == $ap_attr) ? 'selected' : null}} >{{$ap_attr}}</option>
                                                                            @else
                                                                                <option value="{{$ap_attr_key}}">{{$ap_attr}}</option>
                                                                            @endif
                                                                        @endforeach
                                                                    </optgroup>

                                                                    @foreach($heading_attributes as $family)

                                                                        <optgroup label="{{$family->name}}">
                                                                            @foreach($family->attributes as $h_attribute)
                                                                                @if(isset($template['variant']['variant_options'][0]))
                                                                                    <option value="{{$h_attribute->pivotId}}" {{($template['variant']['variant_options'][0] == $h_attribute->pivotId || $template['variant']['variant_options'][0] == $h_attribute->name) ? 'selected' : null}}>{{$h_attribute->name}}</option>
                                                                                @else
                                                                                    <option value="{{$h_attribute->pivotId}}">{{$h_attribute->name}}</option>
                                                                                @endif
                                                                            @endforeach
                                                                        </optgroup>

                                                                    @endforeach
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="p-2">
                                                            <div class="form-group">
                                                                <label for="">{{__("Variant options 2")}}</label>
                                                                <select name="nodes[variant][variant_options][]" style="width: 182px"
                                                                        class="form-control ">
                                                                    <option value="" class="Poppins regular text-color">Select Column</option>
                                                                    <optgroup label="Apimio Variants">
                                                                        @foreach($variant_attributes as $var_attr)
                                                                            @if(isset($template['variant']['variant_options'][1]))
                                                                                <option value="{{$var_attr->handle}}" {{($template['variant']['variant_options'][1] == $var_attr->handle || $template['variant']['variant_options'][1] == $var_attr->name) ? 'selected' : null}}>{{$var_attr->name}}</option>
                                                                            @else
                                                                                <option value="{{$var_attr->handle}}">{{$var_attr->name}}</option>

                                                                            @endif
                                                                        @endforeach
                                                                    </optgroup>
                                                                    <optgroup label="Default">
                                                                        @foreach($apimio_attributes as $ap_attr_key => $ap_attr)
                                                                            @if(isset($template['variant']['variant_options'][1]))
                                                                                <option value="{{$ap_attr_key}}" {{($template['variant']['variant_options'][1] == $ap_attr_key || $template['variant']['variant_options'][1] == $ap_attr) ? 'selected' : null}} >{{$ap_attr}}</option>
                                                                            @else
                                                                                <option value="{{$ap_attr_key}}">{{$ap_attr}}</option>
                                                                            @endif
                                                                        @endforeach
                                                                    </optgroup>

                                                                    @foreach($heading_attributes as $family)

                                                                        <optgroup label="{{$family->name}}">
                                                                            @foreach($family->attributes as $h_attribute)
                                                                                @if(isset($template['variant']['variant_options'][1]))
                                                                                    <option value="{{$h_attribute->pivotId}}" {{($template['variant']['variant_options'][1] == $h_attribute->pivotId || $template['variant']['variant_options'][1] == $h_attribute->name) ? 'selected' : null}}>{{$h_attribute->name}}</option>
                                                                                @else
                                                                                    <option value="{{$h_attribute->pivotId}}">{{$h_attribute->name}}</option>
                                                                                @endif
                                                                            @endforeach
                                                                        </optgroup>

                                                                    @endforeach
                                                                </select>
                                                            </div>

                                                        </div>
                                                        <div class="p-2">
                                                            <div class="form-group">
                                                                <label for="">{{__("Variant options 3")}}</label>
                                                                <select name="nodes[variant][variant_options][]" style="width: 182px"
                                                                        class="form-control ">
                                                                    <option value="" class="Poppins regular text-color">Select Column</option>
                                                                    <optgroup label="Apimio Variants">
                                                                        @foreach($variant_attributes as $var_attr)
                                                                            @if(isset($template['variant']['variant_options'][2]))
                                                                                <option value="{{$var_attr->handle}}" {{($template['variant']['variant_options'][2] == $var_attr->handle || $template['variant']['variant_options'][2] == $var_attr->name) ? 'selected' : null}}>{{$var_attr->name}}</option>
                                                                            @else
                                                                                <option value="{{$var_attr->handle}}">{{$var_attr->name}}</option>

                                                                            @endif
                                                                        @endforeach
                                                                    </optgroup>
                                                                    <optgroup label="Default">
                                                                        @foreach($apimio_attributes as $ap_attr_key => $ap_attr)
                                                                            @if(isset($template['variant']['variant_options'][2]))
                                                                                <option value="{{$ap_attr_key}}" {{($template['variant']['variant_options'][2] == $ap_attr_key || $template['variant']['variant_options'][2] == $ap_attr) ? 'selected' : null}} >{{$ap_attr}}</option>
                                                                            @else
                                                                                <option value="{{$ap_attr_key}}">{{$ap_attr}}</option>
                                                                            @endif
                                                                        @endforeach
                                                                    </optgroup>

                                                                    @foreach($heading_attributes as $family)

                                                                        <optgroup label="{{$family->name}}">
                                                                            @foreach($family->attributes as $h_attribute)
                                                                                @if(isset($template['variant']['variant_options'][2]))
                                                                                    <option value="{{$h_attribute->pivotId}}" {{($template['variant']['variant_options'][2] == $h_attribute->pivotId || $template['variant']['variant_options'][2] == $h_attribute->name) ? 'selected' : null}}>{{$h_attribute->name}}</option>
                                                                                @else
                                                                                    <option value="{{$h_attribute->pivotId}}">{{$h_attribute->name}}</option>
                                                                                @endif
                                                                            @endforeach
                                                                        </optgroup>

                                                                    @endforeach
                                                                </select>
                                                            </div>

                                                        </div>

                                                    </div>
                                                </div>

                                            </div>
                                        </div>--}}
                </div>
            </div>

            <!--Export CSV Modal -->
            <x-products.export-c-s-v  :templateAttributes="$template_attributes" />


            <div class="row">
                <div class="col-12">
                    <div class="d-flex flex-row-reverse mt-4">
                        <div class="p-2" style="width: 159px">
                            <button id="pro_export_btn" type="button"
                                    class="form-control btn btn-primary ripplelink">
                                {{trans('products_export_step1.next_btn')}}
                            </button>
                        </div>
                        <div class="p-2">
                            <a href="{{route('products.index')}}" id="cancel-btn"
                               class="px-5 btn btn-outline-primary hovereffect ripplelink" id="cancel-btn">
                                {{trans('products_export_step1.cancel_btn')}}

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('footer_scripts')

        <script>

            var global_row_count = 0;

            var row_node = '';

            var row_node_flag = false;


            /* LARAVEL META CSRF REQUIREMENT */
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            function delete_row(e)
            {
                ($(e).parents(':eq(4)').remove());
            }


            $('#add_operation').on('click',function(){
                // Ajax Call
                let row_count = global_row_count;
                global_row_count++;

                $.ajax({
                    url: "{{ route('export.add.row') }}",
                    method: "POST",
                    async : false,
                    data: {
                        heading_attributes: JSON.stringify({!! $heading_attributes !!}),
                        apimio_attributes: JSON.stringify({!! $apimio_attributes !!}),
                        formula : 'assign',
                        row_count : row_count,
                        row_node : (row_node_flag == true) ?
                            row_node : null

                    },
                    success: function (data) {


                        $('#add_row').append(data.output);

                        $('#product-listing-row').html(data);
                    },
                    error: function (error){
                        console.log(error);
                    }
                });

            });

            $(document).on('change','.formula_field',function(){
                let self = this;
                formula_row = $(self).parents(':eq(2)').find(".assign_formula");
                row_count = formula_row.data('count');
                var formula = $(this).val();

                // Ajax Call
                $.ajax({
                    url: "{{ route('export.fetch.formula.fields') }}",
                    method: "POST",
                    data: {
                        heading_attributes: JSON.stringify({!! $heading_attributes !!}),
                        apimio_attributes: JSON.stringify({!! $apimio_attributes !!}),
                        formula : formula,
                        row_count : row_count
                    },
                    success: function (data) {
                        formula_row.replaceWith(data.output);
                    },
                    error: function (error){
                        console.log(error);
                    }
                });


            });

            $(document).on('change','.apimio-column',function(){
                var apimio_text = $(this).find('option:selected').text();
                $(this).closest('.row').find('.export-column').val(apimio_text);
            });


            $(document).ready(function () {

                @php
                    if(empty($template['data'])){
                        $rows = 4;
                        $nodes = "";
                    }
                    else{
                        $rows = count($template['data']);
                    }


                @endphp
                    @for ($i = 0 ; $i < $rows; $i++)
                    @if(isset($template['data'][$i]))
                    row_node = @json($template['data'][$i]);
                @endif
                    row_node_flag = true;

                $( "#add_operation" ).promise().done(function() {
                    $("#add_operation").click();
                });

                @endfor

                    row_node_flag = false;

            });

            $( "#pro_export_btn" ).click(function(event) {
                event.preventDefault();
                passValidation = false;
                $($("#add_row").find(".apimio-column").get().reverse()).each(function (i, obj) {

                    if ($(obj).val() != "") {

                        obj.reportValidity();

                        passValidation = true;

                        return;
                    }
                });
                if (passValidation){
                    $('#export_product').modal('show');
                }
                else{
                    alert("Please select at-least one column for export csv.")
                }
            });


        </script>
    @endpush
@endsection
