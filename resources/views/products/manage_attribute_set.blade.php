@extends('layouts.app_new')

@section('titles','Update attribute sets')
@section('content')
    <link href="https://unpkg.com/gijgo@1.9.13/css/gijgo.min.css" rel="stylesheet" type="text/css"/>
    <x-products.edit-product-base-form :product="$product" :version="$version">
        <x-products.edit-product-header :product="$product" :version="$version"/>
        <x-products.edit-product-header-navs :product="$product" :version="$version"/>

        <div class="row">
            <div class="col-12 col-xl-8">
                <div class="row d-flex align-items-center">
                    <div class="col-12 col-md-8">
                        @if($product)
                            <input type="hidden" value="{{$product->status}}" name="status">
                        @endif
                        <div class="formStyle">
                            <label for="Family" class="ml-1">{{trans('products_attribute_set.attribute_set')}}</label>
                            <select
                                data-confirm-before-leave="true"
                                placeholder="Select..." id="family_ids" multiple="multiple" name="family"
                                class="form-control sumoselect @error('family') is-invalid @enderror">
                                @foreach($families as $family)
                                    <option value="{{$family->id}}"
                                            {{ in_array($family->id, $family_ids) ? "selected" : null }}
                                            class="Poppins regular text-color">{{ $family->name }}</option>
                                @endforeach
                            </select>
                            <div class="ml-1 mt-2">
                                <div class="row" id="div">
                                    <div class="col-4">
                                        <!--Selected Tags-->
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-md-4 pt-2">
                        <div class="mr-2">
                            <button id="assign_attribute_set" onclick="$( window).unbind( 'beforeunload' );"
                                    type="button"
                                    class="btn btn-outline-primary px-4 w-100">
                                {{trans('products_attribute_set.assign_attribute_set')}}
                            </button>
                        </div>
                    </div>
                </div>
                <div class="accordion" id="accordionExample">
                    <!-- family attribute card start-->
                    <div class="family_attribute">
                        @foreach($product_families as $family)
                            <x-products.edit-product-attribute-set-widget :version="$version" :family="$family"
                                                                          :product="$product"/>
                        @endforeach
                    </div>
                    <!-- family attribute card ends-->
                </div>
            </div>
            <div class="col-12 col-xl-4">
                <div class="row">
                    <div class="col-12 d-none d-xl-block">
                        {{--dropdown with search--}}
                        <div class="dropdown float-md-right">
                             <span class="btn-outline dropdown-toggle dropdown-toggle-attribute" type="button"
                                        id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $current_version->name }}
                                </span>
                                <div class="dropdown-menu dropdown-menu-attribute" aria-labelledby="dropdownMenuButton">
                                    @foreach($versions as $version)
                                        <a class="dropdown-item"
                                           href="{{ route("products.edit", ["id" => $product->id, "version_id" => $version->id]) }}">{{ $version->name }}
                                            @foreach($product_versions as $product_version)
                                                @if($product_version->id == $version->id)
                                                    <span class="text-success ms-4"><i class="fas fa-check"></i></span>
                                                @endif
                                            @endforeach
                                        </a>
                                    @endforeach
                            </div>
                        </div>
                    </div>
                </div>


                <x-completeness-product-fields-scoring :product="$product" :version="$current_version"/>
                <x-products.edit-product-selection-widget :product="$product"/>
                <x-products.edit-product-footer-btns/>
            </div>
        </div>
    </x-products.edit-product-base-form>
@endsection
@push('footer_scripts')
    <script>
        $(document).ready(function () {
            var add_button = $(".add_field_button");
            var x = 0;
            var btn_ref = 1;

            // trigger for button to clone input field.
            $(document).on("click", ".add_field_button", function (e) {
                //let add_new_btn = $(this).clone();
                //x++;
                let input_cloned = $(this).parent().find(`.clone_input_field`).clone(true);
                input_cloned.removeClass("clone_input_field");
                input_cloned.addClass("input-width-css");
                input_cloned.find(`input`).val("");
                input_cloned.find(`.custom-attribute-input-css`).val('{{$version->currency}}');
                input_cloned.val('');
                let dd = $("<div class='mt-3 position-relative parent-div'></div>");
                dd.append("<button type='button' class='remove_field'><i class='trash-button fa fa-trash-o' aria-hidden='true'></i></button>");
                $(this).parent().children().first().append(dd.append(input_cloned));

            });

            // remove the html after triger this function.
            $(document).on("click", ".remove_field", function (e) {
                e.preventDefault();
                const data_length = this.parentNode.previousElementSibling.querySelectorAll(".form-control").length;
                const data = this.parentNode.previousElementSibling.querySelectorAll("input,.form-control,.input-custom-css")[1];
                const data1 = this.parentNode.previousElementSibling.querySelectorAll(".form-control,.clone_input_field")[0];
                let datakey = $(this).parent().remove();
                if (data_length > 0 && data_length < 2) {
                    input_status1(data1);
                } else if (data_length > 1) {
                    input_status1(data);
                } else {
                    input_status1(data);
                }


            })
        });

    </script>


    <script src="{{asset('js/delete_product.js')}}"></script>
    <script>
        $("#assign_attribute_set").click(function () {
            let family_ids = $("#family_ids").val();
            $(document).ready(function () {
                $('<form action="" method="GET"><input type="hidden" name="family_ids" value="' + family_ids + '"></form>').appendTo('body').submit();
            });
        });

        /**
         * Input status change
         * */
       

        /**
        
        /**
         * Trigger input status change on page refresh | page load
         * */
        // $(document).ready(function () {
        //     const productEl111 = document.querySelectorAll('.fa-exclamation-circle');
        //     productEl111.forEach(function(element) {
        //         var popover111 = new bootstrap.Popover(element, {
        //             content: function() {
        //                 return "This is the dynamic content";
        //             }
        //         });
        //     });
        // });

        /**
         * Popover trigger
         * */
        /**
         * Popover trigger
         * */
        const productEl = document.querySelectorAll('.circle-sm');
        productEl.forEach(function(element) {
            var popover = new bootstrap.Popover(element, {
                content: function() {
                    return "This is the dynamic content";
                }
            });
        });

        function unAssign(id) {

            var options = $('#family_ids option');
            if (options.val() == id) {
                console.log(options);
                options.attr('selected', false);
            }

            // var values = $.map(options, function (option) {
            //     if (option.selected && (option.value == id)) {
            //         return option.removeAttribute('selected');
            //     }
            //     console.log(option)
            // });
        }
    </script>
@endpush
