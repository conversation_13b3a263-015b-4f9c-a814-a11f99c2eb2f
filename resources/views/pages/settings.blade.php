<?php ?>
@extends('layouts.app_new')
@section('titles','Settings')
@section('content')
    <div>

        <x-products.page-title name="{{trans('settings.page_title')}}"
                               description="{{trans('settings.page_description')}}"
                               links="false" button="false">
        </x-products.page-title>
        <form action="{{ route('settings.update') }}" method="post" id="edit_profile_form" class="formStyle">
            @csrf
            <div class="row">
                <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                            <div class="form-group mb-3">
                                <label for="input-name">
                                    {{ trans('settings.first_name') }}
                                    &nbsp;<span class="text-danger-light">*</span>
                                </label>
                                <input type="text" name="fname"
                                       class="form-control @error('fname') is-invalid @enderror"
                                       placeholder="{{ __('First Name') }}"
                                       value="{{ __(\Illuminate\Support\Facades\Auth::user()->fname) }}" autofocus>
                                @error('fname')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-xl-6 col-12 ">
                            <div class="form-group mb-3 {{ $errors->has('lname') ? ' has-danger' : '' }}">
                                <label for="input-name">
                                    {{ trans('settings.last_name') }}&nbsp;<span class="text-danger-light">*</span>
                                </label>
                                <input type="text" name="lname"
                                       class="form-control @error('lname') is-invalid @enderror"
                                       placeholder="{{ __('Last Name') }}"
                                       value="{{ __(\Illuminate\Support\Facades\Auth::user()->lname) }}">
                                @error('lname')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="formStyle mb-3">
                                <label for="input-name">
                                    {{ trans('settings.phone_number') }}
                                </label>
                                <input name="phone" value="{{ __(\Illuminate\Support\Facades\Auth::user()->phone) }}"
                                       id="phone" type="tel"
                                       oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                       class="form-control @error('phone') is-invalid @enderror"
                                       placeholder="+92 301 2345678"
                                       onkeypress="return searchKeyPress(event);"/>
                                @error('phone')
                                <span class="text-danger" role="alert">
                                <small>{{ $message }}</small>
                            </span>
                                @enderror
                            </div>
                            <div class="formStyle mb-3">
                                <label for="input-name">
                                    {{ trans('settings.email') }}
                                </label>
                                <input type="text" name="email_prev" id="company-name" disabled
                                       class="form-control"
                                       placeholder="{{ __('Email ID') }}"
                                       value="{{ __(\Illuminate\Support\Facades\Auth::user()->email) }}">

                                {{--                            @include('alerts.feedback', ['field' => 'email'])--}}
                            </div>
                            <div class="form-group mb-3">
                                <label for="input-name">
                                    {{ trans('settings.password') }}
                                </label>
                                <div class="input-group mb-1">
                                    <input type="text" class="form-control border-radius-left"
                                           placeholder="********" aria-label="Recipient's username"
                                           aria-describedby="basic-addon2" disabled>

                                    <div class="input-group-append border-radius-right bg-light-grey">
                                        <input type="hidden" class="form-control" name="email" id="email"
                                               placeholder="Email"
                                               required autofocus
                                               value="{{ __(\Illuminate\Support\Facades\Auth::user()->email) }}"/>
                                        <input type="button" id="inner_form"
                                               class="Roboto bold px-2 {{--pr-4--}} btn-primary-tertiary align-self-center bg-transparent border-0"
                                               value="{{ trans('settings.reset_password') }}"/>
                                        <div class="py-2 px-2 d-none" id="load_reset_btn">
                                            <div class="spinner-border text-secondary" role="status"
                                                 style="width: 1rem; height: 1rem;">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <small class="text-danger d-none mb-3" id="message_text"></small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-xl-6 col-12">
                    <div class="formStyle mb-3">
                        <label for="input-name">
                            {{ trans('organization_create.name') }}
                        </label>
                        <input type="text" name="organization_name" id="org_name"
                               class="form-control"
                               placeholder="{{ __('Organization Name') }}"
                               value="{{ __($organization->name) }}">
                    </div>
                    <div class="formStyle">
                        <label for="units">{{ trans('organization_create.units') }}</label>
                        <select class="form-control bg-white-smoke @error('units') is-invalid @enderror" id="units"
                                name="units" required>
                            <option value="lb" {{$organization->units == 'lb' ?'selected':""}}>{{__("Pounds")}}</option>
                            <option value="oz" {{$organization->units  == 'oz' ?'selected':""}}>{{__("Oz")}}</option>
                            <option
                                value="kg" {{$organization->units  == 'kg' ?'selected':""}}>{{__("Kilogram")}}</option>
                            <option value="g" {{$organization->units  == 'g' ?'selected':""}}>{{__("Grams")}}</option>
                        </select>
                        @error('units')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>
                    {{--                    <div class="form-group" {{($organization->currency == 'DKK') ?'selected':""}}>--}}
                    {{--                        <label for="currency" {{($organization->currency == 'DKK') ?'selected':""}}>{{ trans('organization_create.currency') }}</label>--}}
                    {{--                        <select class="form-control @error('currency') is-invalid @enderror" id="currency"--}}
                    {{--                                name="currency" required>--}}
                    {{--                            <option value="USD" {{($organization->currency == 'USD') ?'selected':""}}>{{__("Dollar")}}</option>--}}
                    {{--                            <option value="EUR" {{($organization->currency == 'EUR') ?'selected':""}}>{{__("Euro")}}</option>--}}
                    {{--                            <option value="GBP" {{($organization->currency == 'GBP') ?'selected':""}}>{{__("Pound")}}</option>--}}
                    {{--                            <option value="DKK" {{($organization->currency == 'DKK') ?'selected':""}}>{{__("Danish Krone")}}</option>--}}
                    {{--                            <option value="AFN" {{($organization->currency == 'AFN') ?'selected':""}}>Afghan Afghani</option>--}}
                    {{--                            <option value="ALL" {{($organization->currency == 'ALL') ?'selected':""}}>Albanian Lek</option>--}}
                    {{--                            <option value="DZD" {{($organization->currency == 'DZD') ?'selected':""}}>Algerian Dinar</option>--}}
                    {{--                            <option value="AOA" {{($organization->currency == 'AOA') ?'selected':""}}>Angolan Kwanza</option>--}}
                    {{--                            <option value="ARS" {{($organization->currency == 'ARS') ?'selected':""}}>Argentine Peso</option>--}}
                    {{--                            <option value="AMD" {{($organization->currency == 'AMD') ?'selected':""}}>Armenian Dram</option>--}}
                    {{--                            <option value="AWG" {{($organization->currency == 'AWG') ?'selected':""}}>Aruban Florin</option>--}}
                    {{--                            <option value="AUD" {{($organization->currency == 'AUD') ?'selected':""}}>Australian Dollar</option>--}}
                    {{--                            <option value="AZN" {{($organization->currency == 'AZN') ?'selected':""}}>Azerbaijani Manat</option>--}}
                    {{--                            <option value="BSD" {{($organization->currency == 'BSD') ?'selected':""}}>Bahamian Dollar</option>--}}
                    {{--                            <option value="BHD" {{($organization->currency == 'BHD') ?'selected':""}}>Bahraini Dinar</option>--}}
                    {{--                            <option value="BDT" {{($organization->currency == 'BDT') ?'selected':""}}>Bangladeshi Taka</option>--}}
                    {{--                            <option value="BBD" {{($organization->currency == 'BBD') ?'selected':""}}>Barbadian Dollar</option>--}}
                    {{--                            <option value="BYR" {{($organization->currency == 'BYR') ?'selected':""}}>Belarusian Ruble</option>--}}
                    {{--                            <option value="BEF" {{($organization->currency == 'BEF') ?'selected':""}}>Belgian Franc</option>--}}
                    {{--                            <option value="BZD" {{($organization->currency == 'BZD') ?'selected':""}}>Belize Dollar</option>--}}
                    {{--                            <option value="BMD" {{($organization->currency == 'BMD') ?'selected':""}}>Bermudan Dollar</option>--}}
                    {{--                            <option value="BTN" {{($organization->currency == 'BTN') ?'selected':""}}>Bhutanese Ngultrum</option>--}}
                    {{--                            <option value="BTC" {{($organization->currency == 'BTC') ?'selected':""}}>Bitcoin</option>--}}
                    {{--                            <option value="BOB" {{($organization->currency == 'BOB') ?'selected':""}}>Bolivian Boliviano</option>--}}
                    {{--                            <option value="BAM" {{($organization->currency == 'BAM') ?'selected':""}}>Bosnia-Herzegovina Convertible Mark</option>--}}
                    {{--                            <option value="BWP" {{($organization->currency == 'BWP') ?'selected':""}}>Botswanan Pula</option>--}}
                    {{--                            <option value="BRL" {{($organization->currency == 'BRL') ?'selected':""}}>Brazilian Real</option>--}}
                    {{--                            <option value="GBP" {{($organization->currency == 'GBP') ?'selected':""}}>British Pound Sterling</option>--}}
                    {{--                            <option value="BND" {{($organization->currency == 'BND') ?'selected':""}}>Brunei Dollar</option>--}}
                    {{--                            <option value="BGN" {{($organization->currency == 'BGN') ?'selected':""}}>Bulgarian Lev</option>--}}
                    {{--                            <option value="BIF" {{($organization->currency == 'BIF') ?'selected':""}}>Burundian Franc</option>--}}
                    {{--                            <option value="KHR" {{($organization->currency == 'KHR') ?'selected':""}}>Cambodian Riel</option>--}}
                    {{--                            <option value="CAD" {{($organization->currency == 'CAD') ?'selected':""}}>Canadian Dollar</option>--}}
                    {{--                            <option value="CVE" {{($organization->currency == 'CVE') ?'selected':""}}>Cape Verdean Escudo</option>--}}
                    {{--                            <option value="KYD" {{($organization->currency == 'KYD') ?'selected':""}}>Cayman Islands Dollar</option>--}}
                    {{--                            <option value="XOF" {{($organization->currency == 'XOF') ?'selected':""}}>CFA Franc BCEAO</option>--}}
                    {{--                            <option value="XAF" {{($organization->currency == 'XAF') ?'selected':""}}>CFA Franc BEAC</option>--}}
                    {{--                            <option value="XPF" {{($organization->currency == 'XPF') ?'selected':""}}>CFP Franc</option>--}}
                    {{--                            <option value="CLP" {{($organization->currency == 'CLP') ?'selected':""}}>Chilean Peso</option>--}}
                    {{--                            <option value="CNY" {{($organization->currency == 'CNY') ?'selected':""}}>Chinese Yuan</option>--}}
                    {{--                            <option value="COP" {{($organization->currency == 'COP') ?'selected':""}}>Colombian Peso</option>--}}
                    {{--                            <option value="KMF" {{($organization->currency == 'KMF') ?'selected':""}}>Comorian Franc</option>--}}
                    {{--                            <option value="CDF" {{($organization->currency == 'CDF') ?'selected':""}}>Congolese Franc</option>--}}
                    {{--                            <option value="CRC" {{($organization->currency == 'CRC') ?'selected':""}}>Costa Rican ColÃ³n</option>--}}
                    {{--                            <option value="HRK" {{($organization->currency == 'HRK') ?'selected':""}}>Croatian Kuna</option>--}}
                    {{--                            <option value="CUC" {{($organization->currency == 'CUC') ?'selected':""}}>Cuban Convertible Peso</option>--}}
                    {{--                            <option value="CZK" {{($organization->currency == 'CZK') ?'selected':""}}>Czech Republic Koruna</option>--}}
                    {{--                            <option value="DKK" {{($organization->currency == 'DKK') ?'selected':""}}>Danish Krone</option>--}}
                    {{--                            <option value="DJF" {{($organization->currency == 'DJF') ?'selected':""}}>Djiboutian Franc</option>--}}
                    {{--                            <option value="DOP" {{($organization->currency == 'DOP') ?'selected':""}}>Dominican Peso</option>--}}
                    {{--                            <option value="XCD" {{($organization->currency == 'XCD') ?'selected':""}}>East Caribbean Dollar</option>--}}
                    {{--                            <option value="EGP" {{($organization->currency == 'EGP') ?'selected':""}}>Egyptian Pound</option>--}}
                    {{--                            <option value="ERN" {{($organization->currency == 'ERN') ?'selected':""}}>Eritrean Nakfa</option>--}}
                    {{--                            <option value="EEK" {{($organization->currency == 'EEK') ?'selected':""}}>Estonian Kroon</option>--}}
                    {{--                            <option value="ETB" {{($organization->currency == 'ETB') ?'selected':""}}>Ethiopian Birr</option>--}}
                    {{--                            <option value="EUR" {{($organization->currency == 'EUR') ?'selected':""}}>Euro</option>--}}
                    {{--                            <option value="FKP" {{($organization->currency == 'FKP') ?'selected':""}}>Falkland Islands Pound</option>--}}
                    {{--                            <option value="FJD" {{($organization->currency == 'FJD') ?'selected':""}}>Fijian Dollar</option>--}}
                    {{--                            <option value="GMD" {{($organization->currency == 'GMD') ?'selected':""}}>Gambian Dalasi</option>--}}
                    {{--                            <option value="GEL" {{($organization->currency == 'GEL') ?'selected':""}}>Georgian Lari</option>--}}
                    {{--                            <option value="DEM" {{($organization->currency == 'DEM') ?'selected':""}}>German Mark</option>--}}
                    {{--                            <option value="GHS" {{($organization->currency == 'GHS') ?'selected':""}}>Ghanaian Cedi</option>--}}
                    {{--                            <option value="GIP" {{($organization->currency == 'GIP') ?'selected':""}}>Gibraltar Pound</option>--}}
                    {{--                            <option value="GRD" {{($organization->currency == 'GRD') ?'selected':""}}>Greek Drachma</option>--}}
                    {{--                            <option value="GTQ" {{($organization->currency == 'GTQ') ?'selected':""}}>Guatemalan Quetzal</option>--}}
                    {{--                            <option value="GNF" {{($organization->currency == 'GNF') ?'selected':""}}>Guinean Franc</option>--}}
                    {{--                            <option value="GYD" {{($organization->currency == 'GYD') ?'selected':""}}>Guyanaese Dollar</option>--}}
                    {{--                            <option value="HTG" {{($organization->currency == 'HTG') ?'selected':""}}>Haitian Gourde</option>--}}
                    {{--                            <option value="HNL" {{($organization->currency == 'HNL') ?'selected':""}}>Honduran Lempira</option>--}}
                    {{--                            <option value="HKD" {{($organization->currency == 'HKD') ?'selected':""}}>Hong Kong Dollar</option>--}}
                    {{--                            <option value="HUF" {{($organization->currency == 'HUF') ?'selected':""}}>Hungarian Forint</option>--}}
                    {{--                            <option value="ISK" {{($organization->currency == 'ISK') ?'selected':""}}>Icelandic KrÃ³na</option>--}}
                    {{--                            <option value="INR" {{($organization->currency == 'INR') ?'selected':""}}>Indian Rupee</option>--}}
                    {{--                            <option value="IDR" {{($organization->currency == 'IDR') ?'selected':""}}>Indonesian Rupiah</option>--}}
                    {{--                            <option value="IRR" {{($organization->currency == 'IRR') ?'selected':""}}>Iranian Rial</option>--}}
                    {{--                            <option value="IQD" {{($organization->currency == 'IQD') ?'selected':""}}>Iraqi Dinar</option>--}}
                    {{--                            <option value="ILS" {{($organization->currency == 'ILS') ?'selected':""}}>Israeli New Sheqel</option>--}}
                    {{--                            <option value="ITL" {{($organization->currency == 'ITL') ?'selected':""}}>Italian Lira</option>--}}
                    {{--                            <option value="JMD" {{($organization->currency == 'JMD') ?'selected':""}}>Jamaican Dollar</option>--}}
                    {{--                            <option value="JPY" {{($organization->currency == 'JPY') ?'selected':""}}>Japanese Yen</option>--}}
                    {{--                            <option value="JOD" {{($organization->currency == 'JOD') ?'selected':""}}>Jordanian Dinar</option>--}}
                    {{--                            <option value="KZT" {{($organization->currency == 'KZT') ?'selected':""}}>Kazakhstani Tenge</option>--}}
                    {{--                            <option value="KES" {{($organization->currency == 'KES') ?'selected':""}}>Kenyan Shilling</option>--}}
                    {{--                            <option value="KWD" {{($organization->currency == 'KWD') ? 'selected': ""}}>Kuwaiti Dinar</option>--}}
                    {{--                            <option value="KGS" {{($organization->currency == 'KGS') ?'selected':""}}>Kyrgystani Som</option>--}}
                    {{--                            <option value="LAK" {{($organization->currency == 'LAK') ?'selected':""}}>Laotian Kip</option>--}}
                    {{--                            <option value="LVL" {{($organization->currency == 'LVL') ?'selected':""}}>Latvian Lats</option>--}}
                    {{--                            <option value="LBP" {{($organization->currency == 'LBP') ?'selected':""}}>Lebanese Pound</option>--}}
                    {{--                            <option value="LSL" {{($organization->currency == 'LSL') ?'selected':""}}>Lesotho Loti</option>--}}
                    {{--                            <option value="LRD" {{($organization->currency == 'LRD') ?'selected':""}}>Liberian Dollar</option>--}}
                    {{--                            <option value="LYD" {{($organization->currency == 'LYD') ?'selected':""}}>Libyan Dinar</option>--}}
                    {{--                            <option value="LTL" {{($organization->currency == 'LTL') ?'selected':""}}>Lithuanian Litas</option>--}}
                    {{--                            <option value="MOP" {{($organization->currency == 'MOP') ?'selected':""}}>Macanese Pataca</option>--}}
                    {{--                            <option value="MKD" {{($organization->currency == 'MKD') ?'selected':""}}>Macedonian Denar</option>--}}
                    {{--                            <option value="MGA" {{($organization->currency == 'MGA') ?'selected':""}}>Malagasy Ariary</option>--}}
                    {{--                            <option value="MWK" {{($organization->currency == 'MWK') ?'selected':""}}>Malawian Kwacha</option>--}}
                    {{--                            <option value="MYR" {{($organization->currency == 'MYR') ?'selected':""}}>Malaysian Ringgit</option>--}}
                    {{--                            <option value="MVR" {{($organization->currency == 'MVR') ?'selected':""}}>Maldivian Rufiyaa</option>--}}
                    {{--                            <option value="MRO" {{($organization->currency == 'MRO') ?'selected':""}}>Mauritanian Ouguiya</option>--}}
                    {{--                            <option value="MUR" {{($organization->currency == 'MUR') ?'selected':""}}>Mauritian Rupee</option>--}}
                    {{--                            <option value="MXN" {{($organization->currency == 'MXN') ?'selected':""}}>Mexican Peso</option>--}}
                    {{--                            <option value="MDL" {{($organization->currency == 'MDL') ?'selected':""}}>Moldovan Leu</option>--}}
                    {{--                            <option value="MNT" {{($organization->currency == 'MNT') ?'selected':""}}>Mongolian Tugrik</option>--}}
                    {{--                            <option value="MAD" {{($organization->currency == 'MAD') ?'selected':""}}>Moroccan Dirham</option>--}}
                    {{--                            <option value="MZM" {{($organization->currency == 'DKK') ?'selected':""}}>Mozambican Metical</option>--}}
                    {{--                            <option value="MMK" {{($organization->currency == 'MMK') ?'selected':""}}>Myanmar Kyat</option>--}}
                    {{--                            <option value="NAD" {{($organization->currency == 'NAD') ?'selected':""}}>Namibian Dollar</option>--}}
                    {{--                            <option value="NPR" {{($organization->currency == 'NPR') ?'selected':""}}>Nepalese Rupee</option>--}}
                    {{--                            <option value="ANG" {{($organization->currency == 'ANG') ?'selected':""}}>Netherlands Antillean Guilder</option>--}}
                    {{--                            <option value="TWD" {{($organization->currency == 'TWD') ?'selected':""}}>New Taiwan Dollar</option>--}}
                    {{--                            <option value="NZD" {{($organization->currency == 'NZD') ?'selected':""}}>New Zealand Dollar</option>--}}
                    {{--                            <option value="NIO" {{($organization->currency == 'NIO') ?'selected':""}}>Nicaraguan CÃ³rdoba</option>--}}
                    {{--                            <option value="NGN" {{($organization->currency == 'NGN') ?'selected':""}}>Nigerian Naira</option>--}}
                    {{--                            <option value="KPW" {{($organization->currency == 'KPW') ?'selected':""}}>North Korean Won</option>--}}
                    {{--                            <option value="NOK" {{($organization->currency == 'NOK') ?'selected':""}}>Norwegian Krone</option>--}}
                    {{--                            <option value="OMR" {{($organization->currency == 'OMR') ?'selected':""}}>Omani Rial</option>--}}
                    {{--                            <option value="PKR" {{($organization->currency == 'PKR') ?'selected':""}}>Pakistani Rupee</option>--}}
                    {{--                            <option value="PAB" {{($organization->currency == 'PAB') ?'selected':""}}>Panamanian Balboa</option>--}}
                    {{--                            <option value="PGK" {{($organization->currency == 'PGK') ?'selected':""}}>Papua New Guinean Kina</option>--}}
                    {{--                            <option value="PYG" {{($organization->currency == 'PYG') ?'selected':""}}>Paraguayan Guarani</option>--}}
                    {{--                            <option value="PEN" {{($organization->currency == 'PEN') ?'selected':""}}>Peruvian Nuevo Sol</option>--}}
                    {{--                            <option value="PHP" {{($organization->currency == 'PHP') ?'selected':""}}>Philippine Peso</option>--}}
                    {{--                            <option value="PLN" {{($organization->currency == 'PLN') ?'selected':""}}>Polish Zloty</option>--}}
                    {{--                            <option value="QAR" {{($organization->currency == 'QAR') ?'selected':""}}>Qatari Rial</option>--}}
                    {{--                            <option value="RON" {{($organization->currency == 'RON') ?'selected':""}}>Romanian Leu</option>--}}
                    {{--                            <option value="RUB" {{($organization->currency == 'RUB') ?'selected':""}}>Russian Ruble</option>--}}
                    {{--                            <option value="RWF" {{($organization->currency == 'RWF') ?'selected':""}}>Rwandan Franc</option>--}}
                    {{--                            <option value="SVC" {{($organization->currency == 'SVC') ?'selected':""}}>Salvadoran ColÃ³n</option>--}}
                    {{--                            <option value="WST" {{($organization->currency == 'WST') ?'selected':""}}>Samoan Tala</option>--}}
                    {{--                            <option value="SAR" {{($organization->currency == 'SAR') ?'selected':""}}>Saudi Riyal</option>--}}
                    {{--                            <option value="RSD" {{($organization->currency == 'RSD') ?'selected':""}}>Serbian Dinar</option>--}}
                    {{--                            <option value="SCR" {{($organization->currency == 'SCR') ?'selected':""}}>Seychellois Rupee</option>--}}
                    {{--                            <option value="SLL" {{($organization->currency == 'SLL') ?'selected':""}}>Sierra Leonean Leone</option>--}}
                    {{--                            <option value="SGD" {{($organization->currency == 'SGD') ?'selected':""}}>Singapore Dollar</option>--}}
                    {{--                            <option value="SKK" {{($organization->currency == 'SKK') ?'selected':""}}>Slovak Koruna</option>--}}
                    {{--                            <option value="SBD" {{($organization->currency == 'SBD') ?'selected':""}}>Solomon Islands Dollar</option>--}}
                    {{--                            <option value="SOS" {{($organization->currency == 'SOS') ?'selected':""}}>Somali Shilling</option>--}}
                    {{--                            <option value="ZAR" {{($organization->currency == 'ZAR') ?'selected':""}}>South African Rand</option>--}}
                    {{--                            <option value="KRW" {{($organization->currency == 'KRW') ?'selected':""}}>South Korean Won</option>--}}
                    {{--                            <option value="XDR" {{($organization->currency == 'XDR') ?'selected':""}}>Special Drawing Rights</option>--}}
                    {{--                            <option value="LKR" {{($organization->currency == 'LKR') ?'selected':""}}>Sri Lankan Rupee</option>--}}
                    {{--                            <option value="SHP" {{($organization->currency == 'SHP') ?'selected':""}}>St. Helena Pound</option>--}}
                    {{--                            <option value="SDG" {{($organization->currency == 'SDG') ?'selected':""}}>Sudanese Pound</option>--}}
                    {{--                            <option value="SRD" {{($organization->currency == 'SRD') ?'selected':""}}>Surinamese Dollar</option>--}}
                    {{--                            <option value="SZL" {{($organization->currency == 'SZL') ?'selected':""}}>Swazi Lilangeni</option>--}}
                    {{--                            <option value="SEK" {{($organization->currency == 'SEK') ?'selected':""}}>Swedish Krona</option>--}}
                    {{--                            <option value="CHF" {{($organization->currency == 'CHF') ?'selected':""}}>Swiss Franc</option>--}}
                    {{--                            <option value="SYP" {{($organization->currency == 'SYP') ?'selected':""}}>Syrian Pound</option>--}}
                    {{--                            <option value="STD" {{($organization->currency == 'STD') ?'selected':""}}>São Tomé and Príncipe Dobra</option>--}}
                    {{--                            <option value="TJS" {{($organization->currency == 'TJS') ?'selected':""}}>Tajikistani Somoni</option>--}}
                    {{--                            <option value="TZS" {{($organization->currency == 'TZS') ?'selected':""}}>Tanzanian Shilling</option>--}}
                    {{--                            <option value="THB" {{($organization->currency == 'THB') ?'selected':""}}>Thai Baht</option>--}}
                    {{--                            <option value="TOP" {{($organization->currency == 'TOP') ?'selected':""}}>Tongan pa'anga</option>--}}
                    {{--                            <option value="TTD" {{($organization->currency == 'TTD') ?'selected':""}}>Trinidad & Tobago Dollar</option>--}}
                    {{--                            <option value="TND" {{($organization->currency == 'TND') ?'selected':""}}>Tunisian Dinar</option>--}}
                    {{--                            <option value="TRY" {{($organization->currency == 'TRY') ?'selected':""}}>Turkish Lira</option>--}}
                    {{--                            <option value="TMT" {{($organization->currency == 'TMT') ?'selected':""}}>Turkmenistani Manat</option>--}}
                    {{--                            <option value="UGX" {{($organization->currency == 'UGX') ?'selected':""}}>Ugandan Shilling</option>--}}
                    {{--                            <option value="UAH" {{($organization->currency == 'UAH') ?'selected':""}}>Ukrainian Hryvnia</option>--}}
                    {{--                            <option value="AED" {{($organization->currency == 'AED') ?'selected':""}}>United Arab Emirates Dirham</option>--}}
                    {{--                            <option value="UYU" {{($organization->currency == 'UYU') ?'selected':""}}>Uruguayan Peso</option>--}}
                    {{--                            <option value="UZS" {{($organization->currency == 'UZS') ?'selected':""}}>Uzbekistan Som</option>--}}
                    {{--                            <option value="VUV" {{($organization->currency == 'VUV') ?'selected':""}}>Vanuatu Vatu</option>--}}
                    {{--                            <option value="VEF" {{($organization->currency == 'VEF') ?'selected':""}}>Venezuelan BolÃ­var</option>--}}
                    {{--                            <option value="VND" {{($organization->currency == 'VND') ?'selected':""}}>Vietnamese Dong</option>--}}
                    {{--                            <option value="YER" {{($organization->currency == 'YER') ?'selected':""}}>Yemeni Rial</option>--}}
                    {{--                            <option value="ZMK" {{($organization->currency == 'ZMK') ?'selected':""}}>Zambian Kwacha</option>--}}
                    {{--                        </select>--}}
                    {{--                        @error('currency')--}}
                    {{--                        <span class="text-danger" role="alert" {{($organization->currency == 'DKK') ?'selected':""}}>--}}
                    {{--                                    <small>{{ $message }}</small>--}}
                    {{--                                </span>--}}
                    {{--                        @enderror--}}
                    {{--                    </div>--}}
                </div>
            </div>
            {{-- footer button--}}
            <div class="d-flex justify-content-end mt-40">
                <a id="cancel-edit" href="{{URL::previous()}}"
                   class="btn btn-outline-danger disabled-with-text">
                    {{ trans('settings.cancel_btn') }}
                </a>
                <button id="edit_profile_btn" type="button" onclick="mainFormSubmit()"
                        class="btn btn-primary ms-2 disabled-with-text px-0 px-md-2 px-lg-4 px-xl-5">
                    {{ trans('settings.save_btn') }}
                </button>
            </div>
        </form>
    </div>

    <!-- NOT USED --- MODAL CHANGE PASSWORD -->
    <div class="modal fade" id="modal_pass_id" tabindex="-1" role="dialog" aria-labelledby="modal_pass_id"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title Poppins bold" id="exampleModalLongTitle">Change Password</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-5">
                    <form method="post" action="" autocomplete="off">
                        @csrf
                        @method('patch')

                        {{--                                @include('alerts.success', ['key' => 'password_status'])--}}
                        {{--                                @include('alerts.error_self_update', ['key' => 'not_allow_password'])--}}

                        <div class="form-group{{ $errors->has('password') ? ' has-danger' : '' }}">
                            <label for="input-password">
                                <em class="w3-xxlarge fa fa-eye-slash mr-1"></em>{{ __('New Password') }}
                            </label>
                            <input type="password" name="password" id="input-password"
                                   class="form-control"
                                   placeholder="{{ __('New Password') }}" value="" required>

                            {{--                                    @include('alerts.feedback', ['field' => 'password'])--}}
                        </div>
                        <div class="form-group">
                            <label class="label-names" for="input-password-confirmation">
                                <em class="w3-xxlarge fa fa-eye-slash me-1"></em>{{ __('Confirm New Password') }}
                            </label>
                            <input type="password" name="password_confirmation"
                                   id="input-password-confirmation" class="form-control"
                                   placeholder="{{ __('Confirm New Password') }}" value="" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn mt-4">{{ __('Change password') }}</button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('footer_scripts')
    <!--Phone Flags-->
    <script>
        let input = document.querySelector("#phone");
        ipLookUp();

        // get current user country
        function ipLookUp() {
            $.ajax({
                dataType: 'json',
                url: 'https://api.hostip.info/get_json.php',
                success: function (data) {

                    let $ip = data['ip'],
                        $city = data['city'],
                        $countryCode = data['country_code'],
                        $countryName = data['country_name'];
                        if ($countryCode == "XX") {
                            window.intlTelInput(input, ({
                                separateDialCode: false,
                                preferredCountries: ["us", "ca"],
                                utilsScript: "{{asset('public/js/utils.js')}}",
                                nationalMode: false
                            }));
                        }
                        else{
                            window.intlTelInput(input, ({
                                separateDialCode: false,
                                preferredCountries: ["us", "ca"],
                                utilsScript: "{{asset('public/js/utils.js')}}",
                                nationalMode: false,
                                initialCountry: $countryCode
                            }));
                        }
                    
                },
                error: function (jqXHR, textStatus) {
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                    }));
                }
            });
            return 0;
        }

        function searchKeyPress(e) {
            e = e || window.event;
            if (e.keyCode === 13) {
                document.getElementById('registeration_next_btn_id').click();
                return false;
            }
            return true;
        }
    </script>
    @if($errors->has('password'))
        <script>
            $('#modal_pass_id').modal("show");
        </script>
    @endif

    <script>
        function mainFormSubmit() {
            document.getElementById("edit_profile_form").submit();
        }


        jQuery(document).ready(function ($) {
            $("#inner_form").click(function (e) {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': jQuery('meta[name="csrf-token"]').attr('content')
                    }
                });
                e.preventDefault();
                var formData = {
                    email: jQuery('#email').val(),
                };
                $.ajax({
                    type: "POST",
                    url: '{{ route('password.email') }}',
                    data: formData,
                    dataType: 'json',
                    beforeSend: function () {
                        $("#inner_form").val(" ");
                        $("#inner_form").addClass('d-none');
                        $("#load_reset_btn").removeClass('d-none');
                        $("#load_reset_btn").addClass('d-block');
                    },
                    success: function (data) {
                        $("#message_text").removeClass('d-none');
                        $("#message_text").html(data.message);
                        $("#inner_form").addClass('d-block');
                        $("#inner_form").val('Sent');
                        $("#load_reset_btn").removeClass('d-block');
                        $("#load_reset_btn").addClass('d-none');
                    },
                    error: function (data) {
                        console.log(data);
                    }
                });
            });

        });

    </script>
@endpush
