<?php ?>
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Add Catalog')
@else
    @section('titles','Add Store')
@endif
@section('content')
    <div>
        <x-products.add-page-title name="{{trans('channel.page_title')}}" type="false" :routes="route('channel.index')" />
        <div class="row mt-4">
            <div class="col-12 col-md-9 col-lg-9 col-xl-6">
                <form id="chan_ctlg_create_form" class="formStyle" action="{{isset($channel) ? route('channel.update',$channel->id) : route('channel.store')}}"
                      method="POST">
                    @csrf
                    @if(isset($brand))
                        @method('PUT')
                        <input type="hidden" name="id" value="{{ $channel->id }}">
                    @endif

                    <input type="hidden" name="type" value="shopify">

                    <div class="form-group">
                        <label for="name" class="mb-1">{{trans('channel_create.catalog_name')}}&nbsp;<span class="text-danger">*</span></label>
                        <input type="text" name="name"
                               class="form-control @error('name') is-invalid @enderror"
                               autofocus value="">
                        @error('name')
                        <span class="text-danger">
                            <small>{{$message}}</small>
                        </span>
                        @enderror
                    </div>



                    <div class="form-group mt-3">
                        <label for="versions" class="mb-1">{{trans('channel_create.language')}}&nbsp;<span class="text-danger">*</span></label>
                        <select type="text" name="versions"
                               class="form-control bg-white-smoke @error('versions') is-invalid @enderror" >
                            <option value="">Select Language</option>
                            @foreach($versions as $version)
                                 <option value="{{$version->id}}">{{__($version->name)}}</option>
                            @endforeach
                        </select>
                        @error('versions')
                        <span class="text-danger">
                            <small>{{$message}}</small>
                        </span>
                        @enderror
                    </div>
{{--                   TODO location edit for channel--}}
{{--                    <div class="form-group mt-3">--}}
{{--                        <label for="locations" class="mb-1">{{trans('channel_create.location')}}<span class="text-danger">*</span></label>--}}
{{--                        <select name="locations[]" id="locations" multiple="multiple"--}}
{{--                                class="form-control sumoselect bg-white-smoke @error('locations') is-invalid @enderror" >--}}
{{--                            @foreach($unassigned_locations as $location)--}}
{{--                                <option value="{{ $location->id }}" {{ (in_array($location->id, old('locations', [])) ? "selected": '') }}>{{ $location->name }}</option>--}}
{{--                            @endforeach--}}

{{--                            @foreach($assigned_locations as $location)--}}
{{--                                <option value="{{ $location->id }}" disabled>{{ $location->name }} (Assigned)</option>--}}
{{--                            @endforeach--}}
{{--                        </select>--}}
{{--                        <a href="/products/locations/create" class="text-sm"><small>Click here to add Locations</small></a>--}}
{{--                        @error('locations')--}}
{{--                        <span class="text-danger">--}}
{{--                            <small>{{$message}}</small>--}}
{{--                        </span>--}}
{{--                        @enderror--}}
{{--                    </div>--}}

                    <div class="form-group mb-4 mt-40 d-flex justify-content-end">
                        <button type="submit" id="chan_ctlg_create_btn" class=" btn btn-primary">
                                    {{trans('channel_create.save_btn')}}
                        </button>
                        <a href="{{route('channel.index')}}" class="btn btn-outline-primary ms-3" id="cancel-btn">
                            {{trans('channel_create.cancel_btn')}}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
@endpush
