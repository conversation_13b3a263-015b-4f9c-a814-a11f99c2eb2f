@extends('layouts.app_new',['sidebar_display'=>false])
@section('titles','Signup')
@section('content')

<style>
    html, body {
        font-weight: inherit !important;
        line-height: 22px !important;
        margin:0 !important;
        padding:0 !important;
    }
    body{
    background-image: url('{{ asset('assets/images/Apimio_Bg.png') }}');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    }

    ul,li{

        line-height: 27px !important;
    }

    a{
        font-size: 0.87rem !important;
    }
</style>

<div class="container-fluid" style="height:100vh !important;">
    <div class="row mt-2 h-100">
        <div class="d-flex justify-content-between px-4 col-lg-12 mx-auto">
            <img src="{{ asset('media/logo.png') }}" style="min-width:150px; max-width:200px;" class="mb-0 h-40" />
            <div class="mb-0" style="min-width:200px; max-width:200px;">
                <div class="d-flex align-items-center bg-light mb-md-0 me-md-auto text-white justify-content-around text-decoration-none w-100 user-information py-1">
                    <span class="circle bg-warning">B</span>
                    <span class="ms-2 d-none d-sm-inline text-black">
                        <h4 class="fw-700 mb-0 d-none d-lg-block" title="Bilal Arshad">
                            {{$user->fname}} {{$user->lname}}
                        </h4>
                        <p class="mb-0 d-none d-lg-block" style="font-size: 0.875rem;" title="Tanzayb1212">
                            {{$organization->name}}
                        </p>
                    </span>
                </div>
            </div>
        </div>

        <div class="col-lg-8 mx-auto">
            <div class="row text-center">
                <div class="col-lg-12">
                    <h1>Welcome to Apimio!</h1>
                    <p>Your account is successfully created</p>
                </div>
                <div class="col-lg-8 mx-auto">
                    <img src="{{ asset('assets/images/apimio-to-shopify.png') }}" class="w-25" />
                    <h1>Now it's time to sync your Shopify store with Apimio and manage your products seamlessly.</h1>
                    <a href="{{route('channel.shopify.fetch_all_products',$shop_data['channel_id'])}}" class="btn btn-primary">Sync Products Now</a>
                    <div class="">
                        <a href="{{route('home')}}" class="text-decoration-none mt-1">Skip for Now</a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div>
                        <div class="d-flex gap-2 mb-0">
                            <img class="mt-1" src="{{ asset('assets/images/blue-check.png') }}" style="width:15px;height:16px;" />
                            <h4 class="mb-0">What happens next?</h4>
                        </div>
                        <p class="ms-4">Once synced, your Shopify products will automatically import into Apimio. You can then manage your product data efficiently in one place</p>
                    </div>
                    <div>
                        <div class="d-flex gap-2 mb-0">
                            <img class="mt-1" src="{{ asset('assets/images/blue-check.png') }}" style="width:15px;height:16px;" />
                            <h4>What happens next?</h4>
                        </div>
                        <div>
                            <ul>
                                <li><strong>Centralized Product Management:</strong> Control all your product data in one platform, making management easier and faster.</li>
                                <li><strong>Seamless Channel Sync:</strong> Ensure consistency by syncing product data across multiple channels, including Shopify and more.</li>
                                <li><strong>Improve Data Accuracy:</strong> Eliminate errors and inconsistencies with Apimio’s powerful data validation tools.</li>
                                <li><strong>Bulk Editing Tools:</strong> Make large-scale edits to your product catalog quickly and efficiently.</li>
                                <li><strong>Automated Updates:</strong> Save time by setting up automated product data updates.</li>
                            </ul>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
