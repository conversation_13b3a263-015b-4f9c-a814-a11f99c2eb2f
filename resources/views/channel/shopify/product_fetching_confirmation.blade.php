<?php ?>
@extends('layouts.app_new')
@section('titles','Channels')
@section('content')
    <div>
        <x-products.page-title name="Channel" description="Fetch the products from shopify"
                               links="false" button="false" buttonname="null"/>


        <div class="row text-center">
            <div class="col-12">
                <img class="mb-5 mt-4" src="{{ asset('media/<EMAIL>') }}" alt="">
                <br>
                <img class="mb-4" src="{{ asset('media/apimio-shopify-link.png') }}" width="20%" alt="">
                <form method="get" action="{{ route("channel.shopify.install") }}" id="chan_shop_creat_form connect-shopify-form">
                    <div class="d-flex justify-content-center">

                        <a href="{{route('shopify.product.fetch',$channel->id)}}" class="ripplelink btn btn-primary"
                                id="chan_shop_creat_btn button-addon2">
                            <i class="fa fa-check-circle-o font-22"></i>&ensp;
                            {{__("Yes")}}
                        </a>
                        &ensp;
                        <a class="ripplelink btn btn-outline-primary"
                                id="chan_shop_creat_btn button-addon2">
                            <i class="fa fa-times-circle-o font-22"></i>&ensp;
                            {{__("No")}}
                        </a>
                    </div>
                </form>
            </div>
        </div>

    </div>
@endsection
