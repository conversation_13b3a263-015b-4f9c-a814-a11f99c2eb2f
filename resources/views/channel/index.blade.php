@extends('layouts.app_new')
@section('titles','Stores')
@section('content')

<style>
    /* Styles for the disabled button */
    
</style>
    <div>
        <x-products.page-title name="{{trans('channel.page_title')}}" description="{{trans('channel.page_description')}}"
                               links="false" button="true">
            <x-slot name="addbutton">
                @can('create-channel', \App\Models\Channel\Channel::query())
                <a href="{{ route('channel.create') }}"
                    id="add-family"
                    class="btn btn-primary float-lg-right float-md-right ">
                        {{ trans('channel.add_new_btn') }}
                </a>
                @else
                <a href="javascript:void(0)"
                    id=""
                    class="btn btn-primary float-lg-right float-md-right "
                    data-bs-toggle="modal" data-billingTitle="Stores" data-billingInfo="Unlock the ability to manage multiple stores efficiently, expand your product catalog across various locations, and streamline operations with ease." data-bs-target="#upgradeBilling">
                        {{ trans('channel.add_new_btn') }}
                </a>
                @endcan
            </x-slot>

        </x-products.page-title>
    </div>

            <div class="row">
                <div class="col-12">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="thead-light">
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Template</th>
                                <th class="text-end">Settings</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($data["channel"] as $channel)
                                <tr onclick="location.href = '{{route('shopify.index',['channels[]'=>$channel->id])}}'"
                                    class="cursor-pointer border-radius">
                                    <td>
                                        <a href="{{route('shopify.index',['channels[]'=>$channel->id])}}" id="channel-name">{{__($channel->name)}}</a>
                                    </td>

                                    <td>
                                        @can('add_shopify' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
                                            @if($channel->shopify_channels->count() > 0)
                                                <img src="https://img.icons8.com/color/25/000000/shopify.png" alt="">&nbsp;
                                                {{trans('channel.shopify_connected_btn')}}
                                            @else
                                                <a href="{{ route("shopify.show",$channel->id) }}" class="btn btn-sm btn-outline-primary" id="connect-to-shopify">
                                                    &nbsp;
                                                    {{trans('channel.connect_to_shopify_btn')}}
                                                </a>
                                            @endif
                                        @endcan
                                    </td>
                                    <td>
                                        @if($channel->shopify_channels->count() == 0)
                                        <div class="update-store-btn disabled">
                                            <a id="Update-btn"
                                            class="btn btn-sm btn-outline-primary update-store-btn"

                                                    href="{{ route("shopify.show",$channel->id) }}"
                                                title="Connect shopify first to update the template"
                                                aria-disabled="true"
                                                disabled="true"
                                            >
                                                Update
                                            </a>
                                        </div>
                                        @else
                                        <div class="update-store-btn">
                                            <a id="Update-btn"
                                            class="btn btn-sm btn-outline-primary update-store-btn"
                                                href="{{route('shopify.template.create',$channel->id)}}"
                                                title="Update the template of shopify for syncing"

                                            >
                                                Update
                                            </a>
                                        </div>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                    @if($channel->shopify_channels->count() == 0)
                                        <a  href="{{ route("shopify.show",$channel->id) }}" id="setting" class="text-decoration-none">
                                            <i class="icon icon-setting fs-20 "></i>
                                        </a>
                                        @else
                                        <a  href="{{ route("channel.edit", $channel->id) }}" id="setting" class="text-decoration-none">
                                            <i class="icon icon-setting fs-20 "></i>
                                        </a>
                                    @endif
                                    </td>
                                    
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                        {!! $data['channel']->appends($request->all())->links() !!}
                    </div>
                </div>
            </div>

@endsection
@push('footer_scripts')
<script>
    $('.update-store-btn').click(function(e) {
        if($(this).attr('disabled','true')){
             e.stopImmediatePropagation();
        }
    })
</script>
<script>
    // Initialize Bootstrap tooltips with custom trigger option
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        var tooltip = new bootstrap.Tooltip(tooltipTriggerEl, {
            trigger: 'hover focus'
        });

        // Hide tooltip on click
        tooltipTriggerEl.addEventListener('click', function () {
            tooltip.hide();
        });

        return tooltip;
    });
</script>
@endpush
