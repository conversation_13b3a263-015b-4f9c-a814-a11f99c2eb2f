@extends('layouts.app_new' ,['sidebar_display'=>false])
@section('titles','Reset Password')
@section('content')

    <div class="row h-100">

        <div class="col-12 mt-4">
            <!--Logo-->
            <div class="d-flex flex-column flex-column-auto mt-3 text-center">
                <a href="{{env('APIMIO_URL')}}">
                    <img src="{{asset('/media/logo.png')}}" class="h-40" alt="Logo">
                </a>
            </div>

            {{--    style="max-width: 900px; margin: 0 auto"--}}
            <div class="row justify-content-center" >
                <div class="col-12 col-sm-9 col-md-9 col-lg-9 col-lg-10 col-xl-5 col-xxl-4 mt-3" >
                    <form id="pass_reset_form" method="POST" class="formStyle" action="{{ route('password.update') }}">
                        @csrf
                        <input type="hidden" name="token" value="{{ $token }}">
                        <input type="hidden" name="email" value="{{ $email }}">
                        <div class="text-left">
                            <h2 class="text-center">{{ trans('password_reset_link.page_title') }}</h2>
                            <p class="text-center">{{ trans('password_reset_link.page_description') }}</p>
                            <div class="form-group mb-3">
                                <label for="password">{{ trans('password_reset_link.password') }}</label>
                                &nbsp;<span class="text-danger">*</span>
                                <input id="password" type="password" name="password"
                                       autocomplete="new-password" placeholder="********"
                                       class="form-control @error('password') is-invalid @enderror"
                                       required autofocus/>
                                @error('password')
                                <span class="invalid-feedback" role="alert">
                                        <small>{{ $message }}</small>
                                    </span>
                                @enderror

                            </div>
                            <div class="form-group">
                                <label for="password-confirm">{{ trans('password_reset_link.confirm_password') }}</label>
                                &nbsp;<span class="text-danger">*</span>
                                <input id="password-confirm" type="password" name="password_confirmation"
                                       autocomplete="new-password" placeholder="********"
                                       class="form-control"
                                       required/>

                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button id="pass_reset_btn" type="submit" class="w-75 btn btn-primary" >
                                {{ trans('password_reset_link.reset_pass_btn') }}
                            </button>
                        </div>

                    </form>
                </div>

            </div>

        </div>
    </div>

@endsection
