@extends('layouts.app_new' ,['sidebar_display'=>false])
@section('titles','Login')
@section('content')
@push('meta_tags')
<meta name="title" content="Login to your Apimio account">
<meta>
@endpush

<div class="row d-flex justify-content-center">
    <!--Left side-->
    <div id="Signup-page" class="col-12 col-sm-12 col-md-10 col-xl-8 mt-4 center-align">

        <!--Logo-->
        <div class="d-flex flex-column flex-column-auto mt-3 text-center position-relative">
            <div class="back-btn-css"> <button class="next-Btn" type="button" id="prevBtn" onclick="nextPrev(-1)"><img
                        src={{asset('media/back.svg')}} alt="back" /> <span class="ml-1"> Back</span></button></div>
            <a href="{{env('APIMIO_URL')}}">
                <img src="{{asset('media/logo.png')}}" alt="Logo">
            </a>
        </div>
        <!--Login Form-->
        <div class="row justify-content-center">
            <div class="col-12 col-sm-10 col-md-10 col-lg-12 col-xl-10 col-xxl-8 px-2 mt-32">
                <div class="pb-4">
                    <h2 id="title" class="text-center">
                        {{ trans('onboarding.boarding_title_information') }}
                    </h2>
                    <p class="clr-grey text-center">{{ trans('onboarding.boarding_paragraph') }}</p>

                    <div class="progress progress-bar-css1 mb-0">
                        <div class="progress-bar bg-success" id="progress" role="progressbar" style="width:0%"
                            aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <p id="progress-text" class="mt-1 text-end fw-700">0%</p>

                    <!--On-boarding form-->
                    <form id="regForm" method="post" action="" class="mt-32">
                        <input type="hidden" id="csrf" name="_token" value="{{csrf_token()}}">
                        <div class="tab">
                            <h2 class="">
                                {{ trans('onboarding.question1') }}
                            </h2>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="options1 me-4" id="Grocery" onclick="ApiData('Grocery','abc')">Grocery</div>
                                <div class="options1 me-4" id="Accessories" onclick="ApiData('Accessories')">Accessories
                                </div>
                                <div class="options1" id="Fashion" onclick="ApiData('Fashion & Clothing')">Fashion &
                                    Clothing</div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="options1 me-4" id="FootApparel" onclick="ApiData('Foot Apparel')">Foot
                                    Apparel
                                </div>
                                <div class="options1 me-4" id="Automotive" onclick="ApiData('Automotive')">Automotive
                                </div>
                                <div class="options1 " id="Electronics" onclick="ApiData('Electronics')">Electronics
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="options1 me-4" id="CraftsDecor" onclick="ApiData('Crafts & decor')">Crafts &
                                    Decor</div>
                                <div class="options1 me-4" id="BooksStationery" onclick="ApiData('Books & Stationery')">
                                    Books & Stationery</div>
                                <div class="options1 " id="SportsOutdoors" onclick="ApiData('Sports & Outdoors')">Sports
                                    & Outdoors </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="options1 me-4" id="HealthBeauty" onclick="ApiData('Health & Beauty')">Health
                                    &
                                    Beauty</div>
                                <div class="options1 me-4" id="Plants" onclick="ApiData('Plants')">Plants</div>
                                <div class="options1 " id="Other" onclick="ApiData('Other')">Other</div>
                            </div>
                        </div>
                        <div class="tab">
                            <h2 class="mt-2">
                                {{ trans('onboarding.question2') }}
                            </h2>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="options1 me-4" id="BrandWholesaler"
                                    onclick="ApiData('Brand or Wholesaler')">
                                    Brand / Wholesaler</div>
                                <div class="options1 me-4" id="Retailer" onclick="ApiData('Retailer')">Retailer</div>
                                <div class="options1 " id="AgencyConsultant" onclick="ApiData('Agency or Consultant')">
                                    Agency / Consultant </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="options1 me-4" id="SystemsIntegrator"
                                    onclick="ApiData('Systems Integrator')">
                                    Systems Integrator</div>
                                <div class="options1 me-4" id="Vendor" onclick="ApiData('Vendor')">Vendor</div>
                                <div class="options1 " id="Other" onclick="ApiData('Other')">Other</div>
                            </div>
                        </div>
                        <div class="tab">
                            <h2 class="">
                                {{ trans('onboarding.question3') }}
                            </h2>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="options1 me-4" id="" onclick="ApiData('0-100')">0-100</div>
                                <div class="options1 me-4" id="" onclick="ApiData('100-1,000')">100-1,000</div>
                                <div class="options1 me-4" id="" onclick="ApiData('1,000-10,000')">1,000-10,000 </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="options1 me-4" id="SystemsIntegrator" onclick="ApiData('10,000-50,000')">
                                    10,000-50,000</div>
                                <div class="options1  me-4" id="Vendor" onclick="ApiData('50,000+')">50,000+</div>
                                <div class="options1 me-4" style="opacity: 0"></div>
                            </div>

                        </div>
                        <div class="tab">
                            <h2 class="">
                                {{ trans('onboarding.question4') }}
                            </h2>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="options1 me-4" id="EcommerceManager" onclick="ApiData('Ecommerce Manager')">
                                    Ecommerce Manager</div>
                                <div class="options1 me-4" id="BrandManager" onclick="ApiData('Brand Manager')">Brand
                                    Manager
                                </div>
                                <div class="options1" id="SalesManager" onclick="ApiData('Sales Manager')">Sales Manager
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="options1 me-4" id="CEOOwner" onclick="ApiData('CEO / Owner')">CEO / Owner
                                </div>
                                <div class="options1 me-4" id="" onclick="ApiData('Other')">Other</div>
                                <div class="options1" style="opacity: 0"></div>
                            </div>
                        </div>
                        <div class="tab">
                            <h2>
                                {{ trans('onboarding.question5') }}
                            </h2>
                            <div class="d-flex flex-column align-items-center">
                                <div class="options1 w-100" id="EcommerceManager"
                                    onclick="ApiData('Organize and share product data with distributors')">Organize and
                                    share product data with distributors</div>
                                <div class="options1 w-100" id="BrandManager"
                                    onclick="ApiData('Receive real-time product data from suppliers')">Receive real-time
                                    product data from suppliers</div>
                                <div class="options1 w-100" id="SalesManager"
                                    onclick="ApiData('Sell products on multiple marketplaces')">Sell products on
                                    multiple marketplaces </div>
                                <div class="options1 w-100" id="" onclick="ApiData('Other')">Other </div>
                            </div>
                        </div>

                        <div id="submitBtn" class="float-end"><button type="button" class="clr-blue next-Btn"
                                id="nextBtn" onclick="nextPrev(1)">Skip for now</button></div>
                        <div id="loaderId" class=""></div>
                        <!-- Circles which indicates the steps of the form: -->
                        <div style="text-align:center;margin-top:40px;">
                            <span class="step"></span>
                            <span class="step"></span>
                            <span class="step"></span>
                            <span class="step"></span>
                            <span class="step"></span>
                        </div>
                    </form>


                </div>


            </div>
        </div>
    </div>

    <!--Right side-->
    <div id="Signup-page" class="col-4 d-xl-block d-none d-flex align-items-center onBoarding-rightSide">
        <!--Right Side-->
        <div class="d-lg-block d-none login-logo">
            <img src="{{ asset('media/boarding.png') }}" class="img-fluid max-h-70px w-75 image-css" alt="" />
        </div>

    </div>
</div>


@push("footer_scripts")
<script>
var optionsArray = [];
var uniqueChars = [];
var currentTab = 0;
var n = 0;
var widthValue = 0;
showTab(currentTab);
var elem = document.getElementById("progress");
var progress = document.getElementById("progress-text");
var widthAnim = widthValue;
var csrf = document.getElementById('csrf').value


function showTab(n) {
    n = n;
    var x = document.getElementsByClassName("tab");
    x[n].style.display = "block";
    if (n == 0) {
        document.getElementById("prevBtn").style.display = "none";
    } else {
        document.getElementById("prevBtn").style.display = "inline";
    }
    if (n == (x.length - 2)) {
        document.getElementById("title").innerHTML = "Role Details";
    } else {
        document.getElementById("title").innerHTML = "Company Information";
    }
    if (n == (x.length - 1)) {
        document.getElementById("nextBtn").innerHTML = "";

    } else {
        document.getElementById("nextBtn").innerHTML = "Skip for now";
    }
    fixStepIndicator(n)
}


function nextPrev(n) {
    var x = document.getElementsByClassName("tab");
    x[currentTab].style.display = "none";
    if (n == -1) {
        currentTab = currentTab + n;
        if (widthAnim > 0) {
            widthAnim = widthAnim - 20;
            elem.style.width = `${widthAnim}%`;
            progress.innerHTML = `${widthAnim}%`;
        }

    } else {
        currentTab = currentTab + n;
        optionsArray.push("0");
        var id = setInterval(frame, 0);
        var widthIncrement = 20;
        widthValue = widthAnim + widthIncrement;

        function frame() {
            if (widthAnim >= widthValue || widthValue > 100) {
                clearInterval(id);
            } else {
                widthAnim++;
                elem.style.width = widthAnim + '%';
                progress.innerHTML = widthAnim * 1 + '%';
            }
        }

    }

    if (currentTab >= x.length) {
        // document.getElementById("regForm").submit();
        return false;
    }
    showTab(currentTab);
}


function fixStepIndicator(n) {
    var i, x = document.getElementsByClassName("step");
    for (i = 0; i < x.length; i++) {
        x[i].className = x[i].className.replace(" active", "");
    }
    x[n].className += " active";
}


function ApiData(option) {
    var x = document.getElementsByClassName("tab");
    x[currentTab].style.display = "none";
    currentTab = currentTab + 1;
    var id = setInterval(frame, 0);
    updateContact(option);
    if (optionsArray.length < 5) {
        optionsArray.push(option);
        uniqueChars = optionsArray.filter((c, index) => {
            return optionsArray.indexOf(c) === index;
        });
    }

    var widthIncrement = 20;
    widthValue = widthAnim + widthIncrement;

    function frame() {
        if (widthAnim >= widthValue || widthValue > 100) {
            clearInterval(id);
        } else {
            widthAnim++;
            elem.style.width = widthAnim + '%';
            progress.innerHTML = widthAnim * 1 + '%';
        }
    }

    function updateContact(option) {
        var access_token = "s6B69M9EhVjjikWRMyMNRw";

        if (currentTab == 1) {
            fetch(`custom/fields/product_category/${option}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    "_token": csrf,
                    "key": "product_category",
                    "value": option
                })
            })

        }
        if (currentTab == 2) {
            fetch(`custom/fields/company_description/${option}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    "_token": csrf,
                    "key": "company_description",
                    "value": option
                })
            })

        }
        if (currentTab == 3) {
            fetch(`custom/fields/number_of_products/${option}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    "_token": csrf,
                    "key": "number_of_products",
                    "value": option
                })
            })

        }
        if (currentTab == 4) {
            fetch(`custom/fields/current_role/${option}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    "_token": csrf,
                    "key": "current_role",
                    "value": option
                })
            })

        }
        if (currentTab == 5) {
            var element = document.getElementById("loaderId");
            element.classList.add("loading");
            fetch(`custom/fields/purpose_for_pim/${option}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',

                    },
                    body: JSON.stringify({
                        "_token": csrf,
                        "key": "purpose_for_pim",
                        "value": option
                    })
                })
                .then(response => {
                    var element = document.getElementById("loaderId");
                    element.classList.remove("loading");
                })
                .catch(err => {
                    var element = document.getElementById("loaderId");
                    element.classList.remove("loading");
                })
        }
    }
    if (currentTab >= x.length) {
        // document.getElementById("regForm").submit();
        // currentTab=0;

        window.location.href = "/organization/";
        currentTab = 4;
        var x = document.getElementsByClassName("tab");
        x[currentTab].style.display = "none";
        widthAnim = 100;
        elem.style.width = `${widthAnim}%`;
        progress.innerHTML = `${widthAnim}%`;
        // return false;
    }
    showTab(currentTab);
}
</script>
@endpush
@endsection
