@extends('layouts.app' ,['sidebar_display'=>false])
@section('titles','Google Linking')
@section('content')

    <div class="row" style="height: 100%;">

        <!--Left side-->
        <div class="col-xs-12 col-md-12 col-sm-12 col-lg-12 align-content-center">
            <div class="flex-column-fluid d-flex flex-column p-10 text-center mt-2">
                <div class="row text-center justify-content-center" style="max-width: 900px; margin: 0 auto">
                    <!--Logo-->
                    <div class="d-flex flex-column flex-column-auto mt-3 d-lg-none d-block ">
                        <a href="{{env('APIMIO_URL')}}">
                            <img src="{{asset('media/logo.png')}}" alt="Logo">
                        </a>
                    </div>
                    <div class="col-12 col-sm-10 col-lg-12 col-xl-12 col-md-9 mt-3 page-width">
                        <div class="pb-4">
                            <div class="text-center mb-5">
                                <h3 class="text-center Poppins bold text-dark font-32">{{ trans('google_and_manual_linking.page_title') }}</h3>
                            </div>

                            <div class="d-flex justify-content-center mb-5">
                                <div class="form-group">
                                    <img src="{{asset('media/icons8-google-48.png')}}" alt="google">
                                    <img src="{{asset('media/icons8-link-100.png')}}" alt="link">
                                    <img src="{{asset('media/Apimio.jpg')}}" alt="logo" style="width: 53px">
                                </div>
                            </div>

                            <div class="d-flex justify-content-center mb-4 mt-4">
                                <div class="form-group">
                                    <div class="text text-center">
                                        {{ trans('google_and_manual_linking.already_linked') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <a href="#javascript:void(0)" class="form-control btn btn-primary" onclick="linkGoogle()">
                                {{ trans('google_and_manual_linking.continue_btn') }}
                            </a>
                            <form method="post" action="{{ route('link.user') }}" id="google_linking">
                                @csrf
                                <input type="hidden" value="{{$existingUser->id??0}}" name="id">
                                <input type="hidden" value="{{$data['password']??0}}" name="password">
                            </form>
                        </div>

                        <div class="form-group text-center">
                            <a href="{{route('home')}}"
                               class="font-text2 text-decoration-none">
                                {{ trans('google_and_manual_linking.cancel_btn') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('footer_scripts')
    <script>
        function linkGoogle() {
            $('#google_linking').submit();
        }
    </script>
@endpush
