@extends('layouts.app_new' ,['sidebar_display'=>false])
@section('titles','Login')
@section('content')
    @push('meta_tags')
        <meta name="title" content="Login to your Apimio account">
        <meta name="description" content="Log in to your Apimio account, the smart solution to all your product management problems.">
        <meta name="keywords" content="Login PIM, Apimio, Sign in, Connect, Integrate">
    @endpush
    <div class="row h-100">
        <!--Left side-->
        <div id="Signup-page" class="col-12 col-sm-12 col-md-12 col-lg-7">
            <!--Login Form-->
            <div class="row justify-content-center">
                <div class="col-12 col-sm-8 px-3 mt-5 pt-4 auth-screen-width">
                    <div class="d-flex flex-column flex-column-auto text-center">
                        <a href="{{env('APIMIO_URL')}}">
                            <img src="{{asset('media/logo.png')}}" class="h-40" alt="Logo">
                        </a>
                    </div>
                    <div class="pb-4">
                        <h1 class="text-center mt-4 mb-0">
                            {{ trans('login.page_title') }}
                        </h1>
                        <!--google signup button-->
                        <x-auth.google-button :link="route('google.auth')" id="login_google_btn"/>
                        <div class="d-flex mt-4 align-items-center">
                            <div class="border-top w-100 me-2"></div>
                            <span class="clr-grey">{{__('OR')}}</span>
                            <div class="border-top w-100 ms-2"></div>
                        </div>
                    </div>
                    <form id="login_login_form" class="formStyle" method="POST" action="{{ route('login') }}">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="email">
                                {{ trans('login.email') }}
                                &nbsp;<span class="text-danger">*</span>
                            </label>

                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email"
                                   placeholder="Email" name="email"
                                   value="{{ old('email', \Illuminate\Support\Facades\Request::get("email")) }}"
                                   required autofocus/>
                            @error('email')
                            <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                            @enderror
                        </div>
                        <div class="form-group mb-0">
                            <label>
                                {{ trans('login.password') }}
                                &nbsp;<span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror"
                                   id="pass" placeholder="*******"
                                   name="password" value="{{ old('password') }}" required/>
                            @error('password')
                            <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                            @enderror
                            <input type="hidden" class="form-control @error('role_id') is-invalid @enderror"
                                   placeholder="*******" name="role_id"
                                   value="{{Request::get('role')?((Request::get('role')=='vendor')?2:3):(old('role_id')) }}"
                                   required readonly/>
                        </div>
                        <div class="mt-1 text-end">
                            <small>
                                <a href="{{ route('password.request',['role'=>Request::get('role')]) }}"
                                   class="inline-block text-decoration-none Roboto forgot-text">
                                    {{ trans('login.forgot_pass') }}
                                </a>
                            </small>
                        </div>

                        <div class="form-group mt-40">

                            <button id="login_login_btn" type="submit"
                                    class="form-control btn btn-primary">
                                {{ trans('login.login_btn') }}
                            </button>
                        </div>
                    </form>
                    <div class="form-group text-center d-none">
                        <p class="mt-3">
                            {{ trans('login.terms_and_privacy') }}<br>
                            <a href="{{env('TERM_OF_SERVICE_URL')}}"
                               class="clr-black fw-700" target="_blank">
                                {{ trans('login.terms-of_service') }}
                            </a> {{ trans('login.and') }}
                            <a href="{{env('PRIVACY_POLICY_URL')}}"
                               class="clr-black fw-700" target="_blank">
                                {{ trans('login.privacy_policy') }}
                            </a>.
                        </p>
                    </div>
                    <div class="form-group text-center mt-3">
                        <label class="text-break text-dark fw-400">
                            {{ trans('login.create_account') }}&nbsp;
                        </label>

                        <a id="signup-btn"
                           href="{{ route('register',['role'=>Request::get('role')]) }}"
                           class="text-decoration-none ">
                            {{ trans('login.signup_btn') }}</a>
                    </div>
                </div>
            </div>
        </div>
        <!--Right side-->
        <div id="Signup-page" class="col-5 d-none d-lg-block onBoarding-rightSide ">
            <div class="d-lg-block d-none text-center login-logo mt-80">
                <img src="{{ asset('assets/images/signup.png') }}" class="w-75 mt-4" alt=""/>
            </div>
            <div class="mt-4">
                <div class="pb-5">
                    <h1 class="text-uppercase fw-700 text-center"><span>Healthy</span> <img src="{{ asset('assets/images/heading.png') }}" alt="" style="width: 30px;margin-top: -10px"><span> Parcel</span></h1>
                    <p class="fs-20 fw-600 text-center mb-1 review-text">
                        "Managing our product information has never been easier since we started using Apimio. We've seen a noticeable boost in customer satisfaction as a result."
                    </p>
                    <p class="text-center fs-20 mb-1">Kareena</p>
                    <p class="text-center clr-grey fs-20">CEO, Healthy Parcel</p>
                </div>
            </div>

        </div>
    </div>

@endsection

