<?php ?>
@extends('layouts.app_new')
@section('titles','Notification')
@section('content')
    @push('header_scripts')

    @endpush


    <div>
        <x-products.page-title name="{{trans('notification.page_title')}}" description=""
                               links="false" button="false">
        </x-products.page-title>

        <div class="row">
          <div class="col-12">
              <table id="example" class="table table-striped table-bordered" style="width:100%">
                  <thead>
                  <tr>
                      <th width="5%">S#</th>
                      <th width="35%">Message</th>
                      <th width="15%">Type</th>
                      <th width="10%">Status</th>
                      <th width="15%">Date & Time</th>
                      <th width="20%">Action</th>
                  </tr>
                  </thead>
                  <tbody>
                  @foreach($logs as $log)
                  <tr>
                      <td>{{$loop->iteration}}</td>
                      <td>{!! $log->description !!}</td>
                      <td>{{$log->type}}</td>
                      <td class="text-capitalize">{{$log->status}}</td>
                      <td>{{$log->created_at->format('d M Y H:i a')}}</td>
                      <td>
                          <a class="btn btn-primary" href="{{$log->link}}">
                              {{$log->link_text}}
                          </a>
                      </td>
                  </tr>
                  @endforeach

                  </tbody>
              </table>
          </div>
        </div>

    </div>
   

@endsection
@push('footer_scripts')
   <script>
       $(document).ready(function () {
           $('#example').DataTable();
       });
   </script>
@endpush
