import React, { useState } from "react";
import { Head } from "@inertiajs/react";
import { Table, Input, But<PERSON>, Drawer, Flex, Select, Space } from "antd";
import { DeleteOutlined, FilterOutlined, PlusOutlined } from "@ant-design/icons";
import { add } from "lodash";

const ProductListing = ({ org_id, batches_progress, products }) => {
    const columns = [
        {
            title: "Image",
            dataIndex: "image",
            key: "image",
        },
        {
            title: "Product Name",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "Product Identifier",
            dataIndex: "identifier",
            key: "identifier",
        },
        {
            title: "No. of Variants",
            dataIndex: "variants",
            key: "variants",
        },
        {
            title: "Status",
            dataIndex: "status",
            key: "status",
            render: (text, record) =>
                record.status === "Published" ? (
                    <span className="status badge-light status-publish">{record.status}</span>
                ) : (
                    <span className="status badge-light status-draft">{record.status}</span>
                ),
        },
        {
            title: "Product Quality Score",
            dataIndex: "score",
            key: "score",
        },
        {
            title: "Action",
            dataIndex: "action",
            key: "action",
        },
    ];
    const data = products.map((product, index) => ({
        key: index + 1,
        image: "", // Add your image URL or component here
        name: "", // Add your product name here
        identifier: product.sku,
        variants: "", // Add the number of variants here
        status: product.status === 1 ? "Published" : "Draft",
        score: "", // Add the product quality score here
        action: <DeleteOutlined />, // Add delete icon or button here
    }));
    const rowSelection = {
        onChange: (selectedRowKeys, selectedRows) => {
            console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
        },
        getCheckboxProps: (record) => ({
            disabled: record.name === "Disabled User",
            // Column configuration not to be checked
            name: record.name,
        }),
    };
    const [searchText, setSearchText] = useState("");
    const [filteredData, setFilteredData] = useState(data);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchText(value);
        const filtered = data.filter((item) => item.identifier.includes(value) || item.name.includes(value));
        setFilteredData(filtered);
    };

    const showDrawer = () => {
        setDrawerVisible(true);
    };

    const onClose = () => {
        setAddfilterdropdown(0);
        setDrawerVisible(false);
    };
    const [addfilterdropdown, setAddfilterdropdown] = useState(0);
    const [addfilterdropdownOptions, setAddfilterdropdownOptions] = useState(0);
    const handleAddFilter = () => {
        setAddfilterdropdown(1);
    };

    const [selectionType, setSelectionType] = useState("checkbox");
    const handleAttributeChange = (value) => {
        console.log(`selected ${value}`);

        setAddfilterdropdownOptions(1);
    };
    const handleOptionChange = (value) => {
        console.log(`selected ${value}`);
    };
    console.log("products listing:", products);

    return (
        <div>
            <Head title="Manage Products" />
            <div className="row row-cols-1 row-cols-lg-2">
                <div className="col d-flex flex-column align-items-start justify-content-center mb-4">
                    <h2 className="mb-0">Manage Products</h2>
                    <p className="mb-0">Add, update, and organize your product listings.</p>
                </div>
                <div className="col d-flex justify-content-lg-end justify-content-md-start mt-4 mt-lg-1 mt-1">
                    <Flex wrap gap="small">
                        <Button type="primary" href="/products/create" size="medium">
                            Add Product
                        </Button>
                        <Button type="default" href="/products/import/step1" size="medium">
                            Import CSV
                        </Button>
                        <Button type="default" href="/products/export/step1" size="medium">
                            Export CSV
                        </Button>
                    </Flex>
                </div>
            </div>
            <div className="row mb-3">
                <div className="col d-flex">
                    <Input placeholder="Search SKU or Product Name" value={searchText} onChange={handleSearch} style={{ width: 300 }} />
                    <Button type="default" icon={<FilterOutlined />} onClick={showDrawer} style={{ marginLeft: "auto" }}>
                        Filter
                    </Button>
                </div>
            </div>
            <div className="row">
                <div className="col-12">
                    <Table
                        rowSelection={{
                            type: selectionType,
                            ...rowSelection,
                        }}
                        columns={columns}
                        dataSource={filteredData}
                    />
                </div>
            </div>
            <Drawer title="Filter Products" placement="right" onClose={onClose} visible={drawerVisible}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddFilter} style={{ width: "100%" }} className="mb-8">
                    Add Filters
                </Button>
                {addfilterdropdown === 1 && (
                    <div>
                        <Select
                            className="mb-2"
                            defaultValue=""
                            style={{
                                width: "100%",
                            }}
                            onChange={handleAttributeChange}
                            options={[
                                {
                                    value: "",
                                    label: "Select Filter",
                                },
                                {
                                    value: "Product Name",
                                    label: "Product Name",
                                    type: "1",
                                },
                                {
                                    value: "Category",
                                    label: "Category",
                                    type: "4",
                                },
                                {
                                    value: "Brands",
                                    label: "Brands",
                                    type: "2",
                                },
                                {
                                    value: "Vendor",
                                    label: "Vendor",
                                    type: "3",
                                },
                            ]}
                        />
                    </div>
                )}
                {addfilterdropdownOptions === 1 && (
                    <>
                        <Select
                            defaultValue=""
                            style={{
                                width: "100%",
                            }}
                            onChange={handleOptionChange}
                            options={[
                                {
                                    value: "isdefined",
                                    label: "is defined",
                                },
                                {
                                    value: "is'tdefined",
                                    label: "isn't defined",
                                },
                                {
                                    value: "Containanyof ",
                                    label: "Contain any of ",
                                },
                                {
                                    value: "does'tcontainanyof",
                                    label: "does't contain any of",
                                },
                            ]}
                        />
                    </>
                )}
            </Drawer>
        </div>
    );
};
export default ProductListing;
