// src/components/LanguageSelect.jsx
import React from "react";
import { Select } from "antd";
import { languages } from "./LanguageOptions";

const { Option } = Select;

/**
 * Reusable language dropdown.
 * Props:
 *  - value       (string)       // controlled selected value
 *  - onChange    (func)         // handler (newValue) => void
 *  - ...rest     (any Select props you want to pass)
 */
export default function LanguageSelect({ value, onChange, ...rest }) {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="Select a language"
      showSearch
      optionFilterProp="children"
      filterOption={(input, option) =>
        option.children.toLowerCase().includes(input.toLowerCase())
      }
      {...rest}
    >
      {languages.map(({ value: code, label }) => (
        <Option key={code} value={code}>
          {label}
        </Option>
      ))}
    </Select>
  );
}
