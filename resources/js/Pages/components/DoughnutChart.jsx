import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Label } from "recharts";

const DoughnutChart = ({
    qualityType,
    scores,
    colors = ["#FE1F23", "#FF9C3E", "#15D476"],
    dataKeys = {
        first: "bad",
        second: "fair",
        third: "good",
    },
    centerLabel = {
        firstLine: qualityType,
        secondLine: "Quality",
    },
    startFromFirst = false,
}) => {
    // Check if all values are zero or missing
    const allZeroOrNull =
        !scores ||
        ((scores[dataKeys.first] === 0 || scores[dataKeys.first] == null) &&
            (scores[dataKeys.second] === 0 || scores[dataKeys.second] == null) &&
            (scores[dataKeys.third] === 0 || scores[dataKeys.third] == null));

    // Create data array based on the order needed
    let data;

    if (allZeroOrNull) {
        // Create equal placeholder segments when all values are zero
        data = [
            { name: "Placeholder1", value: 1 },
            { name: "Placeholder2", value: 1 },
            { name: "Placeholder3", value: 1 },
        ];
    } else {
        data = startFromFirst
            ? [
                  { name: "First", value: scores?.[dataKeys.first] || 0 },
                  { name: "Second", value: scores?.[dataKeys.second] || 0 },
                  { name: "Third", value: scores?.[dataKeys.third] || 0 },
              ]
            : [
                  { name: "Third", value: scores?.[dataKeys.third] || 0 },
                  { name: "Second", value: scores?.[dataKeys.second] || 0 },
                  { name: "First", value: scores?.[dataKeys.first] || 0 },
              ];
    }

    // Colors for empty state - using a lighter gray for all segments
    const emptyStateColors = ["#E0E0E0", "#EBEBEB", "#F5F5F5"];

    return (
        <PieChart width={150} height={150}>
            <Pie
                data={data}
                dataKey="value"
                innerRadius={55}
                outerRadius={70}
                startAngle={90}
                endAngle={450}
                paddingAngle={1} // Creates spacing between slices
                cornerRadius={8} // Rounds corners
            >
                {data.map((entry, index) => (
                    <Cell
                        key={`cell-${index}`}
                        fill={allZeroOrNull ? emptyStateColors[index] : colors[startFromFirst ? index : colors.length - 1 - index]}
                    />
                ))}
                {/* Custom Label for two lines */}
                <Label
                    content={({ viewBox }) => {
                        const { cx, cy } = viewBox;
                        return (
                            <text
                                x={cx}
                                y={cy}
                                textAnchor="middle"
                                dominantBaseline="middle"
                                style={{ fontSize: "16px", fontWeight: "bold", fill: "#333" }}
                            >
                                <tspan x={cx} dy="-10" className="text-[#252525] font-[600] text-[14px]">
                                    {centerLabel.firstLine}
                                </tspan>
                                <tspan x={cx} dy="20" className="text-[#252525] font-[600] text-[14px]">
                                    {centerLabel.secondLine}
                                </tspan>
                            </text>
                        );
                    }}
                />
            </Pie>
        </PieChart>
    );
};

export default DoughnutChart;
