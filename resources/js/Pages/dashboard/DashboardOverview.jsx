import React, { useState } from "react";
import { Row, Col } from "antd";
const DashboardOverview = ({ dashboardData }) => {
    console.log("overview:", dashboardData);
    const cards = [
        {
            index: 1,
            value: dashboardData?.data?.shopify_channel ? dashboardData?.data?.shopify_channel : 1,
            label: "Stores",
        },
        {
            index: 2,
            value: dashboardData?.data?.sku_count,
            label: "SKU's",
        },
        {
            index: 3,
            value: dashboardData?.data?.product_count,
            label: "Products",
        },
        {
            index: 4,
            value: dashboardData?.data?.brand_count,
            label: "Bands",
        },
        {
            index: 5,
            value: dashboardData?.data?.images,
            label: "Images",
        },
    ];
    return (
        <div className="p-5">
            <p className="text-[#252525] font-[600] text-[18px]">Overview</p>
            <p className="text-[#626262] font-normal text-[14px]">Total number of SKUs, Categories and Brands in your organization.</p>
            <div className="py-5">
                <Row className="flex gap-5">
                    {cards.map((card) => (
                        <Col flex="1" key={card.index}>
                            <div className="h-[168px] bg-[#F9FAFB] flex flex-col rounded-[12px] items-center justify-center">
                                <p className="text-[#740898] text-[40px] md:text-4xl font-[700]">{card.value}</p>
                                <p className="text-[#252525] text-[16px] font-[600]">{card.label}</p>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    );
};

export default DashboardOverview;
