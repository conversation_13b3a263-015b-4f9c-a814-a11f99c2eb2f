import React, { useState, useEffect } from "react";
import { Table, Input, Row, Col, message } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { get } from "../../../axios";

// Add CSS for overriding the blue outline/border on pagination buttons
const paginationOverrideStyles = `
    .ant-pagination li,
    .ant-pagination button,
    .ant-pagination .ant-pagination-item-link,
    .ant-pagination-item a {
        outline: none !important;
    }

    .ant-pagination li:focus,
    .ant-pagination li:focus-visible,
    .ant-pagination button:focus,
    .ant-pagination button:focus-visible,
    .ant-pagination-item-link:focus,
    .ant-pagination-item-link:focus-visible {
        outline: none !important;
        box-shadow: none !important;
        border-color: inherit;
    }
`;

const Locations = () => {
    // State for locations data
    const [locations, setLocations] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch locations data
    const fetchLocations = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            // This URL might need to be adjusted based on your actual API endpoint
            let endpoint = `/location?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);

            if (response && response.locations) {
                setLocations(response.locations || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch locations:", error);
            message.error("Failed to load locations");
        } finally {
            setLoading(false);
        }
    };

    // Load locations on component mount
    useEffect(() => {
        fetchLocations(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Handle search
    const handleSearch = (e) => {
        const searchValue = e.target.value;
        setSearchText(searchValue);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set a new timeout for the search (300ms debounce)
        const timeout = setTimeout(() => {
            fetchLocations(1, pagination.pageSize, searchValue);
        }, 300);

        setSearchTimeout(timeout);
    };

    // Table columns configuration
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-5">Location</span>,
            dataIndex: "name",
            key: "name",
            width: "25%",
            render: (text) => <span className="text-[#252525] pl-3">{text}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Address</span>,
            dataIndex: "address",
            key: "address",
            width: "35%",
            render: (text) => <span>{text || "-"}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">City</span>,
            dataIndex: "city",
            key: "city",
            width: "20%",
            render: (text) => <span>{text || "-"}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Type</span>,
            dataIndex: "default_location",
            key: "type",
            width: "20%",
            render: (isDefault) => <span>{isDefault ? "Default" : "Standard"}</span>,
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchLocations(newPagination.current, newPagination.pageSize, searchText);
    };

    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Locations</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">
                            Efficiently manage and organize your inventory across multiple locations.
                        </p>
                    </Col>
                </Row>
            </div>

            <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
                {/* Table Controls */}
                <div className="p-5">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-semibold text-[#252525]">Locations</h2>
                        </Col>
                        <Col>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search Location"
                                    prefix={<SearchOutlined />}
                                    value={searchText}
                                    onChange={handleSearch}
                                    className="w-60 rounded"
                                    allowClear
                                />
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* Locations Table */}
                <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                    <Table
                        columns={columns}
                        dataSource={locations}
                        rowKey="id"
                        pagination={{
                            ...pagination,
                            position: ["bottomRight"],
                            className: "mt-4 text-right pagination-no-focus-border",
                            itemRender: (page, type, originalElement) => {
                                if (type === "page") {
                                    return (
                                        <button
                                            size="small"
                                            className={`rounded outline-none focus:outline-none bg-white ${
                                                pagination.current === page
                                                    ? "text-[#740898] border-[#740898] shadow-none"
                                                    : "border-[#d9d9d9]"
                                            }`}
                                            style={{
                                                boxShadow: "none",
                                                margin: "0 4px",
                                            }}
                                        >
                                            {page}
                                        </button>
                                    );
                                }
                                return originalElement;
                            },
                        }}
                        loading={loading}
                        onChange={handleTableChange}
                        className="locations-table"
                        locale={{
                            emptyText: "No locations found",
                        }}
                        bordered={true}
                        size="middle"
                        components={{
                            header: {
                                cell: (props) => (
                                    <th
                                        {...props}
                                        style={{
                                            ...props.style,
                                            backgroundColor: "#F9FAFB",
                                            padding: 0,
                                            height: "48px",
                                        }}
                                    />
                                ),
                            },
                            body: {
                                row: (props) => (
                                    <tr
                                        {...props}
                                        style={{
                                            ...props.style,
                                            height: "48px",
                                        }}
                                    />
                                ),
                            },
                        }}
                    />
                </div>
            </div>
        </div>
    );
};

export default Locations;
