import React, { useState, useEffect } from "react";
import { Button, Input, Table, Form, Modal, message, Row, Col, Tooltip } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { get, post } from "../../../axios";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";

// Define CSS for pagination styles
const paginationOverrideStyles = `
    .pagination-no-focus-border .ant-pagination-item-link:focus,
    .pagination-no-focus-border .ant-pagination-item-link:hover,
    .pagination-no-focus-border .ant-select-selector:focus,
    .pagination-no-focus-border .ant-select-selector:hover,
    .pagination-no-focus-border .ant-pagination-options-quick-jumper input:focus,
    .pagination-no-focus-border .ant-pagination-options-quick-jumper input:hover {
        border-color: #d9d9d9 !important;
        outline: none !important;
        box-shadow: none !important;
    }
`;

const Brands = () => {
    // State for brands data
    const [brands, setBrands] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch brands data
    const fetchBrands = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            let endpoint = `brands?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            console.log("response", response);
            if (response) {
                setBrands(response.data || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch brands:", error);
            message.error("Failed to load brands");
        } finally {
            setLoading(false);
        }
    };

    // Fetch brand details by ID
    const fetchBrandDetails = async (brandId) => {
        try {
            setLoading(true);
            const id = brandId || null;
            if (!id) {
                message.error("Brand ID is required");
                return;
            }

            const result = await get(`brands/${id}`);
            return result;
        } catch (error) {
            console.error("Failed to fetch brand details:", error);
            message.error("Failed to load brand details");
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Load brands on component mount
    useEffect(() => {
        fetchBrands(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [brandToDelete, setBrandToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentBrand, setCurrentBrand] = useState(null);

    // Handle create brand
    const handleCreateBrand = () => {
        // Reset form and state
        form.resetFields();
        setIsEditMode(false);
        setCurrentBrand(null);

        // Open modal
        setIsModalVisible(true);
    };

    // Handle edit brand
    const handleEdit = async (record) => {
        try {
            const brandId = record.id;
            const brandDetails = await fetchBrandDetails(brandId);

            if (brandDetails) {
                // Store the entire brand object
                const brand = brandDetails.brand || brandDetails;

                setCurrentBrand(brand);
                setIsEditMode(true);

                // Set form values
                const formValues = {
                    name: brand.name,
                    is_default: brand.is_default ? 1 : 0,
                };

                form.setFieldsValue(formValues);
                setIsModalVisible(true);
            }
        } catch (error) {
            console.error("Error preparing brand for edit:", error);
            message.error("Failed to load brand for editing");
        }
    };

    // Open delete modal
    const openDeleteModal = (record) => {
        setBrandToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Handle delete brand
    const handleDelete = async () => {
        if (brandToDelete) {
            try {
                setLoading(true);
                const brandId = brandToDelete.id;

                if (!brandId) {
                    message.error("Failed to delete brand: Missing brand ID");
                    return;
                }

                // Make the delete API call using post with _method: DELETE (Laravel convention)
                const response = await post(`brands/${brandId}`, {
                    _method: "DELETE",
                });

                if (response) {
                    message.success(`Brand "${brandToDelete.name}" deleted successfully`);
                    fetchBrands(pagination.current, pagination.pageSize, searchText); // Refresh data after delete
                }

                setIsDeleteModalVisible(false);
                setBrandToDelete(null);
            } catch (error) {
                console.error("Failed to delete brand:", error);

                // Display appropriate error message
                if (error.response && error.response.data && error.response.data.message) {
                    message.error(`Failed to delete brand: ${error.response.data.message}`);
                } else {
                    message.error("Failed to delete brand. Please try again.");
                }
            } finally {
                setLoading(false);
            }
        }
    };

    // Handle delete modal cancel
    const handleDeleteCancel = () => {
        setIsDeleteModalVisible(false);
        setBrandToDelete(null);
    };

    // Handle search
    const handleSearch = (e) => {
        const searchValue = e.target.value;
        setSearchText(searchValue);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set a new timeout for the search (300ms debounce)
        const timeout = setTimeout(() => {
            fetchBrands(1, pagination.pageSize, searchValue);
        }, 300);

        setSearchTimeout(timeout);
    };

    // Handle modal submit
    const handleModalSubmit = async () => {
        try {
            const values = await form.validateFields();

            setLoading(true);

            const formData = {
                name: values.name,
                is_default: values.is_default || 0,
            };

            let response;

            if (isEditMode && currentBrand) {
                // Update existing brand
                formData.id = currentBrand.id;
                response = await post(`brands/${currentBrand.id}`, {
                    ...formData,
                    _method: "PUT",
                });

                if (response) {
                    message.success(`Brand "${values.name}" updated successfully`);
                }
            } else {
                // Create new brand
                response = await post("brands", formData);

                if (response) {
                    message.success(`Brand "${values.name}" created successfully`);
                }
            }

            // Reset and close
            setIsModalVisible(false);
            form.resetFields();

            // Refresh the list
            fetchBrands(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Failed to save brand:", error);

            // Display appropriate error message
            if (error.response && error.response.data && error.response.data.message) {
                message.error(`Failed to save brand: ${error.response.data.message}`);
            } else if (error.response && error.response.data && error.response.data.errors) {
                // Handle validation errors
                const errors = error.response.data.errors;
                const errorMessages = Object.values(errors).flat();
                errorMessages.forEach((errorMessage) => {
                    message.error(errorMessage);
                });
            } else {
                message.error("Failed to save brand. Please try again.");
            }
        } finally {
            setLoading(false);
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
    };

    // Table columns
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] pl-3">Name</span>,
            dataIndex: "name",
            key: "name",
            width: "90%",
            render: (text) => <span className="pl-3">{text}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] pl-2">Action</span>,
            key: "action",
            width: "10%",
            render: (_, record) => (
                <div className="flex gap-1">
                    {
                        <>
                            <Tooltip title="Delete">
                                <Button
                                    type="text"
                                    icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                    onClick={() => openDeleteModal(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                            <Tooltip title="Edit">
                                <Button
                                    type="text"
                                    icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                    onClick={() => handleEdit(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                        </>
                    }
                </div>
            ),
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchBrands(newPagination.current, newPagination.pageSize, searchText);
    };

    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Brands</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">Create and manage your product brands.</p>
                    </Col>
                    <Col>
                        <Button type="primary" className="bg-[#740898] border-[#740898] rounded font-medium" onClick={handleCreateBrand}>
                            Create Brand
                        </Button>
                    </Col>
                </Row>
            </div>

            <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
                {/* Table Controls */}
                <div className="p-5">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-semibold text-[#252525]">Brands</h2>
                        </Col>
                        <Col>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search by brand name"
                                    prefix={<SearchOutlined />}
                                    value={searchText}
                                    onChange={handleSearch}
                                    className="w-60 rounded"
                                    allowClear
                                />
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* Brands Table */}
                <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                    <Table
                        columns={columns}
                        dataSource={brands}
                        rowKey="id"
                        pagination={{
                            ...pagination,
                            position: ["bottomRight"],
                            className: "mt-4 text-right pagination-no-focus-border",
                            itemRender: (page, type, originalElement) => {
                                if (type === "page") {
                                    return (
                                        <Button
                                            size="small"
                                            className={`rounded outline-none focus:outline-none bg-white ${
                                                pagination.current === page
                                                    ? "text-[#740898] border-[#740898] shadow-none"
                                                    : "border-[#d9d9d9]"
                                            }`}
                                            style={{
                                                boxShadow: "none",
                                                margin: "0 4px",
                                            }}
                                        >
                                            {page}
                                        </Button>
                                    );
                                }
                                return originalElement;
                            },
                        }}
                        loading={loading}
                        onChange={handleTableChange}
                        className="brands-table"
                        locale={{
                            emptyText: "No brands found",
                        }}
                        bordered={true}
                        size="middle"
                    />
                </div>
            </div>

            {/* Brand Form Modal */}
            <Modal
                title={isEditMode ? "Edit Brand" : "Create Brand"}
                open={isModalVisible}
                onCancel={handleModalCancel}
                centered
                footer={[
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        loading={loading}
                        onClick={handleModalSubmit}
                        className="bg-[#740898] hover:bg-[#580573]"
                    >
                        {isEditMode ? "Update" : "Create"}
                    </Button>,
                ]}
                width={600}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="brand_form"
                    initialValues={{
                        is_default: 0,
                    }}
                >
                    <Form.Item
                        name="name"
                        label="Brand Name"
                        rules={[
                            {
                                required: true,
                                message: "Please enter a brand name",
                            },
                        ]}
                    >
                        <Input placeholder="Enter brand name" />
                    </Form.Item>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title="Delete Brand"
                open={isDeleteModalVisible}
                onCancel={handleDeleteCancel}
                footer={[
                    <Button key="cancel" onClick={handleDeleteCancel}>
                        Cancel
                    </Button>,
                    <Button key="delete" type="primary" danger loading={loading} onClick={handleDelete}>
                        Delete
                    </Button>,
                ]}
            >
                <p>
                    Are you sure you want to delete the brand "<strong>{brandToDelete?.name}</strong>"? This action cannot be undone.
                </p>
            </Modal>
        </div>
    );
};

export default Brands;
