import React, { useState, useEffect } from "react";
import { Table, Input, But<PERSON>, Row, Col, Tooltip, Popconfirm, message, Modal, Form, Select, Space } from "antd";
import { SearchOutlined, FilterOutlined, DeleteOutlined, EditOutlined } from "@ant-design/icons";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";
import { get, post, destroy } from "../../../axios";

// Add CSS for overriding the blue outline/border on pagination buttons
const paginationOverrideStyles = `
    .ant-pagination li,
    .ant-pagination button,
    .ant-pagination .ant-pagination-item-link,
    .ant-pagination-item a {
        outline: none !important;
    }

    .ant-pagination li:focus,
    .ant-pagination li:focus-visible,
    .ant-pagination button:focus,
    .ant-pagination button:focus-visible,
    .ant-pagination-item-link:focus,
    .ant-pagination-item-link:focus-visible {
        outline: none !important;
        box-shadow: none !important;
        border-color: inherit;
    }
`;

const Languages = () => {
    // State for languages data
    const [languages, setLanguages] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for form visibility and editing
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [editingLanguage, setEditingLanguage] = useState(null);
    const [form] = Form.useForm();

    // State for delete modal
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [languageToDelete, setLanguageToDelete] = useState(null);

    // Currency options for dropdown
    const currencyOptions = [
        { value: "USD", label: "USD" },
        { value: "EUR", label: "EUR" },
        { value: "GBP", label: "GBP" },
        { value: "JPY", label: "JPY" },
        { value: "CAD", label: "CAD" },
        // Add more currencies as needed
    ];

    // Separator options for dropdown
    const separatorOptions = [
        { value: ".", label: "Point ( . )" },
        { value: ",", label: "Comma ( , )" },
    ];

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch languages data
    const fetchLanguages = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            let endpoint = `versions?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            if (response) {
                setLanguages(response.data || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch languages:", error);
            message.error("Failed to load languages");
        } finally {
            setLoading(false);
        }
    };

    // Initial data load
    useEffect(() => {
        fetchLanguages();
    }, []);

    // Create new language
    const handleCreateLanguage = () => {
        setEditingLanguage(null);
        form.resetFields();
        setIsModalVisible(true);
    };

    // Edit language
    const handleEdit = async (record) => {
        setEditingLanguage(record);
        form.setFieldsValue({
            name: record.name,
            currency: record.currency,
            separator: record.separator,
        });
        setIsModalVisible(true);
    };

    // Open delete confirmation modal
    const openDeleteModal = (record) => {
        setLanguageToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Delete language
    const handleDelete = async () => {
        if (!languageToDelete) return;

        try {
            const response = await destroy(`versions/${languageToDelete.id}`);
            message.success("Language deleted successfully");
            fetchLanguages(pagination.current, pagination.pageSize, searchText);
            setIsDeleteModalVisible(false);
            setLanguageToDelete(null);
        } catch (error) {
            console.error("Error deleting language:", error);
            message.error(error.response?.data?.message || "An error occurred while deleting the language");
        }
    };

    // Cancel delete
    const handleDeleteCancel = () => {
        setIsDeleteModalVisible(false);
        setLanguageToDelete(null);
    };

    // Handle search input change with debounce
    const handleSearch = (e) => {
        const { value } = e.target;
        setSearchText(value);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set a new timeout
        const timeout = setTimeout(() => {
            fetchLanguages(1, pagination.pageSize, value);
        }, 500);

        setSearchTimeout(timeout);
    };

    // Submit form for create/edit
    const handleModalSubmit = async () => {
        try {
            const values = await form.validateFields();

            const payload = {
                name: values.name,
                currency: values.currency,
                separator: values.separator,
            };

            let response;
            if (editingLanguage) {
                // Update existing language
                response = await post(`versions/update/${editingLanguage.id}`, payload);
            } else {
                // Create new language
                response = await post("versions", payload);
            }

            console.log("API Response:", response); // Debug log

            // Check if response exists and is valid
            if (response) {
                message.success(`Language ${editingLanguage ? "updated" : "created"} successfully`);
                setIsModalVisible(false);
                fetchLanguages(pagination.current, pagination.pageSize, searchText);
            } else {
                message.error(`Failed to ${editingLanguage ? "update" : "create"} language`);
            }
        } catch (error) {
            console.error(`Error ${editingLanguage ? "updating" : "creating"} language:`, error);
            message.error(`An error occurred while ${editingLanguage ? "updating" : "creating"} the language`);
        }
    };

    // Cancel form modal
    const handleModalCancel = () => {
        setIsModalVisible(false);
        setEditingLanguage(null);
        form.resetFields();
    };

    // Table columns definition
    const columns = [
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
            render: (text) => <span className="text-sm font-medium">{text}</span>,
        },
        {
            title: "Currency",
            dataIndex: "currency",
            key: "currency",
            render: (text) => <span className="text-sm">{text}</span>,
        },

        {
            title: "Action",
            key: "action",
            width: 100,
            render: (_, record) => (
                <div className="flex items-center gap-2">
                    {
                        <>
                            <Tooltip title="Delete">
                                <Button
                                    type="text"
                                    icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                    onClick={() => openDeleteModal(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                            <Tooltip title="Edit">
                                <Button
                                    type="text"
                                    icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                    onClick={() => handleEdit(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                        </>
                    }
                </div>
            ),
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchLanguages(newPagination.current, newPagination.pageSize, searchText);
    };

    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Languages</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">Add and configure languages to cater to a global audience.</p>
                    </Col>
                    <Col>
                        <Button type="primary" className="bg-[#740898] border-[#740898] rounded font-medium" onClick={handleCreateLanguage}>
                            Create Language
                        </Button>
                    </Col>
                </Row>
            </div>

            <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
                {/* Table Controls */}
                <div className="p-5">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-semibold text-[#252525]">Languages</h2>
                        </Col>
                        <Col>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search Language"
                                    prefix={<SearchOutlined />}
                                    value={searchText}
                                    onChange={handleSearch}
                                    className="w-60 rounded"
                                    allowClear
                                />
                                <Button icon={<FilterOutlined />} className="rounded border-[#DBDBDB]">
                                    Filter
                                </Button>
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* Languages Table */}
                <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                    <Table
                        columns={columns}
                        dataSource={languages}
                        rowKey="id"
                        pagination={{
                            ...pagination,
                            position: ["bottomRight"],
                            className: "mt-4 text-right pagination-no-focus-border",
                            itemRender: (page, type, originalElement) => {
                                if (type === "page") {
                                    return (
                                        <Button
                                            size="small"
                                            className={`rounded outline-none focus:outline-none bg-white ${
                                                pagination.current === page
                                                    ? "text-[#740898] border-[#740898] shadow-none"
                                                    : "border-[#d9d9d9]"
                                            }`}
                                            style={{
                                                boxShadow: "none",
                                                margin: "0 4px",
                                            }}
                                        >
                                            {page}
                                        </Button>
                                    );
                                }
                                return originalElement;
                            },
                        }}
                        loading={loading}
                        onChange={handleTableChange}
                        className="languages-table"
                    />
                </div>
            </div>

            {/* Create/Edit Language Modal */}
            <Modal
                title={editingLanguage ? "Edit Language" : "Create Language"}
                open={isModalVisible}
                onCancel={handleModalCancel}
                centered
                footer={[
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        className="bg-[#740898] hover:bg-[#8A2BE2] border-[#740898]"
                        onClick={handleModalSubmit}
                    >
                        {editingLanguage ? "Update" : "Create"}
                    </Button>,
                ]}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="languageForm"
                    initialValues={{
                        name: "",
                        currency: "USD",
                        separator: ".",
                    }}
                >
                    <Form.Item name="name" label="Language Name" rules={[{ required: true, message: "Please enter language name" }]}>
                        <Input placeholder="Enter language name" />
                    </Form.Item>

                    <Form.Item name="currency" label="Currency" rules={[{ required: true, message: "Please select currency" }]}>
                        <Select placeholder="Select currency" options={currencyOptions} />
                    </Form.Item>

                    <Form.Item name="separator" label="Decimal Separator" rules={[{ required: true, message: "Please select separator" }]}>
                        <Select placeholder="Select separator" options={separatorOptions} />
                    </Form.Item>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title="Delete Language"
                open={isDeleteModalVisible}
                onCancel={handleDeleteCancel}
                footer={[
                    <Button key="cancel" onClick={handleDeleteCancel}>
                        Cancel
                    </Button>,
                    <Button key="delete" type="primary" danger onClick={handleDelete}>
                        Delete
                    </Button>,
                ]}
            >
                <p>Are you sure you want to delete this language? This action cannot be undone.</p>
                {languageToDelete && (
                    <p>
                        <strong>Language:</strong> {languageToDelete.name}
                    </p>
                )}
            </Modal>
        </div>
    );
};

export default Languages;
