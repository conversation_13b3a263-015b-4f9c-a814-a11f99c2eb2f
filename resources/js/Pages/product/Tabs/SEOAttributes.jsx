import React, { useState } from "react";
import { Input, Select, Tag } from "antd";

const { Option } = Select;
const { TextArea } = Input;

const SEOAttributes = ({ product, onDataChange }) => {
    const [urlSlug, setUrlSlug] = useState("");
    const [seoTitle, setSeoTitle] = useState("");
    const [seoDescription, setSeoDescription] = useState("");
    const [tags, setTags] = useState([]);
    const [inputVisible, setInputVisible] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [brand, setBrand] = useState("Brand 1"); // Default value for brand
    const [vendor, setVendor] = useState("Vendor 1"); // Default value for vendor
    const [category, setCategory] = useState([]); // State for multi-select category
    const [store, setStore] = useState([]); // State for multi-select store

    const handleClose = (removedTag) => {
        const newTags = tags.filter((tag) => tag !== removedTag);
        setTags(newTags);
        onDataChange?.();
    };

    const handleInputConfirm = () => {
        if (inputValue && !tags.includes(inputValue)) {
            setTags([...tags, inputValue]);
            onDataChange?.();
        }
        setInputVisible(false);
        setInputValue("");
    };

    const handleUrlSlugChange = (e) => {
        setUrlSlug(e.target.value);
        onDataChange?.();
    };

    const handleSeoTitleChange = (e) => {
        setSeoTitle(e.target.value);
        onDataChange?.();
    };

    const handleSeoDescriptionChange = (e) => {
        setSeoDescription(e.target.value);
        onDataChange?.();
    };

    const handleBrandChange = (value) => {
        setBrand(value);
        onDataChange?.();
    };

    const handleVendorChange = (value) => {
        setVendor(value);
        onDataChange?.();
    };

    const handleCategoryChange = (value) => {
        setCategory(value);
        onDataChange?.();
    };

    const handleStoreChange = (value) => {
        setStore(value);
        onDataChange?.();
    };

    const handleInputChange = (e) => {
        setInputValue(e.target.value);
    };

    const showInput = () => {
        setInputVisible(true);
    };

    return (
        <div className="flex flex-col 2xl:flex-row p-5">
            {/* Left Column (Full width on smaller screens, 70% on 2xl) */}
            <div className="w-full 2xl:w-7/12 2xl:pr-4 mb-4 2xl:mb-0">
                {/* URL Slug Field */}
                <div className="mb-4">
                    <label className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${urlSlug ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">URL Slug</span>
                    </label>
                    <Input value={urlSlug} onChange={handleUrlSlugChange} placeholder="Enter URL Slug" required />
                </div>

                {/* SEO Title Field */}
                <div className="mb-4">
                    <label className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${seoTitle ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">SEO Title</span>
                    </label>
                    <Input value={seoTitle} onChange={handleSeoTitleChange} placeholder="Enter SEO Title" />
                </div>

                {/* SEO Description Field */}
                <div className="mb-4">
                    <label className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${seoDescription ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">SEO Description</span>
                    </label>
                    <TextArea value={seoDescription} onChange={handleSeoDescriptionChange} placeholder="Enter SEO Description" rows={4} />
                </div>

                {/* Tags Section */}
                <div className="mb-4">
                    <label className="flex items-center mb-2">
                        <span className={`w-2 h-2 rounded-full mr-2 ${tags.length > 0 ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">Tags</span>
                    </label>
                    <div className="flex flex-wrap gap-2 border border-[#DBDBDB] rounded-[4px] p-4">
                        {tags.map((tag) => (
                            <span
                                key={tag}
                                className="px-[12px] py-[8.5px] bg-[#F1E6F5] text-[#740898] font-normal text-[14px] rounded-[8px] flex items-center"
                            >
                                {tag}
                                <span className="ml-1 cursor-pointer text-black text-2xl" onClick={() => handleClose(tag)}>
                                    ×
                                </span>
                            </span>
                        ))}
                        {!inputVisible && (
                            <Input
                                type="text"
                                size="small"
                                className="w-[200px] border-0 hover:border-0 focus:border-0 focus:shadow-none focus:outline-none"
                                placeholder="Add a tag"
                                onPressEnter={handleInputConfirm}
                                value={inputValue}
                                onChange={handleInputChange}
                                onBlur={handleInputConfirm}
                            />
                        )}
                    </div>
                </div>
            </div>

            {/* Right Column (Full width on smaller screens, 30% on 2xl) */}
            <div className="w-full 2xl:w-5/12 2xl:pl-4">
                {/* Language Dropdown */}
                <div className="mb-4">
                    <Select value="EN-US" style={{ backgroundColor: "#FCF5FF" }}>
                        <Option value="EN-US">EN-US</Option>
                        <Option value="EN-UK">EN-UK</Option>
                        <Option value="FR-FR">FR-FR</Option>
                    </Select>
                </div>

                {/* Dropdown Fields Container */}
                <div className="border border-[#DBDBDB] rounded-[4px] p-4">
                    {/* Brand Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${brand ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Brand</span>
                        </label>
                        <Select value={brand} onChange={handleBrandChange} style={{ width: "100%" }}>
                            <Option value="Brand 1">Brand 1</Option>
                            <Option value="Brand 2">Brand 2</Option>
                        </Select>
                    </div>

                    {/* Vendor Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${vendor ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Vendor</span>
                        </label>
                        <Select value={vendor} onChange={handleVendorChange} style={{ width: "100%" }}>
                            <Option value="Vendor 1">Vendor 1</Option>
                            <Option value="Vendor 2">Vendor 2</Option>
                        </Select>
                    </div>

                    {/* Category Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${category.length > 0 ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Category</span>
                        </label>
                        <Select mode="multiple" value={category} onChange={handleCategoryChange} style={{ width: "100%" }}>
                            <Option value="Category 1">Category 1</Option>
                            <Option value="Category 2">Category 2</Option>
                        </Select>
                        {category.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                                {category.map((cat) => (
                                    <span
                                        key={cat}
                                        className="px-[12px] py-[8.5px] bg-[#F1E6F5] text-[#740898] font-semibold text-[14px] rounded-[8px]"
                                    >
                                        {cat}
                                    </span>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Store Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${store.length > 0 ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Store</span>
                        </label>
                        <Select mode="multiple" value={store} onChange={handleStoreChange} style={{ width: "100%" }}>
                            <Option value="Store 1">Store 1</Option>
                            <Option value="Store 2">Store 2</Option>
                        </Select>
                        {store.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                                {store.map((st) => (
                                    <span
                                        key={st}
                                        className="px-[12px] py-[8.5px] bg-[#F1E6F5] text-[#740898] font-semibold text-[14px] rounded-[8px]"
                                    >
                                        {st}
                                    </span>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SEOAttributes;
