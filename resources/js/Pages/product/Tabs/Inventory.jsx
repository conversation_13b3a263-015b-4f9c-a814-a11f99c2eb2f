import React, { useState } from "react";
import { Select, Checkbox, Input } from "antd";

const { Option } = Select;

// Updated variants data structure to include store assignments
const variantsByStore = {
    store1: [
        { size: "Small", sku: "SF9702-S", location: "Store 1 Warehouse", quantity: 1095 },
        { size: "Medium", sku: "SF9702-M", location: "Store 1 Warehouse", quantity: 800 },
        { size: "Large", sku: "SF9702-L", location: "Store 1 Warehouse", quantity: 600 },
        { size: "Extra Large", sku: "SF9702-XL", location: "Store 1 Warehouse", quantity: 400 },
    ],
    store2: [
        { size: "Small", sku: "SF9702-S", location: "Store 2 Warehouse", quantity: 1000 },
        { size: "Medium", sku: "SF9702-M", location: "Store 2 Warehouse", quantity: 700 },
        { size: "Large", sku: "SF9702-L", location: "Store 2 Warehouse", quantity: 600 },
        { size: "Extra Large", sku: "SF9702-XL", location: "Store 2 Warehouse", quantity: 400 },
    ],
};

const Inventory = ({ product, onDataChange }) => {
    const [selectedStore, setSelectedStore] = useState("store1");
    const [selectedVariant, setSelectedVariant] = useState(variantsByStore.store1[0]);
    const [trackQuantity, setTrackQuantity] = useState(true);
    const [continueSellingOutOfStock, setContinueSellingOutOfStock] = useState(false);

    const stores = [
        { value: "store1", label: "Store 1" },
        { value: "store2", label: "Store 2" },
    ];

    const handleQuantityChange = (value, variant) => {
        // Update quantity for specific store and variant
        const updatedVariants = [...variantsByStore[selectedStore]];
        const variantIndex = updatedVariants.findIndex((v) => v.sku === variant.sku);
        if (variantIndex !== -1) {
            updatedVariants[variantIndex] = { ...variant, quantity: parseInt(value) };
            variantsByStore[selectedStore] = updatedVariants;
        }
        onDataChange?.();
    };

    const handleStoreChange = (value) => {
        setSelectedStore(value);
        setSelectedVariant(variantsByStore[value][0]); // Select first variant of new store
        onDataChange?.();
    };

    const handleTrackQuantityChange = (e) => {
        setTrackQuantity(e.target.checked);
        onDataChange?.();
    };

    const handleContinueSellingChange = (e) => {
        setContinueSellingOutOfStock(e.target.checked);
        onDataChange?.();
    };

    const handleVariantSelect = (variant) => {
        setSelectedVariant(variant);
        onDataChange?.();
    };

    return (
        <div className="flex flex-col text-[#252525] p-5">
            <p className="text-[#252525] mb-4">Please update the inventory stock quantities for each location.</p>
            {/* Store Selection */}
            <div className="mb-6">
                <label className="block mb-2 font-semibold text-[14px] text-[#252525]">Select a Store:</label>
                <Select
                    value={selectedStore}
                    placeholder="Store Name"
                    style={{ width: "660px" }}
                    onChange={handleStoreChange}
                    className="text-[#252525]"
                >
                    {stores.map((store) => (
                        <Option key={store.value} value={store.value} className="text-[#252525]">
                            {store.label}
                        </Option>
                    ))}
                </Select>
            </div>

            <div className="border border-[#DBDBDB] rounded-[12px] p-0">
                <div className="mb-0 border-b border-[#DBDBDB] p-4">
                    <h2 className="text-lg font-semibold text-[#252525]">Rome-court-shorts-black-grey</h2>
                </div>

                {/* Main Content */}
                <div className="flex gap-6">
                    {/* Variants List (20%) */}
                    <div className="w-[204px]">
                        <div className="flex flex-col gap-0 border-r border-[#DBDBDB]">
                            {variantsByStore[selectedStore].map((variant) => (
                                <div
                                    key={variant.sku}
                                    onClick={() => handleVariantSelect(variant)}
                                    className={`cursor-pointer p-3 flex items-center font-normal gap-2 border-t border-[#DBDBDB] ${
                                        selectedVariant?.sku === variant.sku
                                            ? "bg-[#740898] text-[#fff]"
                                            : "hover:bg-gray-50 text-[#252525]"
                                    }`}
                                >
                                    <div className="w-6 h-6 bg-gray-200 rounded-md"></div>
                                    <span className="text-[14px] font-normal">{variant.size}</span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Table Section (80%) */}
                    <div className="w-[80%]">
                        {/* Checkboxes */}
                        <div className="mb-4 flex gap-6 p-3">
                            <Checkbox checked={trackQuantity} onChange={handleTrackQuantityChange} className="text-[#252525]">
                                <span className="text-[#252525]">Track quantity</span>
                            </Checkbox>
                            <Checkbox checked={continueSellingOutOfStock} onChange={handleContinueSellingChange} className="text-[#252525]">
                                <span className="text-[#252525]">Continue selling when out of stock</span>
                            </Checkbox>
                        </div>

                        {/* Table */}
                        <div className="border border-[#DBDBDB] border-b-0 overflow-hidden m-2">
                            <table className="w-full border-collapse">
                                <thead className="bg-[#F9FAFB]">
                                    <tr className="border-b border-[#DBDBDB]">
                                        <th className="px-4 py-3 text-left text-sm font-semibold text-[#252525] border-r border-[#DBDBDB]">
                                            SKU
                                        </th>
                                        <th className="px-4 py-3 text-left text-sm font-semibold text-[#252525] border-r border-[#DBDBDB]">
                                            Location
                                        </th>
                                        <th className="px-4 py-3 text-left text-sm font-semibold text-[#252525]">Quantity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {selectedVariant && (
                                        <tr className="border-b border-[#DBDBDB]">
                                            <td className="px-4 py-3 text-sm text-[#252525] border-r border-[#DBDBDB]">
                                                {selectedVariant.sku}
                                            </td>
                                            <td className="px-4 py-3 text-sm text-[#252525] border-r border-[#DBDBDB]">
                                                {selectedVariant.location}
                                            </td>
                                            <td className="px-4 py-3 text-sm text-[#252525]">
                                                <Input
                                                    type="number"
                                                    value={selectedVariant.quantity}
                                                    onChange={(e) => handleQuantityChange(e.target.value, selectedVariant)}
                                                    style={{ width: "100px" }}
                                                    className="text-[#252525]"
                                                />
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Inventory;
