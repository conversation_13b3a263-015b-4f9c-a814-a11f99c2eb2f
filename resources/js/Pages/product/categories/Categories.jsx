import React, { useState, useEffect } from "react";
import { Table, Input, Button, Row, Col, Tooltip, Popconfirm, message, Modal, Form, Checkbox, Select, Space } from "antd";
import { SearchOutlined, FilterOutlined, DeleteOutlined, EditOutlined, DownOutlined, RightOutlined } from "@ant-design/icons";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";
import CategoryArrow from "../../../../../public/v2/icons/category-arrow.svg";
import { get, post } from "../../../axios";

// Add CSS for overriding the blue outline/border on pagination buttons
const paginationOverrideStyles = `
    .ant-pagination li,
    .ant-pagination button,
    .ant-pagination .ant-pagination-item-link,
    .ant-pagination-item a {
        outline: none !important;
    }

    .ant-pagination li:focus,
    .ant-pagination li:focus-visible,
    .ant-pagination button:focus,
    .ant-pagination button:focus-visible,
    .ant-pagination-item-link:focus,
    .ant-pagination-item-link:focus-visible {
        outline: none !important;
        box-shadow: none !important;
        border-color: inherit;
    }
`;

const Categories = () => {
    // State for categories data
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for expanded row keys
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);
    const [isSearching, setIsSearching] = useState(false);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch categories data
    const fetchCategories = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            setIsSearching(!!searchQuery);
            let endpoint = `categories?tree_structure=true&page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);

            console.log("response", response);
            if (response) {
                setCategories(response.data);

                // Extract all expanded keys (for initial expanded state)
                const extractKeys = (items) => {
                    let keys = [];
                    items.forEach((item) => {
                        if (item.children && item.children.length > 0) {
                            keys.push(item.id);
                            keys = [...keys, ...extractKeys(item.children)];
                        }
                    });
                    return keys;
                };

                setExpandedRowKeys(extractKeys(response.data));

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch categories:", error);
            message.error("Failed to load categories");
        } finally {
            setLoading(false);
        }
    };

    // Fetch category details by ID
    const fetchCategoryDetails = async (categoryId) => {
        try {
            setLoading(true);
            const id = categoryId || null;
            if (!id) {
                message.error("Category ID is required");
                return;
            }

            const result = await get(`categories/${id}`);
            console.log("Category details:", result);
            return result;
        } catch (error) {
            console.error("Failed to fetch category details:", error);
            message.error("Failed to load category details");
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Load categories on component mount
    useEffect(() => {
        fetchCategories(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [categoryToDelete, setCategoryToDelete] = useState(null);
    const [form] = Form.useForm();
    const [assignParent, setAssignParent] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentCategory, setCurrentCategory] = useState(null);

    // Handle row expansion
    const handleExpand = (expanded, record) => {
        if (expanded) {
            setExpandedRowKeys([...expandedRowKeys, record.id]);
        } else {
            setExpandedRowKeys(expandedRowKeys.filter((key) => key !== record.id));
        }
    };

    // Handle create category
    const handleCreateCategory = () => {
        // Reset form and state
        form.resetFields();
        setAssignParent(false);
        setIsEditMode(false);
        setCurrentCategory(null);

        // Open modal
        setIsModalVisible(true);
    };

    // Handle edit category
    const handleEdit = async (record) => {
        try {
            const categoryId = record.id;
            console.log("Editing category with ID:", categoryId);
            const categoryDetails = await fetchCategoryDetails(categoryId);
            console.log("Received category details:", categoryDetails);

            if (categoryDetails) {
                // Store the entire category object
                const category = categoryDetails.category || categoryDetails;
                console.log("Using category object:", category);

                setCurrentCategory(category);
                setIsEditMode(true);

                // Set form values
                const formValues = {
                    categoryName: category.name,
                    description: category.description || "",
                };

                // Set parent category if exists
                if (category.parent_id || category.category_id) {
                    setAssignParent(true);
                    formValues.assignParent = true;
                    formValues.parentCategory = category.parent_id || category.category_id;
                    console.log("Setting parent category:", formValues.parentCategory);
                } else {
                    setAssignParent(false);
                }

                console.log("Setting form values:", formValues);
                form.setFieldsValue(formValues);

                setIsModalVisible(true);
            }
        } catch (error) {
            console.error("Error preparing category for edit:", error);
            message.error("Failed to load category for editing");
        }
    };

    // Open delete modal
    const openDeleteModal = (record) => {
        setCategoryToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Handle delete category
    const handleDelete = async () => {
        if (categoryToDelete) {
            try {
                setLoading(true);
                // Get the category ID
                const categoryId = categoryToDelete.id;

                if (!categoryId) {
                    message.error("Failed to delete category: Missing category ID");
                    return;
                }

                // Make the delete API call using post with _method: DELETE (Laravel convention)
                const response = await post(`categories/${categoryId}`, {
                    _method: "DELETE",
                });

                if (response) {
                    message.success(`Category "${categoryToDelete.name}" deleted successfully`);
                    fetchCategories(); // Refresh data after delete
                }

                setIsDeleteModalVisible(false);
                setCategoryToDelete(null);
            } catch (error) {
                console.error("Failed to delete category:", error);

                // Display appropriate error message
                if (error.response && error.response.data && error.response.data.message) {
                    message.error(`Failed to delete category: ${error.response.data.message}`);
                } else {
                    message.error("Failed to delete category. Please try again.");
                }
            } finally {
                setLoading(false);
            }
        }
    };

    // Handle delete modal cancel
    const handleDeleteCancel = () => {
        setIsDeleteModalVisible(false);
        setCategoryToDelete(null);
    };

    // Handle search
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchText(value);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // If search is cleared immediately fetch all results
        if (value === "") {
            setIsSearching(false);
            fetchCategories(1, pagination.pageSize, "");
            return;
        }

        // Set a new timeout to debounce the search request
        const timeoutId = setTimeout(() => {
            // Reset to first page when searching
            setIsSearching(!!value);
            fetchCategories(1, pagination.pageSize, value);
        }, 500); // 500ms debounce

        setSearchTimeout(timeoutId);
    };

    // Handle modal submit
    const handleModalSubmit = async () => {
        try {
            const values = await form.validateFields();

            // Prepare category data
            const categoryData = {
                name: values.categoryName,
                description: values.description || "",
            };

            // Add category_id if parent category is assigned
            if (values.assignParent && values.parentCategory) {
                categoryData.category_id = values.parentCategory;
            }

            let response;

            if (isEditMode && currentCategory) {
                // Update existing category
                console.log("Updating category with data:", categoryData);
                const categoryId = currentCategory.id || currentCategory.value;
                response = await post(`categories/${categoryId}`, {
                    ...categoryData,
                    _method: "PUT", // Laravel convention for PUT requests
                });
                console.log("Category update response:", response);
                message.success(`Category "${values.categoryName}" updated successfully`);
            } else {
                // Create new category
                console.log("Creating category with data:", categoryData);
                response = await post("categories", categoryData);
                console.log("Category creation response:", response);
                message.success(`Category "${values.categoryName}" created successfully`);
            }

            fetchCategories(); // Refresh data after creation/update
            setIsModalVisible(false);
            form.resetFields();
            setAssignParent(false);
            setIsEditMode(false);
            setCurrentCategory(null);
        } catch (error) {
            console.error("Failed to save category:", error);

            // Display appropriate error message
            if (error.response && error.response.data && error.response.data.message) {
                message.error(`Failed to save category: ${error.response.data.message}`);
            } else {
                message.error("Failed to save category. Please try again.");
            }
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setAssignParent(false);
        setIsEditMode(false);
        setCurrentCategory(null);
    };

    // Handle parent category assignment checkbox
    const handleAssignParentChange = (e) => {
        setAssignParent(e.target.checked);
    };

    // Define table columns
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-5">Name</span>,
            dataIndex: "name",
            key: "name",
            width: "90%",
        },
        {
            title: <span className="text-[#626262] font-[400] pl-4">Action</span>,
            key: "action",
            render: (_, record) => (
                <div className="flex gap-1">
                    <Tooltip title="Delete">
                        <Button
                            type="text"
                            icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                            onClick={() => openDeleteModal(record)}
                            className="p-0"
                        />
                    </Tooltip>
                    <Tooltip title="Edit">
                        <Button
                            type="text"
                            icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                            onClick={() => handleEdit(record)}
                            className="p-0"
                        />
                    </Tooltip>
                </div>
            ),
        },
    ];

    // Custom expand icon
    const expandIcon = ({ expanded, onExpand, record }) => {
        if (record.children && record.children.length > 0) {
            return expanded ? (
                <img
                    onClick={(e) => onExpand(record, e)}
                    src={CategoryArrow}
                    className="inline rotate-90 mr-1 text-black font-bold cursor-pointer ml-2"
                />
            ) : (
                <img
                    onClick={(e) => onExpand(record, e)}
                    src={CategoryArrow}
                    className="inline  ml-2 mr-1 text-black font-bold cursor-pointer"
                />
            );
        }
        return <span className="w-[18px] inline-block"></span>;
    };

    // Flat list of categories for parent selection dropdown
    const flattenCategories = (cats, prefix = "") => {
        let options = [];
        cats.forEach((category) => {
            options.push({
                value: category.id,
                label: prefix + category.name,
            });

            if (category.children && category.children.length > 0) {
                options = [...options, ...flattenCategories(category.children, prefix + "→ ")];
            }
        });
        return options;
    };

    const categoryOptions = flattenCategories(categories);

    // Custom row class name function to give parent rows a different background
    const getRowClassName = (record) => {
        // Check if the record has children (making it a parent category)
        return record.children && record.children.length > 0 ? "bg-[#F9FAFB]" : "";
    };
    console.log("currentCategory", currentCategory);
    return (
        <div className="p-0 rounded-lg">
            {/* Inject CSS to override pagination styles */}
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Categories</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">
                            Organize your products with a flexible, tree-based category system.
                        </p>
                    </Col>
                    <Col>
                        <Button type="primary" className="bg-[#740898] border-[#740898] rounded font-medium" onClick={handleCreateCategory}>
                            Create Category
                        </Button>
                    </Col>
                </Row>
            </div>

            <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px] ">
                {/* Table Controls */}
                <div className="p-5 ">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-semibold text-[#252525]">Categories</h2>
                        </Col>
                        <Col>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search Category"
                                    prefix={<SearchOutlined />}
                                    value={searchText}
                                    onChange={handleSearch}
                                    className="w-60 rounded"
                                    allowClear
                                    onPressEnter={() => fetchCategories(1, pagination.pageSize, searchText)}
                                />
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* Categories Table */}
                <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                    {isSearching && searchText && (
                        <div className="p-3 bg-[#F9FAFB] border-b border-[#DBDBDB]">
                            <span className="text-[#252525]">
                                Search results for: <strong>{searchText}</strong>
                            </span>
                            <Button
                                type="link"
                                onClick={() => {
                                    setSearchText("");
                                    setIsSearching(false);
                                    fetchCategories(1, pagination.pageSize, "");
                                }}
                                className="text-[#740898] ml-2 p-0"
                            >
                                Clear
                            </Button>
                        </div>
                    )}
                    <Table
                        columns={columns}
                        dataSource={categories}
                        loading={loading}
                        rowKey="id"
                        expandable={{
                            expandIcon: expandIcon,
                            expandedRowKeys: expandedRowKeys,
                            onExpand: handleExpand,
                        }}
                        bordered={true}
                        pagination={{
                            ...pagination,
                            position: ["bottomRight"],
                            className: "mt-4 text-right pagination-no-focus-border",
                            itemRender: (page, type, originalElement) => {
                                if (type === "page") {
                                    return (
                                        <Button
                                            size="small"
                                            className={`rounded outline-none focus:outline-none bg-white ${
                                                pagination.current === page
                                                    ? " text-[#740898] border-[#740898] shadow-none"
                                                    : " border-[#d9d9d9]"
                                            }`}
                                            style={{
                                                boxShadow: "none",
                                                margin: "0 4px",
                                            }}
                                        >
                                            {page}
                                        </Button>
                                    );
                                }
                                return originalElement;
                            },
                        }}
                        onChange={(newPagination) => {
                            console.log("Pagination changed:", newPagination);
                            // Fetch data with new pagination parameters and current search
                            fetchCategories(newPagination.current, newPagination.pageSize, searchText);
                        }}
                        rowClassName={getRowClassName}
                        size="middle"
                        components={{
                            header: {
                                cell: (props) => (
                                    <th
                                        {...props}
                                        style={{
                                            ...props.style,
                                            backgroundColor: "#F9FAFB",
                                            padding: 0,
                                            height: "48px",
                                        }}
                                    />
                                ),
                            },
                            body: {
                                row: (props) => (
                                    <tr
                                        {...props}
                                        style={{
                                            ...props.style,
                                            height: "48px",
                                        }}
                                    />
                                ),
                            },
                        }}
                    />
                </div>
            </div>

            {/* Create Category Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">{isEditMode ? "Edit Category" : "Create a New Category"}</div>}
                open={isModalVisible}
                onCancel={handleModalCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={640}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                <Form form={form} layout="vertical" requiredMark={false}>
                    <Form.Item
                        name="categoryName"
                        label={<span className="font-semibold text-[#252525] block mb-0">Category Name*</span>}
                        rules={[
                            {
                                required: true,
                                message: "Please enter a category name",
                            },
                        ]}
                    >
                        <Input placeholder="Enter Name" className="rounded-sm h-8" />
                    </Form.Item>

                    <Form.Item name="description" label={<span className="font-semibold text-[#252525] block mb-0">Description</span>}>
                        <Input.TextArea
                            placeholder="Enter Description"
                            className="rounded-sm min-h-[120px] resize-none"
                            autoSize={{ minRows: 4, maxRows: 6 }}
                        />
                    </Form.Item>

                    <Form.Item name="assignParent" valuePropName="checked" className="mb-4 mt-2 text-[#740898]">
                        <Checkbox onChange={handleAssignParentChange} className="text-sm">
                            Assign Parent Category
                        </Checkbox>
                    </Form.Item>

                    {assignParent && (
                        <Form.Item
                            name="parentCategory"
                            label={<span className="font-semibold text-[#252525] block mb-0">Parent Category</span>}
                        >
                            <Select
                                placeholder="Select"
                                className="rounded-sm h-8"
                                options={categoryOptions}
                                suffixIcon={<DownOutlined className="text-gray-500 text-xs" />}
                                showSearch
                                optionFilterProp="label"
                            />
                        </Form.Item>
                    )}

                    <Form.Item className="m-0 text-right">
                        <Space size="middle">
                            <Button onClick={handleModalCancel} className="rounded min-w-20">
                                Cancel
                            </Button>
                            <Button type="primary" onClick={handleModalSubmit} className="bg-[#740898] border-[#740898] rounded min-w-20">
                                {isEditMode ? "Update" : "Save"}
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">Delete Category</div>}
                open={isDeleteModalVisible}
                onCancel={handleDeleteCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={480}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                <div className="mb-6">
                    <p className="text-[#252525]">
                        Are you sure you want to delete the category "{categoryToDelete?.name}"?
                        {categoryToDelete?.children?.length > 0 && (
                            <span className="block mt-2 text-red-500">
                                Warning: This category has {categoryToDelete.children.length} subcategories that will also be deleted.
                            </span>
                        )}
                    </p>
                </div>

                <div className="text-right">
                    <Space size="middle">
                        <Button onClick={handleDeleteCancel} className="rounded min-w-20">
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            danger
                            onClick={handleDelete}
                            className="rounded min-w-20 bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700"
                        >
                            Delete
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
};

export default Categories;
