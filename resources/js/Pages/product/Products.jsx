import React from "react";
import ProductsLayout from "./ProductsLayout";
import AllProducts from "./AllProducts";
import CSVImport from "./import/CSVImport";
import Categories from "./categories/Categories";
import AttributeSets from "./attribute-sets/AttributeSets";
import ManageAttributes from "./attributes/ManageAttributes";
import VariantOptions from "./variant-options/VariantOptions";
import Brands from "./brands/Brands";
import Vendors from "./vendors/Vendors";
import Locations from "./locations/Locations";
import Stores from "./stores/Stores";
import Languages from "./languages/Languages";
// This component is used as an Inertia.js page for the products section
const Products = ({ tab = "all-products", initialProduct = null, productId = null }) => {
    let TabContent;

    // Create a mapping of tab names to human-readable titles
    const tabTitles = {
        "all-products": "All Products",
        importcsv: "Import CSV",
        export: "Export Products",
        categories: "Categories",
        "attribute-sets": "Attribute Sets",
        attribute: "Attributes",
        "variant-options": "Variant Options",
        brands: "Brands",
        vendors: "Vendors",
        languages: "Languages",
        locations: "Locations",
        stores: "Stores",
    };

    // Get the appropriate title based on the current tab
    const pageTitle = tabTitles[tab] || "Products";

    // Determine which tab content to render based on the tab prop
    switch (tab) {
        case "all-products":
            TabContent = () => <AllProducts initialProduct={initialProduct} productId={productId} />;
            break;
        case "importcsv":
            TabContent = CSVImport;
            break;
        case "categories":
            TabContent = Categories;
            break;
        case "attribute-sets":
            TabContent = AttributeSets;
            break;
        case "attribute":
            TabContent = ManageAttributes;
            break;
        case "variant-options":
            TabContent = VariantOptions;
            break;
        case "brands":
            TabContent = Brands;
            break;
        case "vendors":
            TabContent = Vendors;
            break;
        case "locations":
            TabContent = Locations;
            break;
        case "stores":
            TabContent = Stores;
            break;
        case "languages":
            TabContent = Languages;
            break;
        // Add other cases as they become available
        default:
            TabContent = () => <div>Content for {tab} tab</div>;
    }

    return (
        <ProductsLayout title={pageTitle}>
            <TabContent />
        </ProductsLayout>
    );
};

export default Products;
