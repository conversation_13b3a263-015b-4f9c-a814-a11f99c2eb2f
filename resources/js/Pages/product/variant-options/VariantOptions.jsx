import React, { useState, useEffect } from "react";
import { Button, Input, Table, Row, Col, Form, Modal, message, Tooltip } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { get, post, put, destroy } from "../../../axios";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";

// CSS override for pagination styling
const paginationOverrideStyles = `
    .pagination-no-focus-border .ant-pagination-item-link:focus,
    .pagination-no-focus-border .ant-pagination-item-link:hover,
    .pagination-no-focus-border .ant-pagination-item:focus,
    .pagination-no-focus-border .ant-pagination-item:hover {
        border-color: #740898 !important;
    }
`;

const VariantOptions = () => {
    // State for variant options data
    const [variantOptions, setVariantOptions] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch variant options data
    const fetchVariantOptions = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            let endpoint = `variant-options?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            if (response) {
                setVariantOptions(response.variantOptions || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch variant options:", error);
            message.error("Failed to load variant options");
        } finally {
            setLoading(false);
        }
    };

    // Fetch variant option details by ID
    const fetchVariantOptionDetails = async (variantOptionId) => {
        try {
            setLoading(true);
            const id = variantOptionId || null;
            if (!id) {
                message.error("Variant Option ID is required");
                return;
            }

            const result = await get(`variant-options/${id}`);
            return result;
        } catch (error) {
            console.error("Failed to fetch variant option details:", error);
            message.error("Failed to load variant option details");
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Load variant options on component mount
    useEffect(() => {
        fetchVariantOptions(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [variantOptionToDelete, setVariantOptionToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentVariantOption, setCurrentVariantOption] = useState(null);

    // Handle create variant option
    const handleCreateVariantOption = () => {
        // Reset form and state
        form.resetFields();
        setIsEditMode(false);
        setCurrentVariantOption(null);

        // Open modal
        setIsModalVisible(true);
    };

    // Handle edit variant option
    const handleEdit = async (record) => {
        try {
            const variantOptionId = record.id;
            const variantOptionDetails = await fetchVariantOptionDetails(variantOptionId);

            if (variantOptionDetails) {
                // Store the entire variant option object
                const variantOption = variantOptionDetails.variantOption || variantOptionDetails;

                setCurrentVariantOption(variantOption);
                setIsEditMode(true);

                // Set form values
                const formValues = {
                    name: variantOption.name,
                    values: variantOption.values?.map((value) => value.name) || [],
                };

                form.setFieldsValue(formValues);
                setIsModalVisible(true);
            }
        } catch (error) {
            console.error("Error preparing variant option for edit:", error);
            message.error("Failed to load variant option for editing");
        }
    };

    // Open delete modal
    const openDeleteModal = (record) => {
        setVariantOptionToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Handle delete variant option
    const handleDelete = async () => {
        if (!variantOptionToDelete) return;

        try {
            setLoading(true);
            await destroy(`variant-options/${variantOptionToDelete.id}`);
            message.success("Variant option deleted successfully");
            setIsDeleteModalVisible(false);
            fetchVariantOptions(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Failed to delete variant option:", error);
            message.error("Failed to delete variant option");
        } finally {
            setLoading(false);
        }
    };

    // Handle delete modal cancel
    const handleDeleteModalCancel = () => {
        setIsDeleteModalVisible(false);
        setVariantOptionToDelete(null);
    };

    // Handle search input change
    const handleSearch = (e) => {
        const query = e.target.value;
        setSearchText(query);

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set new timeout for debounce
        const timeout = setTimeout(() => {
            fetchVariantOptions(1, pagination.pageSize, query);
        }, 500);

        setSearchTimeout(timeout);
    };

    // Handle form submission
    const handleSubmit = async (values) => {
        try {
            setLoading(true);

            const formData = {
                name: values.name,
                values: values.values?.map((value) => ({ name: value })) || [],
            };

            let response;
            if (isEditMode && currentVariantOption) {
                // Update existing variant option
                response = await put(`variant-options/${currentVariantOption.id}`, formData);
                message.success("Variant option updated successfully");
            } else {
                // Create new variant option
                response = await post("variant-options", formData);
                message.success("Variant option created successfully");
            }

            // Close modal and refresh data
            setIsModalVisible(false);
            fetchVariantOptions(pagination.current, pagination.pageSize, searchText);
            form.resetFields();
            setIsEditMode(false);
            setCurrentVariantOption(null);
        } catch (error) {
            console.error("Form submission error:", error);

            if (error.errorFields) {
                // Form validation error
                return;
            }

            message.error("Failed to save variant option. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setIsEditMode(false);
        setCurrentVariantOption(null);
    };

    // Table columns configuration
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-5">Name</span>,
            dataIndex: "name",
            key: "name",
            width: "30%",
            render: (text) => <span className="text-[#252525] pl-3">{text}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Values</span>,
            dataIndex: "values",
            key: "values",
            width: "60%",
            render: (values) => (
                <span>
                    {values && values.length > 0
                        ? values
                              .slice(0, 5)
                              .map((val) => val.name)
                              .join(", ") + (values.length > 5 ? "..." : "")
                        : "No values"}
                </span>
            ),
        },
        {
            title: <span className="text-[#626262] font-[400] pl-4">Action</span>,
            key: "action",
            width: "10%",
            render: (_, record) => (
                <div className="flex gap-1">
                    {
                        <>
                            <Tooltip title="Delete">
                                <Button
                                    type="text"
                                    icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                    onClick={() => openDeleteModal(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                            <Tooltip title="Edit">
                                <Button
                                    type="text"
                                    icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                    onClick={() => handleEdit(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                        </>
                    }
                </div>
            ),
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchVariantOptions(newPagination.current, newPagination.pageSize, searchText);
    };

    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Variant Options</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">Organize your product variant options for easier management.</p>
                    </Col>
                    <Col>
                        <Button
                            type="primary"
                            className="bg-[#740898] border-[#740898] rounded font-medium"
                            onClick={handleCreateVariantOption}
                        >
                            Create Variant Option
                        </Button>
                    </Col>
                </Row>
            </div>

            <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
                {/* Table Controls */}
                <div className="p-5">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-semibold text-[#252525]">Variant Options</h2>
                        </Col>
                        <Col>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search Variant Option"
                                    prefix={<SearchOutlined />}
                                    value={searchText}
                                    onChange={handleSearch}
                                    className="w-60 rounded"
                                    allowClear
                                />
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* Variant Options Table */}
                <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                    <Table
                        columns={columns}
                        dataSource={variantOptions}
                        rowKey="id"
                        pagination={{
                            ...pagination,
                            position: ["bottomRight"],
                            className: "mt-4 text-right pagination-no-focus-border",
                            itemRender: (page, type, originalElement) => {
                                if (type === "page") {
                                    return (
                                        <Button
                                            size="small"
                                            className={`rounded outline-none focus:outline-none bg-white ${
                                                pagination.current === page
                                                    ? "text-[#740898] border-[#740898] shadow-none"
                                                    : "border-[#d9d9d9]"
                                            }`}
                                            style={{
                                                boxShadow: "none",
                                                margin: "0 4px",
                                            }}
                                        >
                                            {page}
                                        </Button>
                                    );
                                }
                                return originalElement;
                            },
                        }}
                        loading={loading}
                        onChange={handleTableChange}
                        className="variant-options-table"
                        locale={{
                            emptyText: "No variant options found",
                        }}
                        bordered={true}
                        size="middle"
                    />
                </div>
            </div>

            {/* Create/Edit Modal */}
            <Modal
                title={isEditMode ? "Edit Variant Option" : "Create Variant Option"}
                open={isModalVisible}
                onCancel={handleModalCancel}
                footer={null}
                width={600}
                centered
            >
                <Form form={form} layout="vertical" onFinish={handleSubmit}>
                    <Form.Item
                        name="name"
                        label="Variant Option Name"
                        rules={[{ required: true, message: "Please enter variant option name" }]}
                    >
                        <Input placeholder="Enter name (e.g., Size, Color)" />
                    </Form.Item>

                    <Form.Item
                        name="values"
                        label="Variant Option Values"
                        rules={[{ required: true, message: "Please enter at least one value" }]}
                    >
                        <Form.List name="values">
                            {(fields, { add, remove }) => (
                                <>
                                    {fields.map((field) => (
                                        <div key={field.key} style={{ display: "flex", marginBottom: 8 }}>
                                            <Form.Item
                                                {...field}
                                                rules={[{ required: true, message: "Please enter a value" }]}
                                                style={{ marginBottom: 0, flex: 1 }}
                                            >
                                                <Input placeholder="Enter value (e.g., Small, Red)" />
                                            </Form.Item>
                                            <Button onClick={() => remove(field.name)} style={{ marginLeft: 8 }} danger>
                                                Remove
                                            </Button>
                                        </div>
                                    ))}
                                    <Form.Item>
                                        <Button type="dashed" onClick={() => add()} block className="mt-2" icon={<span>+</span>}>
                                            Add Value
                                        </Button>
                                    </Form.Item>
                                </>
                            )}
                        </Form.List>
                    </Form.Item>

                    <div className="flex justify-end gap-2 mt-4">
                        <Button onClick={handleModalCancel}>Cancel</Button>
                        <Button type="primary" htmlType="submit" loading={loading} className="bg-[#740898] border-[#740898]">
                            {isEditMode ? "Update" : "Create"}
                        </Button>
                    </div>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title="Delete Variant Option"
                open={isDeleteModalVisible}
                onCancel={handleDeleteModalCancel}
                footer={[
                    <Button key="cancel" onClick={handleDeleteModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="delete" type="primary" danger onClick={handleDelete} loading={loading}>
                        Delete
                    </Button>,
                ]}
            >
                <p>
                    Are you sure you want to delete this variant option?
                    {variantOptionToDelete && <strong> "{variantOptionToDelete.name}"</strong>}
                </p>
                <p>This action cannot be undone.</p>
            </Modal>
        </div>
    );
};

export default VariantOptions;
