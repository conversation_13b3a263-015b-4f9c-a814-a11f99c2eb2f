import React, { useState, useEffect, useRef } from "react";
import MainContainer from "./MainContainer"; // Adjust the import path as necessary
import { Button, Progress, Modal, Spin, message } from "antd"; // Import message from antd
import { LeftOutlined, RightOutlined, DeleteOutlined } from "@ant-design/icons"; // Import Ant Design icons
import { Link } from "@inertiajs/react"; // Import Inertia Link
import Doughnut<PERSON>hart from "../components/DoughnutChart"; // Import DoughnutChart component
import ProductImage from "../../../../public/v2/images/product-img.png";
import { get, put } from "../../axios"; // Import put from axios
import DeleteIcon from "../../../../public/v2/icons/delete-icon.svg";

// Import Tab Components
import GeneralAttributes from "./Tabs/GeneralAttributes";
import SEOAttributes from "./Tabs/SEOAttributes";
import Inventory from "./Tabs/Inventory";
import CustomAttributes from "./Tabs/CustomAttributes";
import Media from "./Tabs/Media";
import Variants from "./Tabs/Variants";
import Stores from "./Tabs/Stores";
import ProductQualityScore from "./Tabs/ProductQualityScore";

const defaultScores = {
    good: 750,
    fair: 130,
    bad: 120,
};

const SingleProduct = ({ productId }) => {
    const [product, setProduct] = useState(null);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState("general attributes"); // State to manage active tab
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [nextTab, setNextTab] = useState(null);
    const [scrollPosition, setScrollPosition] = useState(0);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(false);
    const tabsContainerRef = useRef(null);
    const [saving, setSaving] = useState(false);
    const [formData, setFormData] = useState({
        sku: "",
        barcode: "",
        weight: "",
        weight_unit: "oz",
        price: "",
        compare_at_price: "",
        cost_price: "",
        brand: [],
        vendor: [],
        category: "",
        channels: [],
        product_name: "",
        description: "",
        status: "active",
    });

    // Add scroll event listener
    useEffect(() => {
        const handleScroll = () => {
            setScrollPosition(window.scrollY);
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    // Add scroll check effect
    useEffect(() => {
        const checkScroll = () => {
            if (tabsContainerRef.current) {
                const { scrollLeft, scrollWidth, clientWidth } = tabsContainerRef.current;
                setCanScrollLeft(scrollLeft < scrollWidth - clientWidth);
                setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
            }
        };

        checkScroll();

        // Add scroll event listener to the tabs container
        const tabsContainer = tabsContainerRef.current;
        if (tabsContainer) {
            tabsContainer.addEventListener("scroll", checkScroll);
        }

        window.addEventListener("resize", checkScroll);

        return () => {
            window.removeEventListener("resize", checkScroll);
            if (tabsContainer) {
                tabsContainer.removeEventListener("scroll", checkScroll);
            }
        };
    }, []);

    // Fetch product data when component mounts or productId changes
    useEffect(() => {
        const fetchProductData = async () => {
            try {
                setLoading(true);
                const response = await get(`products/${productId}`);
                setProduct(response.product);
            } catch (error) {
                console.error("Error fetching product:", error);
            } finally {
                setLoading(false);
            }
        };

        if (productId) {
            fetchProductData();
        }
    }, [productId]);

    // Add media query check
    const [isWideScreen, setIsWideScreen] = useState(window.innerWidth >= 1920);

    useEffect(() => {
        const handleResize = () => {
            setIsWideScreen(window.innerWidth >= 1920);
        };

        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    const handleTabChange = (tab) => {
        if (hasUnsavedChanges) {
            setNextTab(tab);
            setIsModalVisible(true);
        } else {
            setActiveTab(tab);
        }
    };

    const handleModalOk = () => {
        setIsModalVisible(false);
        if (nextTab) {
            setActiveTab(nextTab);
            setNextTab(null);
            setHasUnsavedChanges(false);
        }
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
        setNextTab(null);
    };

    const handleDataChange = (data) => {
        setFormData((prev) => ({ ...prev, ...data }));
        setHasUnsavedChanges(true);
    };

    const handleSave = async () => {
        try {
            setSaving(true);

            // Prepare the payload using formData instead of product state
            const payload = {
                sku: formData.sku || "",
                version_id: formData.version_id || product.version_id || "",
                attribute: {
                    37: formData.product_name || "",
                    38: formData.description || "",
                },
                variant_sku: product.variant_sku || [],
                barcode: formData.barcode || "",
                weight_unit: formData.weight_unit || "oz",
                weight: formData.weight || "0",
                price: formData.price?.toString() || "0",
                compare_at_price: formData.compare_at_price?.toString() || "0",
                cost_price: formData.cost_price?.toString() || "0",
                variants: JSON.stringify(product.variants || []),
                brand: formData.brand.map((id) => ({ id: id.toString() })) || [],
                vendor: formData.vendor.map((id) => ({ id: id.toString() })) || [],
                category: formData.category || "", // This will be a comma-separated string of category IDs
                channels: formData.channels.map((id) => ({ id: id.toString() })) || [],
            };

            // Log formData and payload for debugging
            console.log("Current formData:", formData);
            console.log("Payload being sent:", payload);

            // Make the API call
            await put(`/products/${productId}`, payload);

            // Show success message
            message.success("Product saved successfully");

            // Reset unsaved changes state
            setHasUnsavedChanges(false);

            // Close modal if open
            setIsModalVisible(false);

            // Refresh product data
            const response = await get(`products/${productId}`);
            setProduct(response.product);
        } catch (error) {
            console.error("Error saving product:", error);
            message.error("Failed to save product. Please try again.");
        } finally {
            setSaving(false);
        }
    };

    const scrollTabs = (direction) => {
        if (tabsContainerRef.current) {
            const scrollAmount = 200;
            tabsContainerRef.current.scrollBy({
                left: direction === "left" ? -scrollAmount : scrollAmount,
                behavior: "smooth",
            });
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Spin size="large" />
            </div>
        );
    }

    const productHandle = product?.product_name || product?.sku || "Product Name";
    const productStatus = product?.status === "Active" ? "Active" : "Draft";
    const progressPercentage = product?.version_score || 80;
    const validFieldsCount = 10; // Example count for valid fields
    const invalidFieldsCount = 2; // Example count for invalid fields

    const renderTabContent = () => {
        const commonProps = {
            product,
            productId,
            onDataChange: handleDataChange,
            formData,
            setFormData,
        };

        switch (activeTab) {
            case "general attributes":
                return <GeneralAttributes {...commonProps} />;
            case "seo attributes":
                return <SEOAttributes {...commonProps} />;
            case "inventory":
                return <Inventory {...commonProps} />;
            case "custom attributes":
                return <CustomAttributes {...commonProps} />;
            case "media":
                return <Media {...commonProps} />;
            case "variants":
                return <Variants {...commonProps} />;
            case "stores":
                return <Stores {...commonProps} />;
            case "product quality score":
                return <ProductQualityScore {...commonProps} />;
            default:
                return null;
        }
    };
    console.log("product", product);
    return (
        <>
            {/* Fixed header with dynamic top position based on scroll */}
            <div
                className={`fixed top-[133px] left-[80px] -mx-[-20px] z-50 w-full transition-all duration-0`}
                style={{ transform: `translateY(${scrollPosition > 0 ? "-53px" : "0"})` }}
            >
                <div className="bg-white border border-[#DBDBDB] max-w-[calc(100vw-232px)] mx-auto rounded-t-[12px]">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center px-5 py-4 md:h-[64px]">
                        {/* Breadcrumb Section */}
                        <div className="flex items-center mb-4 md:mb-0">
                            <Link href="/v2/products/all-products" className="text-[#740898] hover:underline">
                                All Products
                            </Link>
                            <span className="mx-2 text-black">/</span>
                            <span className="font-bold text-[#252525]">{productHandle}</span>
                        </div>

                        {/* Action Buttons Section */}
                        <div className="flex items-center gap-2">
                            <Button
                                type="text"
                                danger
                                icon={<img src={DeleteIcon} alt="Delete" />}
                                style={{ color: "#FE1F23", backgroundColor: "#fff", border: "1px solid #D9D9D9" }}
                            >
                                Delete
                            </Button>
                            <Button
                                type="primary"
                                onClick={handleSave}
                                loading={saving}
                                style={{
                                    backgroundColor: "#740898",
                                    color: "white",
                                    border: "1px solid #740898",
                                }}
                            >
                                {saving ? "Saving..." : "Save"}
                            </Button>
                            <Button type="default" icon={<LeftOutlined />}></Button>
                            <Button type="default" icon={<RightOutlined />}></Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main content with padding for fixed header */}
            <div className="bg-white rounded-[12px] border border-[#DBDBDB]">
                <div className="h-[64px]"></div>
                {/* Product Image and Status */}
                <div
                    className="border-t border-b border-[#DBDBDB] flex"
                    style={{
                        flexDirection: isWideScreen ? "row" : "column",
                        minHeight: isWideScreen ? "216px" : "auto",
                        maxHeight: isWideScreen ? "216px" : "none",
                    }}
                >
                    {/* Left Section: Product Image and Details */}
                    <div
                        className={`pr-0 flex ${
                            isWideScreen ? "w-8/12 border-r justify-start items-start" : "w-full border-b"
                        } border-[#DBDBDB]`}
                        style={{
                            minHeight: isWideScreen ? "216px" : "180px",
                            maxHeight: isWideScreen ? "216px" : "180px",
                            padding: "24px 0",
                            overflow: "hidden",
                        }}
                    >
                        <div
                            className={`flex w-full  px-[30px] ${
                                isWideScreen ? "justify-start items-start" : "justify-center items-center"
                            }`}
                        >
                            <div className="flex items-center gap-[20px] max-w-[1000px]">
                                {/* Image Container */}
                                <div
                                    className={`flex items-center justify-center ${isWideScreen ? "w-3/12" : "w-4/12"}`}
                                    style={{ maxWidth: "140px" }}
                                >
                                    <div className="w-full h-full flex items-center justify-center">
                                        <img
                                            src={product?.file?.link || ProductImage}
                                            alt="Product"
                                            className="w-[178px] h-[178px] object-contain"
                                        />
                                    </div>
                                </div>

                                {/* Product Details */}
                                <div className={`flex items-start flex-col justify-center ${isWideScreen ? "w-9/12" : "w-8/12"} pl-[30px]`}>
                                    <div className="mb-3">
                                        <span
                                            className={`text-[14px] py-[8px] px-[12px] rounded-[21px] font-[600] ${
                                                productStatus === "Active" ? "text-green-500 bg-[#DDF9EC]" : "text-yellow-500 bg-yellow-100"
                                            }`}
                                        >
                                            {productStatus}
                                        </span>
                                    </div>
                                    <h2 className="text-[18px] font-[600] mb-3 text-[#252525] line-clamp-2 capitalize">{productHandle}</h2>
                                    <div className="-mb-[10px] pb-0">
                                        <span className="text-[#252525]">{progressPercentage}% Complete</span>
                                    </div>
                                    <div style={{ width: "265px" }}>
                                        <Progress
                                            percent={progressPercentage}
                                            size={{ height: 4 }}
                                            showInfo={false}
                                            strokeColor="#15D476"
                                            className="pt-0"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Section: Doughnut Chart and Valid/Invalid Fields */}
                    <div
                        className={`flex flex-row gap-[20px] items-center justify-center ${isWideScreen ? "w-4/12" : "w-full"}`}
                        style={{
                            minHeight: isWideScreen ? "216px" : "180px",
                            maxHeight: isWideScreen ? "216px" : "180px",
                            padding: "24px",
                            overflow: "hidden",
                        }}
                    >
                        <div className="flex items-center gap-[20px] max-w-[1000px] mx-auto">
                            <div className="chart">
                                <DoughnutChart qualityType="Product" scores={defaultScores} />
                            </div>
                            <div className="border border-[#EBEBEB] rounded-[8px] h-[105px] w-[134px] p-4 flex flex-col items-center justify-center">
                                <span className="text-[40px] font-[700] text-[#252525]">{validFieldsCount}</span>
                                <span className="text-[#15D476]">Valid Fields</span>
                            </div>
                            <div className="border border-[#EBEBEB] rounded-[8px] p-4 h-[105px] w-[134px] flex flex-col items-center justify-center">
                                <span className="text-[40px] font-[700] text-[#252525]">{invalidFieldsCount}</span>
                                <span className="text-[#FE1F23]">Invalid Fields</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="p-5">
                    {/* Custom Tabs Section with Scroll */}
                    <div className="bg-[#FCF5FF] rounded-[4px] mt-0 relative mx-4">
                        {canScrollLeft && (
                            <Button
                                type="default"
                                icon={<LeftOutlined />}
                                className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10"
                                onClick={() => scrollTabs("left")}
                            />
                        )}
                        <div
                            ref={tabsContainerRef}
                            className={`flex overflow-x-auto no-scrollbar gap-[4px] ${canScrollLeft ? "px-8" : "px-0"}`}
                            style={{
                                scrollbarWidth: "none",
                                msOverflowStyle: "none",
                            }}
                        >
                            {[
                                "general attributes",
                                "seo attributes",
                                "inventory",
                                "custom attributes",
                                "media",
                                "variants",
                                "stores",
                                "product quality score",
                            ].map((tab) => (
                                <div
                                    key={tab}
                                    onClick={() => handleTabChange(tab)}
                                    className={`whitespace-nowrap text-center py-2 px-[12px] cursor-pointer transition-colors duration-200 rounded-[4px] ${
                                        activeTab === tab ? "bg-[#740898] text-white" : "bg-[#FCF5FF] text-[#626262]"
                                    }`}
                                >
                                    {tab.charAt(0).toUpperCase() + tab.slice(1).replace(/([A-Z])/g, " $1")}
                                </div>
                            ))}
                        </div>
                        {canScrollRight && (
                            <Button
                                type="default"
                                icon={<RightOutlined />}
                                className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10"
                                onClick={() => scrollTabs("right")}
                            />
                        )}
                    </div>
                    {/* Render the content of the active tab */}
                    <div className="mt-0">{renderTabContent()}</div>
                </div>
                {/* Confirmation Modal */}
                <Modal
                    title="Unsaved Changes"
                    open={isModalVisible}
                    onCancel={handleModalCancel}
                    footer={[
                        <Button key="back" onClick={handleModalCancel}>
                            Cancel
                        </Button>,
                        <Button key="discard" onClick={handleModalOk} danger>
                            Discard Changes
                        </Button>,
                        <Button key="save" type="primary" onClick={handleSave} className="bg-[#740898]">
                            Save Changes
                        </Button>,
                    ]}
                >
                    <p>You have unsaved changes. Would you like to save them before switching tabs?</p>
                </Modal>
            </div>
        </>
    );
};

export default SingleProduct;
