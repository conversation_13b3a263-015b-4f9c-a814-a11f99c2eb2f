import React, { useState, useEffect, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { Table, Input, Button, Row, Col, Tooltip, message, Modal, Spin } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";
import { get, post } from "../../../axios";
import { Checkbox, Form } from "antd";

// Import the main Attributes component to access its modal
import Attributes from "./Attributes";

// Mock data for development and testing
const mockAttributes = [
    {
        id: 1,
        name: "Company Name",
        attribute_type_id: 4,
        attribute_set_id: 3,
        is_required: 1,
        handle: "company_name",
    },
    {
        id: 2,
        name: "Department",
        attribute_type_id: 4,
        attribute_set_id: 3,
        is_required: 0,
        handle: "department",
    },
    {
        id: 3,
        name: "Business Type",
        attribute_type_id: 4,
        attribute_set_id: 3,
        is_required: 1,
        handle: "business_type",
    },
    {
        id: 4,
        name: "Industry",
        attribute_type_id: 4,
        attribute_set_id: 3,
        is_required: 1,
        handle: "industry",
    },
    {
        id: 5,
        name: "Employee Count",
        attribute_type_id: 4,
        attribute_set_id: 3,
        is_required: 0,
        handle: "employee_count",
    },
    {
        id: 6,
        name: "Annual Revenue",
        attribute_type_id: 4,
        attribute_set_id: 3,
        is_required: 0,
        handle: "annual_revenue",
    },
];

const ProductOrganizationAttributes = forwardRef((props, ref) => {
    // State for attributes data
    const [attributes, setAttributes] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Reference to the main attributes component
    const attributesRef = React.useRef();

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch organization attributes data
    const fetchAttributes = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);

            // Use mock data for development/demo purposes
            // Comment this section and uncomment the API call for production
            setTimeout(() => {
                let filteredData = [...mockAttributes];

                // Filter by search query if provided
                if (searchQuery) {
                    filteredData = filteredData.filter((attr) => attr.name.toLowerCase().includes(searchQuery.toLowerCase()));
                }

                setAttributes(filteredData);
                setPagination({
                    ...pagination,
                    current: page,
                    pageSize: pageSize,
                    total: filteredData.length,
                });
                setLoading(false);
            }, 500); // Simulate network delay

            // Uncomment for actual API call
            /*
            let endpoint = `attributes?page=${page}&paginate=${pageSize}&attribute_type_id=4`;

            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            if (response && response.data) {
                setAttributes(response.data || []);

                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
            */
        } catch (error) {
            console.error("Failed to fetch product organization attributes:", error);
            message.error("Failed to load product organization attributes");
        } finally {
            // setLoading(false); // Moved to the setTimeout for mock data
        }
    };

    // Load organization attributes on component mount
    useEffect(() => {
        fetchAttributes(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [attributeToDelete, setAttributeToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentAttribute, setCurrentAttribute] = useState(null);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        handleCreateAttribute: () => {
            // Use the main Attributes component's modal
            if (attributesRef.current) {
                attributesRef.current.handleCreateAttribute();
            }
        },
    }));

    // Handle edit attribute
    const handleEdit = (record) => {
        // Use the main Attributes component's modal
        if (attributesRef.current) {
            attributesRef.current.handleCreateAttribute();
        }
    };

    // Open delete confirmation modal
    const openDeleteModal = (record) => {
        setAttributeToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Confirm delete organization attribute
    const confirmDelete = async () => {
        try {
            setLoading(true);
            // API call to delete attribute
            await post(`attributes/${attributeToDelete.id}`, { _method: "DELETE" });
            message.success("Product organization attribute deleted successfully");
            fetchAttributes(pagination.current, pagination.pageSize, searchText);
            setIsDeleteModalVisible(false);
        } catch (error) {
            console.error("Failed to delete product organization attribute:", error);
            message.error("Failed to delete product organization attribute");
        } finally {
            setLoading(false);
        }
    };

    // Handle search input change
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchText(value);
        if (value === "") {
            // If search is cleared, fetch all organization attributes
            fetchAttributes(1, pagination.pageSize, "");
        }
    };

    // Handle search
    const onSearch = () => {
        fetchAttributes(1, pagination.pageSize, searchText);
    };

    // Handle form submission
    const handleFormSubmit = async () => {
        try {
            setLoading(true);

            // Validate form fields
            const values = await form.validateFields();

            // Prepare form data
            const formData = {
                name: values.name,
                attribute_type_id: values.attributeType || 4, // Default to organization type
                attribute_set_id: values.attributeSet,
                is_required: values.isRequired ? 1 : 0,
                handle: values.name.toLowerCase().replace(/[^a-z0-9]/g, "_"), // Generate handle from name
            };

            // Send request to API
            if (isEditMode && currentAttribute) {
                await post(`attributes/${currentAttribute.id}`, { ...formData, _method: "PUT" });
                message.success("Product organization attribute updated successfully");
            } else {
                await post("attributes", formData);
                message.success("Product organization attribute created successfully");
            }

            // Close modal and refresh data
            setIsModalVisible(false);
            fetchAttributes(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Form submission error:", error);

            if (error.errorFields) {
                // Form validation error
                return;
            }

            message.error("Failed to save product organization attribute. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setIsEditMode(false);
        setCurrentAttribute(null);
    };

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchAttributes(newPagination.current, newPagination.pageSize, searchText);
    };

    return (
        <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
            {/* Render the Attributes component with ref but hide it */}
            <div style={{ display: "none" }}>
                <Attributes ref={attributesRef} />
            </div>

            {/* Table Controls */}
            <div className="p-5">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h2 className="m-0 text-base font-semibold text-[#252525]">Product Organization Attributes</h2>
                    </Col>
                    <Col>
                        <div className="flex gap-2">
                            <Input
                                placeholder="Search Attribute"
                                prefix={<SearchOutlined />}
                                value={searchText}
                                onChange={handleSearch}
                                className="w-60 rounded"
                                allowClear
                                onPressEnter={onSearch}
                            />
                        </div>
                    </Col>
                </Row>
            </div>

            {/* Organization Attributes Table */}
            <div className="border-t border-[#DBDBDB]">
                <table className="w-full border-collapse">
                    <thead>
                        <tr className="bg-[#F9FAFB] border-b border-[#DBDBDB]">
                            <th className="p-4 text-left text-[#626262] font-[400] text-[14px] w-[70%] border-r border-[#DBDBDB]">Name</th>
                            <th className="p-4 text-left text-[#626262] font-[400] text-[14px] w-[15%] border-r border-[#DBDBDB]">
                                Is Required
                            </th>
                            <th className="p-4 text-left text-[#626262] font-[400] text-[14px] w-[15%]">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {loading ? (
                            <tr>
                                <td colSpan="3" className="p-4 text-center">
                                    <Spin size="default" />
                                </td>
                            </tr>
                        ) : attributes.length === 0 ? (
                            <tr>
                                <td colSpan="3" className="p-4 text-center">
                                    No product organization attributes found
                                </td>
                            </tr>
                        ) : (
                            attributes.map((attribute) => (
                                <tr key={attribute.id} className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">{attribute.name}</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={attribute.is_required === 1} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Tooltip title="Delete">
                                                <Button
                                                    type="text"
                                                    icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                    onClick={() => openDeleteModal(attribute)}
                                                    className="p-0"
                                                />
                                            </Tooltip>
                                            <Tooltip title="Edit">
                                                <Button
                                                    type="text"
                                                    icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                    onClick={() => handleEdit(attribute)}
                                                    className="p-0"
                                                />
                                            </Tooltip>
                                        </div>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>

                {/* Pagination */}
                {attributes.length > 0 && (
                    <div className="flex justify-end p-4">
                        <div className="flex items-center gap-2">
                            <Button
                                disabled={pagination.current === 1}
                                onClick={() => handleTableChange({ ...pagination, current: pagination.current - 1 })}
                                className="border-[#d9d9d9] rounded"
                            >
                                Previous
                            </Button>
                            {Array.from({ length: Math.min(5, Math.ceil(pagination.total / pagination.pageSize)) }, (_, i) => {
                                const page = i + 1;
                                return (
                                    <Button
                                        key={page}
                                        className={`rounded outline-none focus:outline-none bg-white ${
                                            pagination.current === page ? "text-[#740898] border-[#740898] shadow-none" : "border-[#d9d9d9]"
                                        }`}
                                        onClick={() => handleTableChange({ ...pagination, current: page })}
                                    >
                                        {page}
                                    </Button>
                                );
                            })}
                            <Button
                                disabled={pagination.current === Math.ceil(pagination.total / pagination.pageSize)}
                                onClick={() => handleTableChange({ ...pagination, current: pagination.current + 1 })}
                                className="border-[#d9d9d9] rounded"
                            >
                                Next
                            </Button>
                        </div>
                    </div>
                )}
            </div>

            {/* Delete Confirmation Modal */}
            <Modal
                title="Delete Attribute"
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={[
                    <Button key="cancel" onClick={() => setIsDeleteModalVisible(false)}>
                        Cancel
                    </Button>,
                    <Button key="delete" type="primary" danger loading={loading} onClick={confirmDelete}>
                        Delete
                    </Button>,
                ]}
                width={400}
                centered
            >
                <p>Are you sure you want to delete this attribute?</p>
                {attributeToDelete && <p className="font-semibold">{attributeToDelete.name}</p>}
                <p>This action cannot be undone.</p>
            </Modal>
        </div>
    );
});

export default ProductOrganizationAttributes;
