import React from "react";
import { Card, Row, Col, Progress, Tooltip, Empty, Input, Button } from "antd";
import { EditOutlined, DeleteOutlined, FileImageOutlined, SearchOutlined, FilterOutlined } from "@ant-design/icons";

import { router } from "@inertiajs/react";

const ProductGrid = ({ products, onProductSelect }) => {
    // Function to handle card click
    const handleCardClick = (record) => {
        if (onProductSelect) {
            onProductSelect(record);
        } else {
            router.visit(`/v2/products/single/${record.id}`);
        }
    };

    // Function to get status color
    const getStatusColor = (status) => {
        switch (status) {
            case "Active":
                return "text-green-500";
            case "Draft":
                return "text-red-500";
            default:
                return "text-gray-500";
        }
    };

    // Function to get quality score color
    const getScoreColor = (score) => {
        if (score > 50) return "#15D476"; // Green
        if (score === 50) return "#FF9C3E"; // Orange
        return "#FE1F23"; // Red
    };

    if (!products?.products || products.products.length === 0) {
        return <Empty description="No products found" />;
    }

    return (
        <div className="bg-white rounded-[12px] border border-[#DBDBDB] p-5">
            <Row>
                <Col span={12}>
                    <p className="text-[#252525] font-[600] text-[18px] p-5">Products</p>
                </Col>
                <Col span={12}>
                    <div className="flex justify-end items-center p-5 gap-[14px]">
                        <Input placeholder="Search Product" allowClear style={{ width: 200 }} prefix={<SearchOutlined />} />
                        <Button icon={<FilterOutlined />}>Filter</Button>
                    </div>
                </Col>
            </Row>
            <Row gutter={[16, 16]}>
                {products.products.map((product) => (
                    <Col xs={24} sm={12} md={8} lg={6} key={product.id}>
                        <Card hoverable className="rounded-[8px] border border-[#DBDBDB] h-full" onClick={() => handleCardClick(product)}>
                            <div className="flex flex-col h-full">
                                {/* Product Image */}
                                <div className="flex justify-center mb-4">
                                    {product.file && product.file !== "" ? (
                                        <img src={product.file} alt={product.handle} className="w-[100px] h-[100px] rounded object-cover" />
                                    ) : (
                                        <div className="w-[100px] h-[100px] rounded bg-gray-100 flex items-center justify-center border border-gray-200">
                                            <FileImageOutlined className="text-gray-400 text-3xl" />
                                        </div>
                                    )}
                                </div>

                                {/* Product Details */}
                                <h3 className="font-semibold text-[16px] mb-2 truncate">{product.handle}</h3>

                                <div className="flex justify-between items-center mb-2">
                                    <span className="text-[14px] text-gray-500">Status:</span>
                                    <span className={`font-semibold ${getStatusColor(product.status)}`}>{product.status}</span>
                                </div>

                                <div className="flex justify-between items-center mb-2">
                                    <span className="text-[14px] text-gray-500">Variants:</span>
                                    <span className="font-medium">{product.variants_count}</span>
                                </div>

                                <div className="mb-3">
                                    <div className="flex justify-between items-center mb-1">
                                        <span className="text-[14px] text-gray-500">Quality Score:</span>
                                        <span className="font-medium">{product.version_score}%</span>
                                    </div>
                                    <Progress
                                        percent={product.version_score}
                                        showInfo={false}
                                        strokeColor={getScoreColor(product.version_score)}
                                    />
                                </div>

                                {/* Stores */}
                                <div className="mb-2">
                                    <span className="text-[14px] text-gray-500 mb-1 block">Stores:</span>
                                    <div className="flex flex-wrap">
                                        {product?.stores?.map((store, index) => (
                                            <span
                                                key={index}
                                                className="text-[#252525] text-[12px] font-medium w-[32px] h-[32px] rounded-full bg-[#F1E6F5] flex items-center justify-center border border-white mr-1 mb-1"
                                            >
                                                <span className="text-[#740898] text-[14px] font-normal">{store}</span>
                                            </span>
                                        ))}
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex justify-end mt-auto pt-2 space-x-3">
                                    <Tooltip title="Edit">
                                        <EditOutlined className="text-blue-500 cursor-pointer hover:scale-110 transition" />
                                    </Tooltip>
                                    <Tooltip title="Delete">
                                        <DeleteOutlined className="text-red-500 cursor-pointer hover:scale-110 transition" />
                                    </Tooltip>
                                </div>
                            </div>
                        </Card>
                    </Col>
                ))}
            </Row>
        </div>
    );
};

export default ProductGrid;
