import React, { useContext } from "react";
import { Layout } from "antd";
import ReactDOM from "react-dom/client";
import { Head } from "@inertiajs/react";

import ObHeader from "./ObHeader";
import OnboardingSidebar from "./OnboardingSidebar";
import OnboardingOne from "./OnboardingOne";
import OnboardingTwo from "./OnboardingTwo";
import OnboardingThree from "./OnboardingThree";
import OnboardingFour from "./OnboardingFour";
import OnboardingFive from "./OnboardingFive";
import OnboardingSix from "./OnboardingSix";
import OnboardingSeven from "./OnboardingSeven";

import OnboardingEleven from "./OnboardingEleven";

import { OnboardingProvider, OnboardingContext } from "./OnboardingContext";

const { Content } = Layout;

const OnboardingContent = () => {
    const { currentStepIndex, handleNext, handleBack } = useContext(OnboardingContext);

    const renderCurrentStep = () => {
        switch (currentStepIndex) {
            case 0:
                return <OnboardingOne />;
            case 1:
                return <OnboardingTwo />;
            case 2:
                return <OnboardingThree />;
            case 3:
                return <OnboardingFour />;
            case 4:
                return <OnboardingFive />;
            case 5:
                return <OnboardingSix />;
            case 6:
                return <OnboardingSeven />;

            default:
                return null;
        }
    };

    return (
        <Content className="flex flex-col md:flex-row">
            {/* Left Section */}
            {renderCurrentStep()}

            {/* Right Section */}
            {currentStepIndex < 7 && <OnboardingSidebar currentStep={currentStepIndex} />}
        </Content>
    );
};

const Onboarding = ({ title = "Onboarding" }) => {
    const { currentStepIndex } = useContext(OnboardingContext);

    return (
        <Layout className="">
            <Head title={title} />
            {currentStepIndex !== 10 && <ObHeader />}
            <OnboardingContent />
        </Layout>
    );
};

const OnboardingWrapper = () => (
    <OnboardingProvider>
        <Onboarding />
    </OnboardingProvider>
);

export default OnboardingWrapper;

const rootElement = document.getElementById("v2-onboarding");

if (rootElement) {
    ReactDOM.createRoot(rootElement).render(<OnboardingWrapper />);
}
