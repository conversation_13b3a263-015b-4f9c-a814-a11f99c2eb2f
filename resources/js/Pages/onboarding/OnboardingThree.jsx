import React, { useState, useContext, useEffect } from "react";
import { Layout, Card, Button, Checkbox, Row, Col, Input } from "antd";
import { OnboardingContext } from "./OnboardingContext";

const { Content } = Layout;

const OnboardingThree = () => {
    const { handleNext, handleBack, updateFormData, formData } = useContext(OnboardingContext);
    const [checkedItems, setCheckedItems] = useState({});
    const [otherChallenge, setOtherChallenge] = useState("");

    const checkboxOptions = [
        { label: "Limited people and time for updating products" },
        { label: "Maintaining data quality and accuracy to reduce returns" },
        { label: "Managing too many suppliers manually" },
        { label: "Data standardization across platforms" },
        { label: "Complex bulk edits and data consistency and validation" },
        { label: "Other" },
    ];

    // Pre-fill checkedItems and otherChallenge from context if available
    useEffect(() => {
        if (formData.challenges) {
            const initialChecked = {};
            formData.challenges.split(";").forEach((challenge) => {
                if (challenge.startsWith("Other: ")) {
                    initialChecked["Other"] = true;
                    setOtherChallenge(challenge.replace("Other: ", ""));
                } else {
                    initialChecked[challenge] = true;
                }
            });
            setCheckedItems(initialChecked);
        }
    }, [formData.challenges]);

    const handleCheckboxChange = (e, label) => {
        setCheckedItems((prevState) => ({
            ...prevState,
            [label]: e.target.checked,
        }));

        if (label === "Other" && !e.target.checked) {
            setOtherChallenge("");
        }
    };

    const handleNextClick = () => {
        // Extract selected challenges
        let selectedChallenges = Object.keys(checkedItems)
            .filter((label) => checkedItems[label])
            .join(";");

        // Update context with selected challenges
        updateFormData("challenges", selectedChallenges);

        // Proceed to next step
        handleNext();
    };

    const handleSkip = () => {
        // Update context with empty challenges
        updateFormData("challenges", "");

        // Proceed to next step
        handleNext();
    };

    return (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA]">
            <h2 className="xl:text-[40px] sm:text-xl text-center font-bold pb-[10px]">
                What are your biggest challenges with managing product data?
            </h2>
            <p className="text-gray-500 text-center mb-6">Select all that apply</p>
            <div className="p-5">
                <Row gutter={[20, 20]}>
                    {checkboxOptions.map((option, index) => (
                        <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={12} key={index}>
                            <div
                                className="bg-white p-4 rounded-[12px] flex items-start cursor-pointer"
                                onClick={(e) => {
                                    // Prevent triggering checkbox twice
                                    if (e.target.tagName !== "INPUT") {
                                        handleCheckboxChange({ target: { checked: !checkedItems[option.label] } }, option.label);
                                    }
                                }}
                            >
                                <Checkbox
                                    className="text-[#252525] text-[16px]"
                                    checked={checkedItems[option.label] || false}
                                    onChange={(e) => handleCheckboxChange(e, option.label)}
                                ></Checkbox>
                                <span
                                    className={`pl-[14px] pt-[2px] text-[#252525] text-[16px] ${
                                        checkedItems[option.label] ? "text-[#740898] font-semibold" : ""
                                    }`}
                                >
                                    {option.label}
                                </span>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>

            <div className="flex justify-end mt-6 gap-[20px]">
                <Button onClick={handleBack}>Back</Button>
                <Button
                    type="primary"
                    onClick={handleNextClick}
                    // Optionally disable "Next" if no selection is made
                    disabled={Object.keys(checkedItems).filter((key) => checkedItems[key]).length === 0}
                >
                    Next
                </Button>
                <button className="text-[#740898] text-sm cursor-pointer" onClick={handleSkip}>
                    Skip for now
                </button>
            </div>
        </div>
    );
};

export default OnboardingThree;
