import React from "react";
import { Timeline, But<PERSON> } from "antd";
import TroubleIcon from "../../../../public/v2/icons/trouble.svg";
import CompleteIcon from "../../../../public/v2/icons/complete.svg";

const OnboardingSidebar = ({ currentStep }) => {
    const steps = [
        { title: "Provide Industry", description: "What is your Industry of Store" },
        { title: "Company Information", description: "Answer questions about your company" },
        { title: "Managing Products", description: "Tell us your biggest challenges" },
        { title: "Your Role", description: "Describe your role in the company" },
        { title: "Main Objectives", description: "Provide your main objectives" },
        { title: "Create Organization", description: "Purpose of using PIM" },
        { title: "Invite Team", description: "Invite your team to collaborate" },
    ];
    return (
        <div className="w-full xl:w-1/4 sm:w-2/6 xl:pl-8 sm:pl-2 bg-white pt-20 pb-[24px] flex flex-col justify-between">
            {/* Timeline */}
            <Timeline className="pl-12">
                {steps.map((step, index) => (
                    <Timeline.Item
                        key={index}
                        dot={
                            <div
                                className={`w-[44px] h-[44px] rounded-full mt-[5px] flex items-center justify-center ${
                                    index === currentStep
                                        ? "bg-white border border-[#740898] text-[#740898]"
                                        : "text-black border border-[#D9D9D9]"
                                }`}
                            >
                                {index < currentStep ? <img src={CompleteIcon} alt="complete icon" /> : index + 1}
                            </div>
                        }
                    >
                        <h4
                            className={`font-semibold pl-4 text-[16px]  ${
                                index === currentStep
                                    ? "text-[#252525] font-[700]"
                                    : index < currentStep
                                    ? "text-[#252525] font-[600]"
                                    : "text-[#25252540]"
                            }`}
                        >
                            {step.title}
                        </h4>
                        <p
                            className={`text-[14px] pl-4 mb-[30px] ${
                                index === currentStep || index < currentStep ? "text-[#606060]" : "text-[#25252540]"
                            }`}
                        >
                            {step.description}
                        </p>
                    </Timeline.Item>
                ))}
            </Timeline>

            {/* Trouble Section */}
            <div className="text-start pl-8 max-w-sm">
                <img src={TroubleIcon} alt="trouble icon" />
                <p className="text-black mb-2 text-md font-semibold pt-2">Having trouble?</p>
                <p className="text-[#606060] text-sm">Feel free to contact us and we will always help you through the process.</p>
                <Button>Contact us</Button>
            </div>
        </div>
    );
};

export default OnboardingSidebar;
