import React, { useState, useEffect } from "react";
import ReactDOM from "react-dom/client";

import Logo from "../../../../public/v2/images/logo.png";
import WelcomeBck from "../../../../public/v2/images/welcome-bck.png";
import WelcomeVideoBck from "../../../../public/v2/images/welcome-video-bck.png";
import PlayBtn from "../../../../public/v2/icons/play-btn.svg";
import { router } from "@inertiajs/react";
import { Button, Progress } from "antd"; // Import Progress from Ant Design

// Add style tag for responsive behavior
const style = `
    @media (max-width: 1920px) {
        .right-section {
            display: none !important;
        }
        .left-section {
            width: 100% !important;
        }
    }
`;

const Welcome = () => {
    // State to track the progress percentage
    const [progress, setProgress] = useState(0);

    // State to control the "Continue" button's disabled status
    const [buttonEnabled, setButtonEnabled] = useState(false);

    useEffect(() => {
        const totalDuration = 10000; // Total duration for the animation in milliseconds (10 seconds)
        const intervalDuration = 500; // Update interval in milliseconds (1 second)
        const increment = 100 / (totalDuration / intervalDuration); // Progress increment per interval

        // Set up an interval to update the progress
        const interval = setInterval(() => {
            setProgress((prevProgress) => {
                const newProgress = prevProgress + increment;

                // If progress reaches or exceeds 100%, stop the interval and enable the button
                if (newProgress >= 50) {
                    // clearInterval(interval);
                    setButtonEnabled(true);
                    // return 100;
                }

                return newProgress;
            });
        }, intervalDuration);

        // Clean up the interval on component unmount
        return () => clearInterval(interval);
    }, []);

    return (
        <div className="flex h-screen">
            <style>{style}</style>
            {/* Left Section */}
            <div className="left-section w-1/2 bg-white flex flex-col justify-center items-start relative">
                {/* Logo */}
                <div className="absolute top-8 left-8">
                    <img src={Logo} alt="Logo" className="w-36" />
                </div>

                <div className="px-[60px]">
                    <p className="text-[#606060] text-[14px] font-[400]">Syncing your products...</p>

                    {/* Dynamic Progress Bar */}
                    <Progress
                        percent={progress}
                        status={progress < 100 ? "active" : "success"}
                        showInfo={true} // Hide the percentage text if not needed
                        strokeColor={{
                            from: "#740898",
                            to: "#15D476",
                        }} // Customize the color if desired
                        className="my-4 w-[400px]" // Add margin and width as needed
                    />

                    <h1 className="xl:text-[60px] sm:text-3xl pt-[20px] leading-[60px] font-[700] mb-8 text-start text-[#252525]">
                        Welcome to Apimio
                    </h1>
                    <p className="text-[#606060] text-[14px] font-[400]">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                    </p>
                    <ul className="pl-[20px] list-disc text-[#606060] text-[14px] font-[400] pt-[20px]">
                        <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
                        <li>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</li>
                        <li>Ut enim ad minim veniam.</li>
                        <li>Quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</li>
                    </ul>
                    <div className="flex items-center pt-[60px]">
                        {/* "Continue" button is enabled after 30 seconds */}
                        <Button
                            type="primary"
                            className="bg-[#740898] rounded-[4px] font-[400] h-[40px] w-[120px] border border-[#740898] text-[#FFFFFF]"
                            disabled={!buttonEnabled}
                            onClick={() => {
                                router.visit("/dashboard");
                            }}
                        >
                            Continue
                        </Button>
                    </div>
                </div>
            </div>

            {/* Right Section */}
            <div
                className="right-section w-1/2 bg-purple-900 flex flex-col justify-center items-center text-white bg-cover bg-center"
                style={{
                    backgroundImage: `url(${WelcomeBck})`,
                    backgroundPosition: "center",
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                }}
            >
                <h2 className="xl:text-[60px] sm:text-3xl font-[800] leading-[60px] mb-4 text-white">How to use Apimio</h2>
                <p className="text-[20px] mb-6 text-white font-[400] px-[124px] leading-[24px]">Watch demo video</p>
                <div className="relative flex items-center justify-center cursor-pointer">
                    <img src={WelcomeVideoBck} alt="welcome video background" />
                    <img src={PlayBtn} alt="play button" className="absolute" />
                </div>
            </div>
        </div>
    );
};

export default Welcome;
