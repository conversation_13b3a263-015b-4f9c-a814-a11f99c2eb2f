import React from "react";
import Counter from "../components/Counter";
import AuthComponent from "../components/AuthComponent";

export default function ReduxExample() {
    return (
        <div className="min-h-screen bg-gray-100 py-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold mb-8 text-center">Redux Examples</h1>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h2 className="text-xl font-semibold mb-4">Counter Example</h2>
                        <Counter />
                    </div>

                    <div>
                        <h2 className="text-xl font-semibold mb-4">Auth Example</h2>
                        <AuthComponent />
                    </div>
                </div>

                <div className="mt-12 p-6 bg-white rounded-lg shadow-md">
                    <h2 className="text-xl font-semibold mb-4">How to Use Redux in Your App</h2>

                    <div className="prose">
                        <h3>1. Create a Slice</h3>
                        <p>A slice is a collection of reducer logic and actions for a single feature. Here's how to create one:</p>
                        <pre className="bg-gray-100 p-2 rounded">
                            {`import { createSlice } from '@reduxjs/toolkit';

const mySlice = createSlice({
  name: 'feature',
  initialState: { value: 0 },
  reducers: {
    action1: (state) => { /* update state */ },
    action2: (state, action) => { /* use action.payload */ }
  }
});

export const { action1, action2 } = mySlice.actions;
export default mySlice.reducer;`}
                        </pre>

                        <h3>2. Add Slice to Store</h3>
                        <p>Update your store in resources/js/store/index.js:</p>
                        <pre className="bg-gray-100 p-2 rounded">
                            {`import { configureStore } from '@reduxjs/toolkit';
import featureReducer from './slices/featureSlice';

export const store = configureStore({
  reducer: {
    feature: featureReducer,
    // other reducers...
  }
});`}
                        </pre>

                        <h3>3. Use in Components</h3>
                        <p>Access store data with useSelector and dispatch actions with useDispatch:</p>
                        <pre className="bg-gray-100 p-2 rounded">
                            {`import { useSelector, useDispatch } from 'react-redux';
import { action1 } from '../store/slices/featureSlice';

function MyComponent() {
  const value = useSelector(state => state.feature.value);
  const dispatch = useDispatch();

  return (
    <button onClick={() => dispatch(action1())}>
      Do Something
    </button>
  );
}`}
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    );
}
