import React, { useCallback, useState, useEffect, useMemo, useRef } from "react";
import { VariableSizeList as List } from "react-window";
// import { useDispatch } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import MappingRow from "./components/MappingRow";
// import { setMappingData } from "../../../../../../../apimio/resources/js/store/slices/mappingSlice";
import ProductsLayout from "../../../../../../resources/js/Pages/product/ProductsLayout";
import Header from "./components/Header";
import CreateAttributeModal from "./components/CreateAttributeModal";
import { Form, Button, message } from "antd";

// Import package-specific CSS files
import "../../css/attribute-modal.css";
import "../../css/element-tags.css";

// Wrap MappingRow with React.memo to avoid unnecessary re-renders
const MemoizedMappingRow = React.memo(MappingRow);

const CSVMapping = ({ data }) => {
  console.log(data, "All data");
  // Create a ref for the list
  const listRef = useRef(null);

  // State for list height
  const [listHeight, setListHeight] = useState(650);

  // State for attribute modal
  const [isAttributeModalVisible, setIsAttributeModalVisible] = useState(false);
  const [currentRowId, setCurrentRowId] = useState(null);
  const [allFamilies, setAllFamilies] = useState([]);
  const [allAttributes, setAllAttributes] = useState([]);

  // Helper function to validate 'from' field values against available options
  const validateFromFieldValue = (fromValue, convertedInputArray) => {
    if (!fromValue || !convertedInputArray) return false;

    // Get all available option values from convertedInputArray
    const availableValues = convertedInputArray.flatMap(group =>
      group.options?.map(option => option.value) || []
    );

    return availableValues.includes(fromValue);
  };

  // Helper function to clean invalid 'from' values from template data
  const cleanInvalidFromValues = (templateNodes, convertedInputArray) => {
    return templateNodes.map(node => {
      const cleanedNode = { ...node };
      const originalFromValues = [...(cleanedNode.from || [])];

      // Validate and clean 'from' field values
      if (cleanedNode.from && Array.isArray(cleanedNode.from)) {
        cleanedNode.from = cleanedNode.from.filter(fromValue => {
          // For short_code formula, check if it's base64 encoded content
          if (cleanedNode.with_formula === "short_code" && fromValue) {
            // If it's base64 encoded, keep it (it's rich content, not a CSV column reference)
            const isBase64String = (str) => {
              if (!str || typeof str !== 'string') return false;
              const regex = /^[A-Za-z0-9+/=]+$/;
              return regex.test(str) && (str.length % 4 === 0);
            };

            if (isBase64String(fromValue)) {
              return true; // Keep base64 content
            }
          }

          // For all other cases, validate against available CSV columns
          const isValid = validateFromFieldValue(fromValue, convertedInputArray);

          // Log when invalid values are filtered out
          if (!isValid && fromValue) {
            console.warn(`Template validation: Removing invalid 'from' value '${fromValue}' from template. This column does not exist in the current CSV.`);
          }

          return isValid;
        });

        // Log summary if any values were removed
        if (originalFromValues.length > cleanedNode.from.length) {
          console.log(`Template validation: Node with formula '${cleanedNode.with_formula}' had ${originalFromValues.length - cleanedNode.from.length} invalid 'from' value(s) removed.`);
        }
      }

      return cleanedNode;
    });
  };

  // Initialize state with nodes, validating template data against current CSV
  const [nodes, setNodes] = useState(() => {
    const templateNodes = data?.mapping_data || [];
    const convertedInputArray = data?.converted_input_array || [];

    // If we have template data and converted input array, validate the 'from' values
    const validatedNodes = templateNodes.length > 0 && convertedInputArray.length > 0
      ? cleanInvalidFromValues(templateNodes, convertedInputArray)
      : templateNodes;

    return validatedNodes.map((node) => ({
      ...node,
      id: node.id || uuidv4(),
      // Ensure you have a stable shape: from: [], with_formula: '', to: []
      from: node.from ?? [],
      to: node.to ?? [],
      with_formula: node.with_formula || "assign",
    }));
  });

  const dataRequired = data?.data_required;

  // Initialize attribute families and types from data
  useEffect(() => {
    if (data?.apimio_attributes_required) {
      if (data.apimio_attributes_required.all_families) {
        setAllFamilies(data.apimio_attributes_required.all_families);
      }
      if (data.apimio_attributes_required.all_attributes) {
        setAllAttributes(data.apimio_attributes_required.all_attributes);
      }
    }
  }, [data]);

  // Function to determine row height based on content
  const getItemSize = useCallback((index) => {
    const node = nodes[index];

    // Check if this row is a MergeAdvance component
    const isShortcode = node.with_formula === "short_code";

    // Standard padding for all rows
    const standardPadding = 16;

    // Height of conditional elements in unmapped rows:
    // - Warning message: ~16px (text + icon, compact)
    // - Create button: ~24px (button height, compact)
    // - Container margin: 8px (mt-2)
    // Total conditional height: minimal spacing for very compact layout
    const conditionalElementsHeight = 8;

    if (isShortcode) {
      // Merge Advanced formula: consistent height regardless of mapping status
      // Always allocate space for conditional elements to maintain consistent height
      return 240 + standardPadding + conditionalElementsHeight; // 316px total
    } else {
      // All other formulas: consistent height regardless of mapping status
      // Always allocate space for conditional elements to maintain consistent height
      return 120 + standardPadding + conditionalElementsHeight; // 196px total
    }
  }, [nodes]);

  // Adjust list height based on window size
  useEffect(() => {
    const handleResize = () => {
      // Calculate appropriate height (e.g., viewport height minus other UI elements)
      const newHeight = window.innerHeight - 200; // Adjust the offset as needed
      setListHeight(Math.max(400, newHeight)); // Set a minimum height
    };

    // Set initial height
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Log all keys in dataRequired to find the correct key for Import_Action


  const handleDelete = useCallback((idToDelete) => {
    setNodes((prevNodes) => {
      // Find the index of the node to be deleted
      const deleteIndex = prevNodes.findIndex(node => node.id === idToDelete);

      // Filter out the node to be deleted
      const newNodes = prevNodes.filter((node) => node.id !== idToDelete);

      // Reset the list cache after deleting a row
      if (deleteIndex !== -1 && listRef.current) {
        // Use setTimeout to ensure this runs after the state update
        setTimeout(() => {
          listRef.current.resetAfterIndex(deleteIndex);
        }, 0);
      }

      return newNodes;
    });
  }, []);

  // Utility function to check if a string is base64 encoded
  const isBase64String = (str) => {
    if (!str || typeof str !== 'string') return false;
    const regex = /^[A-Za-z0-9+/=]+$/;
    return regex.test(str) && (str.length % 4 === 0);
  };

  const handleFormulaChange = (rowId, newFormula) => {
    // Set the initial state for each node
    setNodes((prev) => {
      const updatedNodes = prev.map((node) => {
        if (node.id !== rowId) return node;

        const updatedNode = { ...node };

        // Reset to initial state when formula changes
        if (newFormula !== node.with_formula) {
          // Remove 'with' and 'replace' if they exist
          delete updatedNode.with;
          delete updatedNode.replace;

          // Check if 'from' field has base64 encoded value
          let fromValue = updatedNode.from?.[0] || "";

          // If the value is base64 encoded or the formula is short_code, make the from field empty
          if ((fromValue && isBase64String(fromValue)) || newFormula === "short_code") {
            updatedNode.from = [];
          } else {
            // Otherwise, keep only the 0th element
            updatedNode.from = fromValue ? [fromValue] : [];
          }

          // Reduce 'to' array to only the 0th element
          updatedNode.to = updatedNode.to?.[0] ? [updatedNode.to[0]] : [];

          // Set the new formula
          updatedNode.with_formula = newFormula;

          // Special handling for vlookup formula
          if (newFormula === "vlookup") {
            // Initialize with empty values
            updatedNode.with = "";
          }
        }

        // Log for debugging
        return updatedNode;
      });

      // Find the index of the changed node to reset the list cache for that row
      const changedIndex = updatedNodes.findIndex(node => node.id === rowId);
      if (changedIndex !== -1 && listRef.current) {
        // Use setTimeout to ensure this runs after the state update
        setTimeout(() => {
          listRef.current.resetAfterIndex(changedIndex);
        }, 0);
      }

      return updatedNodes;
    });
  };

  const handleFieldChange = (rowId, newValue) => {
    console.log(rowId,"triggred",newValue,nodes);

    setNodes((prev) => {
      const updatedNodes = prev.map((node) => {
        if (node.id !== rowId) return node;

        const updatedNode = { ...node };

        // If 'from' is passed, ensure there are no undefined values and validate against available options
        if ("from" in newValue) {
          if (Array.isArray(newValue.from)) {
            // Preserve array indices - don't filter out null/undefined values to maintain positions
            updatedNode.from = newValue.from.map((item) => {
              if (item === undefined || item === null) return null; // Keep null to preserve index

              // For short_code formula, allow base64 encoded content
              if (updatedNode.with_formula === "short_code" && item) {
                if (isBase64String(item)) {
                  return item; // Keep base64 content
                }
              }

              // For all other cases, validate against available CSV columns
              if (validateFromFieldValue(item, data?.converted_input_array || [])) {
                return item; // Keep valid values
              } else {
                return null; // Invalid value, set to null but preserve index
              }
            });
          } else {
            // Single value validation
            if (newValue.from) {
              // For short_code formula, allow base64 encoded content
              if (updatedNode.with_formula === "short_code" && isBase64String(newValue.from)) {
                updatedNode.from = [newValue.from];
              } else if (validateFromFieldValue(newValue.from, data?.converted_input_array || [])) {
                updatedNode.from = [newValue.from];
              } else {
                updatedNode.from = []; // Invalid value, clear the field
              }
            } else {
              updatedNode.from = [];
            }
          }
        }

        // If 'with' is passed, update or remove it
        if ("with" in newValue) {
          if (!newValue.with) {
            delete updatedNode.with;
          } else {
            updatedNode.with = newValue.with;
          }
        }

        // If 'replace' is passed, update or remove it
        if ("replace" in newValue) {
          if (!newValue.replace) {
            delete updatedNode.replace;
          } else {
            updatedNode.replace = newValue.replace;
          }
        }

        // If 'to' is passed, preserve array indices
        if ("to" in newValue) {
          if (Array.isArray(newValue.to)) {
            // Preserve array indices - don't filter out null/undefined values to maintain positions
            updatedNode.to = newValue.to.map((item) => {
              return (item === undefined) ? null : item; // Convert undefined to null to preserve index
            });
          } else {
            updatedNode.to = newValue.to ? [newValue.to] : [];
          }
        }

        console.log(updatedNode,"updatedNodeNew");

        return updatedNode;
      });

      // Find the index of the changed node to reset the list cache for that row
      const changedIndex = updatedNodes.findIndex(node => node.id === rowId);
      if (changedIndex !== -1 && listRef.current) {
        // Use setTimeout to ensure this runs after the state update
        setTimeout(() => {
          listRef.current.resetAfterIndex(changedIndex);
        }, 0);
      }

      return updatedNodes;
    });
  };

  // Helper function to check if a value is valid (not null, undefined, or empty string)
  const isValidValue = (value) => value !== null && value !== undefined && value !== "";

  // Helper function to check if a node is properly mapped
  const checkIfMapped = (node) => {
    switch (node.with_formula) {
      case "split":
        return (
          isValidValue(node?.from?.[0]) &&
          isValidValue(node?.to?.[0]) &&
          isValidValue(node?.to?.[1]) &&
          node?.with && node?.with.trim() !== ""
        );
      case "merge":
        return (
          isValidValue(node?.from?.[0]) &&
          isValidValue(node?.from?.[1]) &&
          isValidValue(node?.to?.[0]) &&
          node?.with && node?.with.trim() !== ""
        );
      case "short_code":
        return (
          isValidValue(node?.from?.[0]) && node?.from?.[0].trim() !== "" &&
          isValidValue(node?.to?.[0])
        );
      case "replace":
        return (
          isValidValue(node?.from?.[0]) &&
          isValidValue(node?.to?.[0]) &&
          node?.replace && node?.replace.trim() !== "" &&
          node?.with && node?.with.trim() !== ""
        );
      case "expand":
        return (
          isValidValue(node?.from?.[0]) &&
          isValidValue(node?.to?.[0]) &&
          node?.with && node?.with.trim() !== ""
        );
      case "calculate":
        return (
          isValidValue(node?.from?.[0]) &&
          isValidValue(node?.to?.[0]) &&
          node?.with && node?.with.trim() !== ""
        );
      case "vlookup":
        return (
          isValidValue(node?.from?.[0]) &&
          isValidValue(node?.to?.[0]) &&
          node?.with && node?.with.trim() !== ""
        );
      case "slug":
      case "assign":
        return isValidValue(node?.from?.[0]) && isValidValue(node?.to?.[0]);
      default:
        return false;
    }
  };

  // Calculate mapped rows count using useMemo to avoid recalculation on every render
  const mappedRowsCount = useMemo(() => {
    return nodes.filter(node => checkIfMapped(node)).length;
  }, [nodes]);

  useEffect(() => {
    console.log("Parent sees nodes:", nodes);
    console.log("Mapped rows count:", mappedRowsCount);

    // Reset cached sizes when nodes change
    if (listRef.current) {
      listRef.current.resetAfterIndex(0);
    }
  }, [nodes, mappedRowsCount]);

  // Memoize the delete handler

  const handleFromFieldChange = useCallback((rowId, newFrom) => {
    setNodes((prev) =>
      prev.map((item) => {
        if (item.id !== rowId) return item;

        // Preserve the existing from array and only update index 0 (main CSV Attribute)
        const updatedFrom = item.from ? [...item.from] : [];

        // Validate the new 'from' value for index 0 only
        let validatedValue = null;

        if (newFrom) {
          // For short_code formula, allow base64 encoded content
          if (item.with_formula === "short_code" && isBase64String(newFrom)) {
            validatedValue = newFrom;
          } else if (validateFromFieldValue(newFrom, data?.converted_input_array || [])) {
            validatedValue = newFrom;
          }
          // If validation fails, validatedValue remains null
        }

        // Update only index 0, preserve other indices
        updatedFrom[0] = validatedValue;

        return { ...item, from: updatedFrom };
      })
    );
  }, [data?.converted_input_array]);

  // Handler for opening the create attribute modal
  const handleCreateAttribute = useCallback((rowId) => {
    setCurrentRowId(rowId);
    setIsAttributeModalVisible(true);
  }, []);

  // Handler for closing the create attribute modal
  const handleCloseAttributeModal = useCallback(() => {
    setIsAttributeModalVisible(false);
    setCurrentRowId(null);
  }, []);

  // Handler for when an attribute is created
  const handleAttributeCreated = useCallback((updatedData, rowId, fullAttributeValue) => {
    // Update the available attributes and families
    if (updatedData.apimio_attributes_required) {
      if (updatedData.apimio_attributes_required.all_families) {
        setAllFamilies(updatedData.apimio_attributes_required.all_families);
      }
      if (updatedData.apimio_attributes_required.all_attributes) {
        setAllAttributes(updatedData.apimio_attributes_required.all_attributes);
      }
    }

    // Update the output array with the new attribute
    if (updatedData.converted_output_array) {
      // Create a new reference to trigger re-renders in child components
      const newOutputArray = [...updatedData.converted_output_array];

      // Find the attribute family group in the output array
      const familyParts = fullAttributeValue.split(',');
      const familyName = familyParts[0];
      const attributeHandle = familyParts[1];

      // Add the new attribute to the converted_output_array
      // This ensures the dropdown in formula components will show the new attribute
      const newAttribute = {
        label: updatedData.new_attribute_name || attributeHandle,
        value: fullAttributeValue
      };

      // Find if the family group already exists
      let familyGroupExists = false;
      for (const group of newOutputArray) {
        if (group.title === familyName || group.label === familyName) {
          // Add the new attribute to this group if it doesn't already exist
          const attributeExists = group.options.some(opt => opt.value === fullAttributeValue);
          if (!attributeExists) {
            group.options.push(newAttribute);
          }
          familyGroupExists = true;
          break;
        }
      }

      // If the family group doesn't exist, create it
      if (!familyGroupExists) {
        newOutputArray.push({
          title: familyName,
          label: familyName,
          options: [newAttribute]
        });
      }

      // Update the data object with the new array
      data.converted_output_array = newOutputArray;
    }

    // Auto-select the new attribute in the row that triggered the creation
    setNodes((prev) => {
      const updatedNodes = prev.map((item) =>
        item.id === rowId
          ? { ...item, to: [fullAttributeValue] }
          : item
      );

      // Force a reset of the list to ensure the row updates
      if (listRef.current) {
        setTimeout(() => {
          listRef.current.resetAfterIndex(0);
        }, 0);
      }

      return updatedNodes;
    });

    message.success("Attribute created and selected successfully!");
  }, [data]);

  // Memoize the add row handler
  const handleAddRow = useCallback(() => {
    const newRow = {
      id: uuidv4(),
      from: [],
      to: [],
      with_formula: "assign",
    };
    setNodes((prevNodes) => {
      const newNodes = [...prevNodes, newRow];
      const newRowIndex = newNodes.length - 1;

      // Reset the list cache after adding a new row
      if (listRef.current) {
        // Use setTimeout to ensure this runs after the state update
        setTimeout(() => {
          listRef.current.resetAfterIndex(prevNodes.length);

          // Auto-scroll to the newly added row with smooth animation
          listRef.current.scrollToItem(newRowIndex, "end");

          // Additional smooth scroll using the underlying DOM element
          const listElement = listRef.current._outerRef;
          if (listElement) {
            // Calculate the scroll position for the new row
            let totalHeight = 0;
            for (let i = 0; i < newRowIndex; i++) {
              totalHeight += getItemSize(i);
            }

            // Smooth scroll to the calculated position
            listElement.scrollTo({
              top: totalHeight,
              behavior: 'smooth'
            });
          }
        }, 0);
      }

      // Show notification to user
      message.success(`New row added successfully! Scrolled to row ${newRowIndex + 1}.`);

      return newNodes;
    });
  }, []);

  // Function to reset nodes and add four new rows
  const handleReset = useCallback(() => {
    // Clear all nodes
    setNodes([]);

    // Add four new rows
    setTimeout(() => {
      for (let i = 0; i < 4; i++) {
        handleAddRow();
      }
    }, 0);
  }, [handleAddRow]);

  // Virtualized row renderer
  const Row = useCallback(({ index, style }) => {
    const node = nodes[index];

    // Consistent padding for all rows regardless of mapping status
    const paddedStyle = {
      ...style,
      paddingTop: 8,
      paddingBottom: 8,
    };

    // Get the current output array to ensure re-renders when it changes
    const currentOutputArray = data?.converted_output_array || [];

    return (
      <div style={paddedStyle}>
        <MemoizedMappingRow
          key={`row-${node.id}-${currentOutputArray.length}`} // Add key to force re-render when output array changes
          index={index}
          node={node}
          convertedInputArray={data?.converted_input_array || []}
          convertedOutputArray={currentOutputArray}
          onDelete={handleDelete}
          onFormulaChange={handleFormulaChange}
          onFromFieldChange={handleFromFieldChange}
          onFieldChange={handleFieldChange}
          onCreateAttribute={handleCreateAttribute}
        />
      </div>
    );
  }, [nodes, data?.converted_input_array, data?.converted_output_array, handleDelete, handleFormulaChange, handleFromFieldChange, handleFieldChange, handleCreateAttribute]);

  return (
    <ProductsLayout activeTab="importcsv" hideHeader={true}>
      <>
        <div className="pt-4 px-4">
          <Header
            totalRows={nodes.length}
            mappedRows={mappedRowsCount}
            dataRequired={dataRequired}
            nodes={nodes}
            onReset={handleReset}
          />
        </div>
        <div className="px-4">
          <div>
            <Form layout="vertical">
              {/*
              Instead of mapping through all nodes at once, we use react-window
              to only render what's visible.
            */}
              <List
                ref={listRef}
                height={listHeight} // Dynamic height based on screen size
                itemCount={nodes.length}
                itemSize={getItemSize} // Dynamic item size based on content
                width="100%"
              >
                {Row}
              </List>
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={handleAddRow}
                  style={{ width: "100%" }}
                >
                  Add New Row
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>

        {/* Create Attribute Modal */}
        <CreateAttributeModal
          isVisible={isAttributeModalVisible}
          onClose={handleCloseAttributeModal}
          rowId={currentRowId}
          allFamilies={allFamilies}
          allAttributes={allAttributes}
          onAttributeCreated={handleAttributeCreated}
          dataRequired={dataRequired}
        />
      </>
    </ProductsLayout>
  );
};

export default CSVMapping;
