import React, { useState, useEffect } from "react";
import { Button, Form, Select, message, Modal, Tabs, Input } from "antd";
import SelectAttribute from "../components/SelectAttribute";
import axios from "axios";

const { TabPane } = Tabs;

const Vlookup = ({ node, onFieldChange, convertedOutputArray }) => {
  const [lookupTables, setLookupTables] = useState([]);
  const [selectedTable, setSelectedTable] = useState(node.with || "");
  const [outputAttribute, setOutputAttribute] = useState(node.to?.[0] || null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("create");
  const [submitting, setSubmitting] = useState(false);

  const [createForm] = Form.useForm();
  const [updateForm] = Form.useForm();

  // Fetch lookup tables on component mount
  useEffect(() => {
    fetchLookupTables();
  }, []);

  const fetchLookupTables = async () => {
    try {
      setLoading(true);
      const response = await axios.post("/vlookup/fetch");
      if (response.data.status === 'success') {
        setLookupTables(response.data.data || []);
      } else {
        message.error("Failed to load lookup tables");
      }
    } catch (error) {
      console.error("Error fetching VLookup tables:", error);
      message.error("Failed to load lookup tables");
    } finally {
      setLoading(false);
    }
  };

  const updateParent = (newTable, newOutput) => {
    // Always call onFieldChange to notify parent of any changes, including cleared values
    onFieldChange(node.id, {
      with: newTable || "", // Pass empty string if cleared
      to: newOutput ? [newOutput] : [], // Pass empty array if cleared
    });
  };

  const handleTableChange = (value) => {
    setSelectedTable(value);
    updateParent(value, outputAttribute);
  };

  const handleOutputChange = (value) => {
    setOutputAttribute(value);
    updateParent(selectedTable, value);
  };

  const showModal = () => {
    setIsModalVisible(true);
    setActiveTab("create");
    createForm.resetFields();
    updateForm.resetFields();
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  const handleCreateSubmit = async (values) => {
    try {
      setSubmitting(true);
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("values", values.values);
      formData.append("submit_type", "add");

      const response = await axios.post("/vlookup", formData);
      if (response.data.status === 'success') {
        message.success("Lookup table created successfully");
        createForm.resetFields();
        fetchLookupTables();
        setIsModalVisible(false);
      } else {
        message.error("Failed to create lookup table");
      }
    } catch (error) {
      console.error("Error creating lookup table:", error);
      if (error.response?.data?.data) {
        const errors = error.response.data.data;
        Object.entries(errors).forEach(([field, msgs]) => {
          message.error(`${field}: ${msgs[0]}`);
        });
      } else {
        message.error("Failed to create lookup table");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateSubmit = async (values) => {
    try {
      setSubmitting(true);
      const formData = new FormData();
      formData.append("id", values.id);
      formData.append("name", values.name);
      formData.append("values", values.values);
      formData.append("submit_type", "edit");

      const response = await axios.post("/vlookup", formData);
      if (response.data.status === 'success') {
        message.success("Lookup table updated successfully");
        updateForm.resetFields();
        fetchLookupTables();
        setIsModalVisible(false);
      } else {
        message.error("Failed to update lookup table");
      }
    } catch (error) {
      console.error("Error updating lookup table:", error);
      if (error.response?.data?.data) {
        const errors = error.response.data.data;
        Object.entries(errors).forEach(([field, msgs]) => {
          message.error(`${field}: ${msgs[0]}`);
        });
      } else {
        message.error("Failed to update lookup table");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleTableSelect = async (tableId) => {
    try {
      const response = await axios.post("/vlookup/fetch", { id: tableId });
      if (response.data.status === 'success' && response.data.data.length > 0) {
        const table = response.data.data[0];
        updateForm.setFieldsValue({
          name: table.name,
          values: table.values
        });
      }
    } catch (error) {
      console.error("Error fetching lookup table details:", error);
      message.error("Failed to load lookup table details");
    }
  };

  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1">
        <Form.Item
          label={
            <span>
              VLookup Table
              <Button
                type="link"
                size="small"
                onClick={showModal}
                style={{ padding: '0 4px' }}
              >
                (Add)
              </Button>
            </span>
          }
          style={{ marginBottom: 0 }}
        >
          <Select
            loading={loading}
            value={selectedTable}
            onChange={handleTableChange}
            style={{ width: 180 }}
            options={lookupTables.map(table => ({
              label: table.name,
              value: table.id.toString()
            }))}
          />
        </Form.Item>
      </div>

      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute" style={{ marginBottom: 0 }}>
          <SelectAttribute
            options={convertedOutputArray}
            value={outputAttribute}
            onChange={handleOutputChange}
          />
        </Form.Item>
      </div>

      <Modal
        title="VLookup Tables"
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={600}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Create New" key="create">
            <Form
              form={createForm}
              layout="vertical"
              onFinish={handleCreateSubmit}
            >
              <Form.Item
                name="name"
                label="Table Name"
                rules={[{ required: true, message: "Please enter a name" }]}
              >
                <Input placeholder="Enter a name for this lookup table" />
              </Form.Item>

              <Form.Item
                name="values"
                label="Values"
                rules={[{ required: true, message: "Please enter values" }]}
                help="Enter key-value pairs, one per line, separated by commas (e.g., 'blk,Black')"
              >
                <Input.TextArea
                  rows={6}
                  placeholder="blk,Black&#10;ylw,Yellow&#10;grn,Green&#10;etc..."
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                >
                  Create
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="Update Existing" key="update">
            <Form
              form={updateForm}
              layout="vertical"
              onFinish={handleUpdateSubmit}
            >
              <Form.Item
                name="id"
                label="Select Table"
                rules={[{ required: true, message: "Please select a table" }]}
              >
                <Select
                  placeholder="Select a table to update"
                  loading={loading}
                  onChange={handleTableSelect}
                  options={lookupTables.map(table => ({
                    label: table.name,
                    value: table.id.toString()
                  }))}
                />
              </Form.Item>

              <Form.Item
                name="name"
                label="Table Name"
                rules={[{ required: true, message: "Please enter a name" }]}
              >
                <Input placeholder="Enter a name for this lookup table" />
              </Form.Item>

              <Form.Item
                name="values"
                label="Values"
                rules={[{ required: true, message: "Please enter values" }]}
                help="Enter key-value pairs, one per line, separated by commas (e.g., 'blk,Black')"
              >
                <Input.TextArea
                  rows={6}
                  placeholder="blk,Black&#10;ylw,Yellow&#10;grn,Green&#10;etc..."
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                >
                  Update
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
};

export default Vlookup;
