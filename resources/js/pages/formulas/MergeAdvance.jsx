import React, { useState, useRef, useEffect } from "react";
import { <PERSON>, But<PERSON>, Menu, Dropdown, Tooltip } from "antd";
import { SyncOutlined } from "@ant-design/icons";
import SelectAttribute from "../components/SelectAttribute";
import "../../../css/element-tags.css";
import ShortcodeEditor from "../components/ShortcodeEditor";

const MergeAdvance = ({
  node,
  onFieldChange,
  convertedInputArray,
  convertedOutputArray,
}) => {
  const [content, setContent] = useState("");
  const [attributeOne, setAttributeOne] = useState(node.to?.[0] || null);

  const updateParent = (attributeOne, short_code) => {
    // Only proceed if all required values exist and shortcode is not null
    if ( short_code !== null) {
      // Make a shallow copy of the old from array if it exists, or start fresh:
      const updatedFrom = node.from ? [...node.from] : [];

      // Ensure there is at least a 0th element in case node.from was empty:
      if (updatedFrom[0] === undefined) {
        updatedFrom[0] = "";
      }

      console.log(updatedFrom, "updatedFrom", attributeOne, short_code, node.id);
      console.log({
        from: [short_code],
        // "with" field
        with_formula: "short_code", // or "merge" if that's the default you always want
        to: [attributeOne],
      });

      // Call onFieldChange with the full newValue object:
      onFieldChange(node.id, {
        from: [short_code],
        // "with" field
        with_formula: "short_code", // or "merge" if that's the default you always want
        to: [attributeOne],
      });
    } else {
      // If shortcode is null or empty, update with empty values
      onFieldChange(node.id, {
        from: [""],
        with_formula: "short_code",
        to: [attributeOne],
      });
    }
  };

  const handle2ContentChange = (base64) => {
    console.log(base64, "base64");

    updateParent(attributeOne, base64);
  };

  // Handle attribute change
  const handleAttributeChange = (val) => {
    setAttributeOne(val);

    // Get current encoded content
    const encodedContent = content ? btoa(content) : "";
    updateParent(val, encodedContent);
  };

  const isBase64String = (str) => {
    const regex = /^[A-Za-z0-9+/=]+$/;
    return regex.test(str) && (str.length % 4 === 0);
  };

  useEffect(() => {
    if (node.from && node.from[0]) {
      console.log(node.from[0], "node.from[0]");
      if (isBase64String(node.from[0])) {
        setContent(atob(node.from[0]));
      }
      // else {
      //   setContent(node.from[0]);
      // }
    }
  }, [node.from]);

  console.log('content', content);

  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1" style={{ width: "300px" }}>
        <Form.Item label="CSV Attribute" style={{ marginBottom: 0 }}>
          <ShortcodeEditor
            nodes={convertedInputArray}
            onChange={handle2ContentChange}
            onSetContent={setContent}
            content={content}
          />
        </Form.Item>
      </div>

      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute" style={{ marginBottom: 0 }}>
          <SelectAttribute
            options={convertedOutputArray}
            value={attributeOne}
            onChange={handleAttributeChange}
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default MergeAdvance;
