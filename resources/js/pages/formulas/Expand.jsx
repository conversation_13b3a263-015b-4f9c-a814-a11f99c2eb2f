import React, { useState, useEffect } from "react";
import { Input, Select, Form } from "antd";
import SelectAttribute from "../components/SelectAttribute";

const Expand = ({ node, onFieldChange, convertedOutputArray }) => {
  // Instead of reading "position" and "With" from "node.to",
  // let's read them from dedicated fields: node.replace, node.with.
  // For the single attribute, read from node.to?.[0].
  const [position, setPosition] = useState(node.replace || "");
  const [withValue, setWithValue] = useState(node.with || "");
  const [attributeOne, setAttributeOne] = useState(node.to?.[0] || null);

  // Always call onFieldChange to notify parent of any changes, including cleared values
  const updateParent = (newPosition, newWith, newAttr1) => {
    onFieldChange(node.id, {
      replace: newPosition || "", // Pass empty string if cleared
      with: newWith || "", // Pass empty string if cleared
      to: newAttr1 ? [newAttr1] : [], // Pass empty array if cleared
    });
  };

  // Handle position changes
  const handlePositionChange = (val) => {
    setPosition(val);
    // Important: pass the new position as the first arg,
    // the *current* withValue as second arg,
    // the *current* attributeOne as the third.
    updateParent(val, withValue, attributeOne);
  };

  // Handle "with" text input
  const handleWithChange = (e) => {
    const newWith = e.target.value;
    setWithValue(newWith);
  };

  // Update parent when with input loses focus
  const handleWithBlur = () => {
    updateParent(position, withValue, attributeOne);
  };

  // Handle the single attribute
  const handleAttributeChange = (val) => {
    setAttributeOne(val);
    // The new attribute is third param; keep position & withValue as is
    updateParent(position, withValue, val);
  };

  // Debug logs
  useEffect(() => {
    console.log("Local state in <Expand>:", {
      position,
      withValue,
      attributeOne,
    });
  }, [position, withValue, attributeOne]);

  useEffect(() => {
    console.log("Expand received node prop:", node);
  }, [node]);

  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1">
        <Form.Item label="Position" style={{ marginBottom: 0 }}>
          <Select
            style={{ width: 180 }}
            allowClear
            placeholder="Select Position"
            value={position}
            onChange={handlePositionChange}
            options={[
              { label: "Start", value: "start" },
              { label: "End", value: "end" },
            ]}
          />
        </Form.Item>
      </div>

      <div className="flex flex-col gap-1">
        <Form.Item label="With" style={{ marginBottom: 0 }}>
          <Input
            style={{ width: 100 }}
            value={withValue}
            onChange={handleWithChange}
            onBlur={handleWithBlur}
          />
        </Form.Item>
      </div>

      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute" style={{ marginBottom: 0 }}>
          <SelectAttribute
            options={convertedOutputArray}
            value={attributeOne}
            onChange={handleAttributeChange}
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default Expand;
