import React, { useState, useEffect } from "react";
import { Modal, Tabs, Form, Input, Button, Select, message } from "antd";
import axios from "axios";

const { TabPane } = Tabs;

const VLookupModal = ({ visible, onClose }) => {
  const [activeTab, setActiveTab] = useState("create");
  const [lookupTables, setLookupTables] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  const [createForm] = Form.useForm();
  const [updateForm] = Form.useForm();

  useEffect(() => {
    if (visible) {
      fetchLookupTables();
    }
  }, [visible]);

  const fetchLookupTables = async () => {
    try {
      setLoading(true);
      const response = await axios.post("/vlookup/fetch");
      setLookupTables(response.data.data || []);
    } catch (error) {
      console.error("Error fetching VLookup tables:", error);
      message.error("Failed to load lookup tables");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSubmit = async (values) => {
    try {
      setSubmitting(true);
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("values", values.values);
      formData.append("submit_type", "add");

      const response = await axios.post("/vlookup", formData);
      message.success("Lookup table created successfully");
      createForm.resetFields();
      onClose();
    } catch (error) {
      console.error("Error creating lookup table:", error);
      if (error.response?.data?.data) {
        const errors = error.response.data.data;
        Object.entries(errors).forEach(([field, msgs]) => {
          message.error(`${field}: ${msgs[0]}`);
        });
      } else {
        message.error("Failed to create lookup table");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateSubmit = async (values) => {
    try {
      setSubmitting(true);
      const formData = new FormData();
      formData.append("id", values.id);
      formData.append("name", values.name);
      formData.append("values", values.values);
      formData.append("submit_type", "edit");

      const response = await axios.post("/vlookup", formData);
      message.success("Lookup table updated successfully");
      updateForm.resetFields();
      onClose();
    } catch (error) {
      console.error("Error updating lookup table:", error);
      if (error.response?.data?.data) {
        const errors = error.response.data.data;
        Object.entries(errors).forEach(([field, msgs]) => {
          message.error(`${field}: ${msgs[0]}`);
        });
      } else {
        message.error("Failed to update lookup table");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleTableSelect = (tableId) => {
    const table = lookupTables.find(t => t.id.toString() === tableId);
    if (table) {
      updateForm.setFieldsValue({
        name: table.name,
        values: table.values
      });
    }
  };

  return (
    <Modal
      title="VLookup Tables"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Create New" key="create">
          <Form
            form={createForm}
            layout="vertical"
            onFinish={handleCreateSubmit}
          >
            <Form.Item
              name="name"
              label="Table Name"
              rules={[{ required: true, message: "Please enter a name" }]}
            >
              <Input placeholder="Enter a name for this lookup table" />
            </Form.Item>
            
            <Form.Item
              name="values"
              label="Values"
              rules={[{ required: true, message: "Please enter values" }]}
              help="Enter key-value pairs, one per line, separated by commas (e.g., 'blk,Black')"
            >
              <Input.TextArea
                rows={6}
                placeholder="blk,Black&#10;ylw,Yellow&#10;grn,Green&#10;etc..."
              />
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={submitting}
              >
                Create
              </Button>
            </Form.Item>
          </Form>
        </TabPane>
        
        <TabPane tab="Update Existing" key="update">
          <Form
            form={updateForm}
            layout="vertical"
            onFinish={handleUpdateSubmit}
          >
            <Form.Item
              name="id"
              label="Select Table"
              rules={[{ required: true, message: "Please select a table" }]}
            >
              <Select
                placeholder="Select a table to update"
                loading={loading}
                onChange={handleTableSelect}
                options={lookupTables.map(table => ({
                  label: table.name,
                  value: table.id.toString()
                }))}
              />
            </Form.Item>
            
            <Form.Item
              name="name"
              label="Table Name"
              rules={[{ required: true, message: "Please enter a name" }]}
            >
              <Input placeholder="Enter a name for this lookup table" />
            </Form.Item>
            
            <Form.Item
              name="values"
              label="Values"
              rules={[{ required: true, message: "Please enter values" }]}
              help="Enter key-value pairs, one per line, separated by commas (e.g., 'blk,Black')"
            >
              <Input.TextArea
                rows={6}
                placeholder="blk,Black&#10;ylw,Yellow&#10;grn,Green&#10;etc..."
              />
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={submitting}
              >
                Update
              </Button>
            </Form.Item>
          </Form>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default VLookupModal;
