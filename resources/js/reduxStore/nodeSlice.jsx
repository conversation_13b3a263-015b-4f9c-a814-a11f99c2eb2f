import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
const getImportAction = (data) => {
  const isExportMode = window.location.href.includes("/export/");
  return isExportMode ? null : data.import_action || "Export";
};
const initialState = {
  nodes: [],
  warning: "",
  rowCount: 0,
  totalRowCount: 0,
  status: "idle",
  error: null,
  importAction: getImportAction(data),
  loading: false,
  previousFrom: {},
};

export const createMappingNodes = createAsyncThunk(
  "nodes/createMappingNodes",
  async (data) => {
    let mappingnodes = [];

    console.log("Processing data:", data.output_array);

    if (data.template_attributes.length === 0) {
      // Handling when template attributes length is 0
      if (data.data_required.template_method_type === "import") {
        for (const familyName in data.input_array.nodes) {
          const attributes = data.input_array.nodes[familyName];

          attributes.forEach((attributeName, index) => {
            // Stop creating new rows if nodes reach 300
            if (mappingnodes.length >= 300) return;

            const formulaValue = "assign";
            const fromValue = `${familyName},${attributeName}`;
            const withValue =
              data.input_array.nodes[familyName][index].with || "";
            const replaceValue =
              data.input_array.nodes[familyName][index].replace || "";
            let to = "";

            for (const outputNode of data.output_array.nodes) {
              if (outputNode.attributes) {
                const outputAttributes = Object.keys(outputNode.attributes);
                const outputAttributesValues = Object.values(
                  outputNode.attributes
                );
                const smalAttrName = attributeName
                  .replace(/\s+/g, "_")
                  .toLowerCase();
                if (outputAttributes.includes(smalAttrName)) {
                  to = `${outputNode.name},${smalAttrName}`;
                } else if (outputAttributesValues.includes(attributeName)) {
                  const index = outputAttributesValues.indexOf(attributeName);
                  const keyAtIndex = outputAttributes[index];
                  to = `${outputNode.name},${keyAtIndex}`;
                }
              }
            }

            const node = {
              from: fromValue,
              with_formula: formulaValue,
              to: to || "",
            };

            mappingnodes.push(node);
          });
        }
      } else {
        data.input_array.nodes.forEach((node, index) => {
          if (mappingnodes.length >= 300) return;

          const attributes = node.attributes;
          const familyName = node.name;

          if (attributes && typeof attributes === "object") {
            Object.entries(attributes).forEach(
              ([attributeName, attributeValue]) => {
                if (mappingnodes.length >= 300) return;

                const formulaValue = "assign";
                const fromValue = `${familyName},${attributeName}`;
                const toValue = attributeValue;

                const newNode = {
                  from: fromValue,
                  with_formula: formulaValue,
                  to: toValue || "",
                };

                mappingnodes.push(newNode);
              }
            );
          }
        });
      }
    } else {
      if (data.data_required.template_method_type === "import") {
        // Handling when template attributes length > 0 and method type is import
        const mappingnodesWithtemplate =
          data.template_attributes.template.data.map(
            ({ from, with_formula, to, with: withValue, replace }) => {
              if (mappingnodes.length >= 300) return;
              let flattenedFrom = Array.isArray(from)
                ? from.flat(Infinity)
                : [from || ""];
              // Special handling for 'merge' formula
              if (with_formula === "merge") {
                flattenedFrom[0] = flattenedFrom[0] || ""; // Convert null/undefined to empty string for index 0
                flattenedFrom[1] = flattenedFrom[1] || ""; // Convert null/undefined to empty string for index 1

                console.log(
                  flattenedFrom,
                  "flattened from (after merge logic)"
                );
              }

              // Ensure 'short_code' handles 'from' values correctly
              if (with_formula === "short_code") {
                flattenedFrom = flattenedFrom.map((value) => value || "");
              }
              const node = {
                from:
                  with_formula === "merge"
                    ? flattenedFrom
                    : flattenedFrom.filter(Boolean),
                with_formula,
                to: to || "",
              };
              console.log(node, "flat node");
              if (withValue !== null && withValue !== undefined) {
                node.with = withValue;
              } else {
                node.with = ""; // Add an empty string if withValue is null or undefined
              }

              if (replace !== null && replace !== undefined) {
                node.replace = replace;
              } else {
                node.replace = ""; // Add an empty string if replace is null or undefined
              }
              if (Array.isArray(node.from)) {
                if (node.from[0] === null) {
                  node.from[0] = [""];
                }
                if (node.from[1] === null) {
                  node.from[1] = [""];
                }
              }

              // Check for null values in to array
              if (Array.isArray(node.to)) {
                if (node.to[0] === null) {
                  node.to[0] = [""];
                }
                if (node.to[1] === null) {
                  node.to[1] = [""];
                }
              }
              return node;
            }
          );
        console.log(mappingnodesWithtemplate, "mappingnodesWithtemplate");
        let inputArrayName = "Default";
        const defaultArray = data.input_array.nodes?.Default || [];

        // Process and update nodes

        const updatedNodes = mappingnodesWithtemplate.map((node) => {
          let updatedNode = { ...node };

          if (Array.isArray(node.from)) {
            // Create a new array to store updated 'from' values
            const updatedFrom = node.from.map((fromValue) => {
              const fromString = fromValue;

              if (node.with_formula !== "short_code") {
                // Check if there is a match with any value in defaultArray
                const matchFoundFrom = defaultArray.some((value) => {
                  const fullValue = `${inputArrayName},${value}`;
                  return fromString === fullValue;
                });
                return matchFoundFrom ? fromValue : "";
              } else {
                return fromValue;
              }
            });
            // Filter out empty values

            updatedNode.from = updatedFrom;
          } else {
            console.log(
              node.from,
              "node.from is not an array or is undefined."
            );
          }

          // Handle 'to' field
          if (Array.isArray(node.to)) {
            // Create a new array to store updated 'to' values
            const updatedTo = node.to
              .map((toValue) => {
                const toString = toValue;

                const matchFoundTo = data.output_array.nodes.some(
                  (outputNode) => {
                    if (outputNode.attributes) {
                      return Object.entries(outputNode.attributes).some(
                        ([key, value]) => {
                          const fullValue = `${outputNode.name},${key}`;
                          return toString === fullValue;
                        }
                      );
                    }
                    return false;
                  }
                );
                return matchFoundTo ? toValue : "";
              })
              .filter((value) => value !== ""); // Filter out empty values

            updatedNode.to = updatedTo;
          } else {
            console.log(node.to, "node.to is not an array or is undefined.");
          }

          // Return the updated node if either 'from' or 'to' has valid values
          return updatedNode;
        });

        mappingnodes = updatedNodes;
      } else {
        // Handle cases where template attributes length > 0 but method type is not "import"
        const mappingnodesWithtemplate =
          data.template_attributes.template.data.map(
            ({ from, with_formula, to, with: withValue, replace }) => {
              if (mappingnodes.length >= 300) return;

              const node = {
                from: from || "",
                with_formula,
                to: to || "",
              };
              if (withValue !== null && withValue !== undefined) {
                node.with = withValue;
              } else {
                node.with = ""; // Add an empty string if withValue is null or undefined
              }

              if (replace !== null && replace !== undefined) {
                node.replace = replace;
              } else {
                node.replace = ""; // Add an empty string if replace is null or undefined
              }
              if (Array.isArray(from) && from[0] === null) {
                console.log(node.from, "node,from");
                node.from = [""];
              }
              if (Array.isArray(to) && to[0] === null) {
                node.to = [""];
              }
              return node;
            }
          );
        const updatedNodes = mappingnodesWithtemplate.map((node) => {
          let updatedNode = { ...node };
          // Handle 'From' field
          if (Array.isArray(node.from)) {
            // Create a new array to store updated 'to' values
            const updatedFrom = node.from
              .map((fromValue) => {
                const fromString = fromValue;
                if (node.with_formula !== "short_code") {
                  const matchFoundfrom = data.input_array.nodes.some(
                    (inputNode) => {
                      if (inputNode.attributes) {
                        return Object.entries(inputNode.attributes).some(
                          ([key, value]) => {
                            const fullValue = `${inputNode.name},${key}`;
                            return fromString === fullValue;
                          }
                        );
                      }
                      return false;
                    }
                  );
                  return matchFoundfrom ? fromValue : "";
                } else {
                  return fromValue;
                }
              })
              .filter((value) => value !== ""); // Filter out empty values

            updatedNode.from = updatedFrom;
          } else {
            console.log(node.to, "node.to is not an array or is undefined.");
          }

          // Return the updated node if either 'from' or 'to' has valid values
          return updatedNode;
        });

        mappingnodes = updatedNodes;
      }
    }
    return {
      nodes: mappingnodes,
    };
  }
);

export function calculateWarning(nodes) {
  let warningMessage = "";
  let anyDefaultHandle = false;
  const importAction = data.import_action; // Assuming data is accessible or pass it as a parameter
  let hasDefaultHandle = false;
  let hasVariantSku = false;

  const checkArray = (array) => {
    // Check if the input is an array and has valid values
    return Array.isArray(array) && array.length > 1 && array[0] && array[1];
  };

  nodes.forEach((node) => {
    const toValue = node.to;
    const fromSelectValue = node.from;

    // Check if from is an array and validate its values
    let validFrom = false;
    if (checkArray(fromSelectValue)) {
      const [fromValue1, fromValue2] = fromSelectValue;
      validFrom = fromValue1 && fromValue2;
    } else {
      validFrom = fromSelectValue !== "";
    }

    // Check if to is an array and validate its values
    let validTo = false;
    if (checkArray(toValue)) {
      const [toValue1, toValue2] = toValue;
      validTo = toValue1 && toValue2;
    } else {
      validTo = toValue !== "";
    }

    // Update conditions based on validFrom and validTo values
    if (importAction == 1) {
      if (validFrom && validTo) {
        if (
          toValue == "Default,handle" ||
          (Array.isArray(toValue) && toValue.join(",") == "Default,handle")
        ) {
          hasDefaultHandle = true;
        } else if (
          toValue == "Variant,sku" ||
          (Array.isArray(toValue) && toValue.join(",") == "Variant,sku")
        ) {
          hasVariantSku = true;
        }
      }
    } else if (importAction == 2) {
      if (
        validFrom &&
        (toValue == "Variant,sku" ||
          (Array.isArray(toValue) && toValue.join(",") == "Variant,sku"))
      ) {
        hasVariantSku = true;
      }
    } else if (importAction == 3) {
      if (
        validFrom &&
        (toValue == "Default,handle" ||
          (Array.isArray(toValue) && toValue.includes("Default,handle")))
      ) {
        hasDefaultHandle = true;
      }
    }
  });

  // Generate warning messages based on conditions
  if (importAction == 1) {
    if (!hasDefaultHandle && !hasVariantSku) {
      warningMessage = "Product Identifier and SKU column are not mapped.";
    } else if (!hasVariantSku) {
      warningMessage = "SKU column is not mapped.";
    } else if (!hasDefaultHandle) {
      warningMessage = "Product Identifier column is not mapped.";
    }
  } else if (importAction == 2 && !hasVariantSku) {
    warningMessage = "SKU column is not mapped.";
  } else if (importAction == 3 && !hasDefaultHandle) {
    warningMessage = "Product Identifier column is not mapped.";
  }

  return (
    warningMessage ||
    (anyDefaultHandle ? "All required columns are mapped." : "")
  );
}
function isBase64(str) {
  try {
    const cleanedStr = str.replace(/\s+/g, "");
    console.log(cleanedStr, "cleaned string");
    if (cleanedStr.length % 4 !== 0) {
      return false;
    }
    const validBase64Chars = /^[A-Za-z0-9+/]+={0,2}$/;
    if (!validBase64Chars.test(cleanedStr)) {
      return false;
    }
    return btoa(atob(cleanedStr)) === cleanedStr;
  } catch (err) {
    return false;
  }
}
const nodeSlice = createSlice({
  name: "nodes",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setNodes(state, action) {
      state.loading = true;
      state.nodes = action.payload.nodes;
      const invalidNodes = state.nodes.filter((node) => {
        if (
          (node.with_formula === "merge" &&
            (!node?.from?.[0] || !node?.from?.[1] || !node?.to)) ||
          (node.with_formula === "split" &&
            (!node?.from || !node?.to?.[0] || !node?.to?.[1] || !node?.with)) ||
          (node.with_formula === "calculate" &&
            (!node?.from || !node?.to || !node?.with)) ||
          (node.with_formula === "expand" &&
            (!node?.from || !node?.to || !node?.with)) ||
          (node.with_formula === "replace" &&
            (!node?.from || !node?.to || !node?.replace)) ||
          (node.with_formula === "assign" && (!node?.from || !node?.to)) ||
          (node.with_formula === "slug" && (!node?.from || !node?.to)) ||
          (node.with_formula === "vlookup" &&
            (!node?.from || !node?.to || !node?.with)) ||
          (node.with_formula === "short_code" && (!node?.from || !node?.to))
        ) {
          return true;
        }
        return node.to === "" || node.from === "";
      });

      state.rowCount = invalidNodes.length;
      state.totalRowCount = state.nodes.length;
      state.warning = calculateWarning(state.nodes);
      state.loading = false;
    },
    updateNode(state, action) {
      const { index, updatedNode } = action.payload;
      if (index >= 0 && index < state.nodes.length) {
        state.nodes[index] = { ...state.nodes[index], ...updatedNode };
        state.warning = calculateWarning(state.nodes);
      } else {
        console.error("Index out of bounds:", index);
      }
    },
    addNode(state) {
      state.loading = true;
      const newNode = { to: "", with_formula: "assign", from: "" }; // Example new node structure
      state.nodes.push(newNode);
      state.loading = false;
    },
    calculateCounts(state) {
      const invalidNodes = state.nodes.filter((node) => {
        const isEmptyArrayOrString = (value) =>
          Array.isArray(value) ? value.length === 0 : !value;

        if (
          (node.with_formula === "merge" &&
            (!node.from?.[0] ||
              !node.from?.[1] ||
              isEmptyArrayOrString(node.to))) ||
          (node.with_formula === "split" &&
            (isEmptyArrayOrString(node.from) ||
              !node.to?.[0] ||
              !node.to?.[1] ||
              !node.with)) ||
          (node.with_formula === "calculate" &&
            (isEmptyArrayOrString(node.from) ||
              isEmptyArrayOrString(node.to) ||
              !node.with)) ||
          (node.with_formula === "expand" &&
            (isEmptyArrayOrString(node.from) ||
              isEmptyArrayOrString(node.to) ||
              !node.with)) ||
          (node.with_formula === "replace" &&
            (isEmptyArrayOrString(node.from) ||
              isEmptyArrayOrString(node.to) ||
              !node.replace ||
              !node.with)) ||
          (node.with_formula === "assign" &&
            (isEmptyArrayOrString(node.from) ||
              isEmptyArrayOrString(node.to))) ||
          (node.with_formula === "slug" &&
            (isEmptyArrayOrString(node.from) ||
              isEmptyArrayOrString(node.to))) ||
          (node.with_formula === "vlookup" &&
            (isEmptyArrayOrString(node.from) ||
              isEmptyArrayOrString(node.to) ||
              !node.with)) ||
          (node.with_formula === "short_code" &&
            (isEmptyArrayOrString(node.from) || isEmptyArrayOrString(node.to)))
        ) {
          return true;
        }

        // Additional check for completely empty `from` or `to` fields
        return isEmptyArrayOrString(node.from) || isEmptyArrayOrString(node.to);
      });

      state.rowCount = invalidNodes.length; // Number of invalid rows
      state.totalRowCount = state.nodes.length; // Total number of nodes
    },
    removeNode(state, action) {
      state.loading = true;
      const index = action.payload;
      state.nodes.splice(index, 1);
      state.loading = false;
    },
    setWarning(state, action) {
      state.warning = action.payload;
    },
    addNewAttribute: (state, action) => {
      state.loading = true;
      const { rowIndex, fullAttribute } = action.payload;
      state.nodes[rowIndex].to = fullAttribute;
      state.warning = calculateWarning(state.nodes);
      state.loading = false;
    },
    updateFormula: (state, action) => {
      const { index, value } = action.payload;
      const node = state.nodes[index];

      // Save the previous 'from' value if the formula is "short_code"
      if (value === "short_code") {
        state.previousFrom[index] = Array.isArray(node.from)
          ? node.from[0]
          : node.from;
      } else {
        if (Array.isArray(node.from)) {
          node.from = node.from.map((item) => (isBase64(item) ? "" : item));
        } else {
          if (isBase64(node.from)) {
            node.from = "";
          }
        }
      }

      let updatedNode = { ...node, with_formula: value };

      switch (value) {
        case "merge":
          updatedNode = {
            ...updatedNode,
            from: [
              Array.isArray(node.from) && node.from.length > 0
                ? node.from[0]
                : state.previousFrom[index] !== undefined
                ? state.previousFrom[index]
                : node.from,
              Array.isArray(node.from) && node.from.length > 1
                ? node.from[1]
                : state.previousFrom[index] !== undefined
                ? state.previousFrom[index]
                : node.from,
            ],
            to: Array.isArray(node.to) ? node.to[0] : node.to,
            with: node.with ? node.with : "",
          };
          break;

        case "split":
          updatedNode = {
            ...updatedNode,
            from: Array.isArray(node.from)
              ? node.from[0]
              : state.previousFrom[index] !== undefined
              ? state.previousFrom[index]
              : node.from,
            to: [
              Array.isArray(node.to) && node.to.length > 0 ? node.to[0] : "",
              Array.isArray(node.to) && node.to.length > 1 ? node.to[1] : "",
            ],
            with: node.with ? node.with : "",
            replace: node.replace ? node.replace : "",
          };
          break;

        case "replace":
          updatedNode = {
            ...updatedNode,
            from: Array.isArray(node.from)
              ? node.from[0]
              : state.previousFrom[index] !== undefined
              ? state.previousFrom[index]
              : node.from,
            to: node.to,
            with: node.with ? node.with : "",
            replace: node.replace ? node.replace : "",
          };
          break;
        case "expand":
          updatedNode = {
            ...updatedNode,
            from: Array.isArray(node.from)
              ? node.from[0]
              : state.previousFrom[index] !== undefined
              ? state.previousFrom[index]
              : node.from,
            to: node.to,
            with: node.with ? node.with : "",
            replace: node.replace ? node.replace : "start",
          };
          break;

        case "short_code":
          updatedNode = {
            ...updatedNode,
            from: Array.isArray(node.from) ? "" : "",
            with: node.with ? node.with : "",
          };
          break;

        default:
          updatedNode = {
            ...updatedNode,
            from:
              state.previousFrom[index] !== undefined
                ? state.previousFrom[index]
                : Array.isArray(node.from)
                ? node.from[0]
                : node.from,
            to: Array.isArray(node.to)
              ? node.to[0] !== undefined
                ? node.to[0]
                : ""
              : node.to !== undefined
              ? node.to
              : "",
            with: "",
            replace: "",
          };
          break;
      }

      // Optionally clear the previousFrom entry if formula is not "short_code"
      if (value !== "short_code") {
        const { [index]: _, ...rest } = state.previousFrom; // Remove entry for the current index
        state.previousFrom = rest;
      }

      state.nodes[index] = updatedNode;
    },
  },

  extraReducers(builder) {
    builder
      .addCase(createMappingNodes.pending, (state) => {
        state.status = "loading";
      })
      .addCase(createMappingNodes.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.nodes = action.payload.nodes;
        const invalidNodes = state.nodes.filter((node) => {
          if (
            (node.with_formula === "merge" &&
              (!node?.from?.[0] || !node?.from?.[1] || !node?.to)) ||
            (node.with_formula === "split" &&
              (!node?.from ||
                !node?.to?.[0] ||
                !node?.to?.[1] ||
                !node?.with)) ||
            (node.with_formula === "calculate" &&
              (!node?.from || !node?.to || !node?.with)) ||
            (node.with_formula === "expand" &&
              (!node?.from || !node?.to || !node?.with)) ||
            (node.with_formula === "replace" &&
              (!node?.from || !node?.to || !node?.replace)) ||
            (node.with_formula === "assign" && (!node?.from || !node?.to)) ||
            (node.with_formula === "slug" && (!node?.from || !node?.to)) ||
            (node.with_formula === "vlookup" &&
              (!node?.from || !node?.to || !node?.with)) ||
            (node.with_formula === "short_code" && (!node?.from || !node?.to))
          ) {
            return true;
          }
          return node.to === "" || node.from === "";
        });

        // Update state with the count of invalid nodes
        state.rowCount = invalidNodes.length;
        state.totalRowCount = state.nodes.length;
        state.warning = calculateWarning(state.nodes, state.importAction);
      })
      .addCase(createMappingNodes.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });
  },
});

export const {
  updateNode,
  removeNode,
  addNode,
  calculateCounts,
  setNodes,
  setWarning,
  addNewAttribute,
  setLoading,
  updateFormula,
} = nodeSlice.actions;

export default nodeSlice.reducer;
