import axios from 'axios';

const BASE_URL = import.meta.env.VITE_APP_URL || 'http://localhost:8000';

axios.defaults.baseURL = BASE_URL;
axios.defaults.withCredentials = true; // Allow cookies to be sent with requests


const instance = axios.create({
    baseURL: `/api/${import.meta.env.VITE_API_VERSION}/`,
    headers: {
        'Content-Type': 'application/json',
    },
});


const handleErrors = (error, endpoint) => {
    console.error(`Error with request to ${endpoint}:`, error);
    throw error;
};

instance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response.status === 401) {
            const event = new CustomEvent('unauthorized');
            window.dispatchEvent(event);
        }
        return Promise.reject(error);
    }
);

const get = async (endpoint, authToken = null) => {
    try {
        const response = await instance.get(endpoint, {
            headers: {
                Authorization: `Bearer ${authToken}`,
            },
        });
        return response.data;
    } catch (error) {
        handleErrors(error, endpoint);
    }
};




const post = async (endpoint, data, authToken = null) => {
    try {
        const response = await instance.post(endpoint, data, {
            headers: {
                Authorization: `Bearer ${authToken}`,
            },
        });
        return response.data;
    } catch (error) {
        handleErrors(error, endpoint);
        // throw error.response;
    }
};

const put = async (endpoint, data, authToken = null) => {
    try {
        const response = await instance.put(endpoint, data, {
            headers: {
                Authorization: `Bearer ${authToken}`,
            },
        });
        return response.data;
    } catch (error) {
        handleErrors(error, endpoint);
    }
};

const destroy = async (endpoint, authToken = null) => {
    try {
        const response = await instance.delete(endpoint, {
            headers: {
                'Referrer-Policy': 'same-origin',
                Authorization: `Bearer ${authToken}`,
            },
        });
        return response.data;
    } catch (error) {
        handleErrors(error, endpoint);
    }
};

export { instance, get, post, put, destroy };
