// MappingForm.js

import React, { useState, useEffect } from "react";
import axios from "axios";
import AddVLookupModal from "./producttabsfields/AddVLookupModal";
import CreateAttributeModal from "./producttabsfields/CreateAttributeModal";
import { useSelector, useDispatch } from "react-redux";
import { updateNode } from "../reduxStore/nodeSlice";
import { Input, Select } from "antd";
import { lastIndexOf, map, remove, set } from "lodash";

const MappingForm = ({ data, index, nodes, onRemoveNode }) => {
  const [attrModalIndex, setAttrModalIndex] = useState(null);
  const input_array = data.input_array;
  const [output_array, setOutputArr] = useState(data.output_array);
  const [mappingnodes, setMappingNodes] = useState(nodes);
  const dispatch = useDispatch();
  const nodeRedux = useSelector((state) => state.nodeRedux.nodes[index]);

  const [allFamilies, setAllFamilies] = useState(
    data.apimio_attributes_required?.all_families
      ? data.apimio_attributes_required.all_families
      : []
  );
  const [allAttributes, setAllAttributes] = useState(
    data.apimio_attributes_required?.all_attributes
      ? data.apimio_attributes_required.all_attributes
      : []
  );
  const [formulaValues, setFormulaValues] = useState(() =>
    nodes.map((node) => node.with_formula || "assign")
  );
  useEffect(() => {
    setMappingNodes(nodes);
  }, [nodes]);

  const defaultNodeName = Object.keys(data.input_array.nodes);
  const [vLookupData, setVLookupData] = useState([]);
  const fetchVLookupData = async () => {
    try {
      const response = await axios.post("/vlookup/fetch");
      const data = response.data.data;
      setVLookupData(data);
    } catch (error) {
      console.error("Error fetching VLookup data:", error);
    }
  };
  const [addVLookupModal, setAddVLookupModal] = useState(0);
  const handleCloseModal = () => {
    setAddVLookupModal(0);
    fetchVLookupData();
  };

  useEffect(() => {
    if (formulaValues[index] === "vlookup") {
      fetchVLookupData();
    }
  }, [formulaValues]);
  const handleRemove = () => {
    dispatch(removeNode(index));
    // $("#delete-item").addClass("block");
    // onRemoveNode(index);
    // setTimeout(function () {
    //   var rowCount = $(".warning-msg:not(.hidden)").length;
    //   $("#invalid_row_count").text(rowCount);
    //   var totalRowCount = $(".mapping-item").length;
    //   $("#total_row_count").text(totalRowCount);
    //   if (rowCount === 0) {
    //     $(".row-count").addClass("hidden");
    //     $(".all-mapped").removeClass("hidden");
    //     if (data.data_required.template_method_type === "import") {
    //       if ($("#handle_warning").hasClass("hidden")) {
    //         $("#pro_imp_btn").removeAttr("disabled", "disabled");
    //       } else {
    //         // $("#pro_imp_btn").attr("disabled", "disabled");
    //       }
    //     } else {
    //       $("#pro_imp_btn").removeAttr("disabled", "disabled");
    //     }
    //     $("#ignore_unmapped").prop("checked", true);
    //     $(".proceed-data-switch").addClass("hidden");
    //     $(".proceed-data-switch").removeClass("d-flex");
    //   } else {
    //     $(".row-count").removeClass("hidden");
    //     $(".all-mapped").addClass("hidden");
    //     $(".proceed-data-switch").removeClass("hidden");
    //     $(".proceed-data-switch").addClass("d-flex");
    //     // $("#pro_imp_btn").attr("disabled", "disabled");
    //   }

    //   $("#delete-item").removeClass("block");

    //   //setDeleteModal(0);
    // }, 200);
  };

  const onattrCloseModal = (updatedData, index) => {
    console.log(updatedData, "updatedData");
    setAllAttributes(updatedData.apimio_attributes_required.all_attributes);
    if (updatedData && updatedData.apimio_attributes_required) {
      setAllFamilies(updatedData.apimio_attributes_required.all_families);
    }
    setAttrModalIndex(null);

    setOutputArr(updatedData.output_array);
    // Refresh the select element options
    updateSelectOptions(updatedData, index);
  };
  const updateSelectOptions = (updatedData, index) => {
    var currIndex = index;
    const selectElement = $(
      "select[name='nodes[data][" + currIndex + "][to][]']"
    );
    // Extract the nodes from both arrays
    const nodes1 = data.output_array.nodes;
    const nodes2 = updatedData.output_array.nodes;
    // Iterate through the nodes of output_array in both arrays
    nodes2.forEach((family2, index) => {
      const family1 = typeof nodes1[index] !== "undefined" ? nodes1[index] : "";
      setTimeout(function () {
        if (family2.name == family1.name) {
          const attributes1 = family1.attributes;
          const attributes2 = family2.attributes;

          for (const key in attributes2) {
            if (
              !attributes1 ||
              !(key in attributes1) ||
              attributes2[key] !== attributes1[key]
            ) {
              var createdAttributefamily = family2.name;
              var createdAttribute = attributes2[key];
              createdAttributeLabel = attributes2[key];
              createdAttribute = createdAttribute
                .toLowerCase()
                .replace(/\s+/g, "_");
              var completeValue =
                createdAttributefamily + "," + createdAttribute;
              document
                .querySelectorAll(".mapping-to-select")
                .forEach(function (toElement) {
                  // Find the optgroup with createdAttributefamily
                  var optgroup = toElement.querySelector(
                    'optgroup[label="' + createdAttributefamily + '"]'
                  );
                  if (
                    toElement.getAttribute("name") !==
                    selectElement.attr("name")
                  ) {
                    // Find the optgroup with createdAttributefamily
                    var optgroup = toElement.querySelector(
                      'optgroup[label="' + createdAttributefamily + '"]'
                    );
                    if (optgroup) {
                      // Append completeValue as an option value and createdAttribute as an option label
                      var option = document.createElement("option");
                      option.value = completeValue;
                      option.text = createdAttributeLabel;
                      optgroup.appendChild(option);
                    }
                  }
                });
              if (selectElement.length === 0) {
                console.log(
                  "The select element with the specified name was not found."
                );
              } else {
                let optionFound = false;
                selectElement.find("option").each(function () {
                  if (completeValue === $(this).val()) {
                    console.log(completeValue, "added value old family");
                    // $(this).prop("selected", true);
                    optionFound = true;

                    // setMappingNodes((prevNodes) => {
                    //   return prevNodes.map((node, i) => {
                    //     if (i === currIndex) {
                    //       return {
                    //         ...node,
                    //         to: completeValue || node.to,
                    //       };
                    //     } else {
                    //       return node;
                    //     }
                    //   });
                    // });
                    return false;
                  }
                });
                if (!optionFound) {
                  console.log("Option not found for", completeValue);
                  // Handle the case when option is not found, if needed
                }
              }
            }
          }
        } else {
          const newattribute = family2.attributes;
          var createdAttributefamily = family2.name;
          for (const key in newattribute) {
            var createdAttribute = newattribute[key];
            var createdAttributeLabel = newattribute[key];
            createdAttribute = createdAttribute
              .toLowerCase()
              .replace(/\s+/g, "_");
            var completeValue = createdAttributefamily + "," + createdAttribute;
            document
              .querySelectorAll(".mapping-to-select")
              .forEach(function (toElement) {
                // Check if optgroup with createdAttributefamily exists
                if (
                  toElement.getAttribute("name") !== selectElement.attr("name")
                ) {
                  var optgroup = toElement.querySelector(
                    'optgroup[label="' + createdAttributefamily + '"]'
                  );
                  if (!optgroup) {
                    // If optgroup doesn't exist, create a new one
                    optgroup = document.createElement("optgroup");
                    optgroup.label = createdAttributefamily;
                    toElement.appendChild(optgroup);
                  }
                  // Append completeValue as an option value and createdAttribute as an option label
                  var option = document.createElement("option");
                  option.value = completeValue;
                  option.text = createdAttributeLabel;
                  optgroup.appendChild(option);
                }
              });
            selectElement.find("option").each(function () {
              if (completeValue === $(this).val()) {
                // $(this).prop("selected", true);
                // setMappingNodes((prevNodes) => {
                //   return prevNodes.map((node, i) => {
                //     if (i === currIndex) {
                //       console.log(completeValue, "added value new family");
                //       return {
                //         ...node,
                //         to: completeValue || node.to,
                //       };
                //     } else {
                //       return node;
                //     }
                //   });
                // });
                return false;
              }
            });
          }
        }
        data = updatedData;
        console.log(data, "Data");
      }, 150);
      setTimeout(function () {
        var rowCount = $(".warning-msg:not(.hidden)").length;
        $("#invalid_row_count").text(rowCount);
        var totalRowCount = $(".mapping-item").length;
        $("#total_row_count").text(totalRowCount);
        if (rowCount < 1) {
          if ($("#handle_warning").hasClass("hidden")) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").attr("disabled", "disabled");
          }
          $(".proceed-data-switch").addClass("hidden");
          $(".proceed-data-switch").removeClass("d-flex");
          $(".row-count").addClass("hidden");
          $(".all-mapped").removeClass("hidden");
        } else {
          $(".row-count").removeClass("hidden");
          $(".all-mapped").addClass("hidden");
          // $("#pro_imp_btn").attr("disabled", "disabled");
          $(".proceed-data-switch").removeClass("hidden");
          $(".proceed-data-switch").addClass("d-flex");
        }
      }, 185);
    });
  };
  const [savedFromValue, setSavedFromValue] = useState("");
  const handleFormulaChange = (e, index) => {
    const value = e.target.value;
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          let updatedNode = { ...node, with_formula: value };
          if (value === "merge") {
            const fromArray = [
              savedFromValue || node.from,
              savedFromValue || node.from,
            ];
            updatedNode = {
              ...updatedNode,
              from: fromArray,
              to: Array.isArray(node.to) ? node.to[0] : node.to,
              with: "",
              with_formula: value,
            };
          } else if (value === "split") {
            const toArray = Array.isArray(node.to) ? node.to : [node.to];
            toArray[0] = node.to;
            toArray[1] = node.to;
            updatedNode = {
              ...updatedNode,
              from: Array.isArray(node.from)
                ? node.from[0]
                : savedFromValue || node.from,
              to: toArray,
              with: "",
              replace: "",
              with_formula: value,
            };
          } else if (value === "replace") {
            updatedNode = {
              ...updatedNode,
              from: Array.isArray(node.from)
                ? node.from[0]
                : savedFromValue || node.from,
              to: node.to,
              with: "",
              replace: "",
              with_formula: value,
            };
          } else if (value === "short_code") {
            setSavedFromValue(
              Array.isArray(node.from) ? node.from[0] : node.from
            );
            updatedNode = {
              ...updatedNode,
              from: "",
              with_formula: value,
            };
          } else {
            updatedNode = {
              ...updatedNode,
              from: Array.isArray(node.from)
                ? node.from[0]
                : savedFromValue || node.from,
              to: Array.isArray(node.to) ? node.to[0] : node.to,
              with: "",
              with_formula: value,
            };
          }
          return updatedNode;
        } else {
          return node;
        }
      });
    });
    const newFormulaValues = [...formulaValues];
    newFormulaValues[index] = value;
    setFormulaValues(newFormulaValues);

    setTimeout(function () {
      var rowCount = $(".warning-msg:not(.hidden)").length;
      $("#invalid_row_count").text(rowCount);
      var totalRowCount = $(".mapping-item").length;
      $("#total_row_count").text(totalRowCount);
      var anyDefaultHandle = false;
      if (data.data_required.template_method_type === "import") {
        if (rowCount < 1) {
          if ($("#handle_warning").hasClass("hidden")) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").attr("disabled", "disabled");
          }
          $(".proceed-data-switch").addClass("hidden");
          $(".proceed-data-switch").removeClass("d-flex");
          $(".row-count").addClass("hidden");
          $(".all-mapped").removeClass("hidden");
        } else {
          $(".row-count").removeClass("hidden");
          $(".all-mapped").addClass("hidden");
          $("#pro_imp_btn").attr("disabled", "disabled");
          $(".proceed-data-switch").removeClass("hidden");
          $(".proceed-data-switch").addClass("d-flex");
        }
      } else {
        if (rowCount < 1) {
          $("#pro_imp_btn").removeAttr("disabled", "disabled");

          $(".proceed-data-switch").addClass("hidden");
          $(".proceed-data-switch").removeClass("d-flex");
          $(".row-count").addClass("hidden");
          $(".all-mapped").removeClass("hidden");
        } else {
          $(".row-count").removeClass("hidden");
          $(".all-mapped").addClass("hidden");
          $("#pro_imp_btn").attr("disabled", "disabled");
          $(".proceed-data-switch").removeClass("hidden");
          $(".proceed-data-switch").addClass("d-flex");
        }
      }
    }, 50);
  };

  const formulaOptions = [
    { value: "assign", label: "Assign" },
    { value: "split", label: "Split" },
    { value: "merge", label: "Merge (Basic)" },
    { value: "short_code", label: "Merge (Advance)" },
    { value: "replace", label: "Replace" },
    { value: "slug", label: "Slug" },
    { value: "vlookup", label: "Vlookup" },
    { value: "calculate", label: "Calculate" },
    { value: "expand", label: "Expand" },
  ];
  const handleSelectChange = (e) => {
    const value = e.target.value;
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          if (node.with_formula === "merge") {
            const fromArray = Array.isArray(node.from)
              ? node.from
              : [node.from];
            fromArray[0] = value;
            return { ...node, from: fromArray };
          } else {
            return { ...node, from: value };
          }
        } else {
          return node;
        }
      });
    });
  };
  $(document).ready(function () {
    $(document).on("input", ".short_code_div ", function (e) {
      var hidden_field_short_code = $(this)
        .closest(".form-group")
        .find(".short_code_hidden_field");
      var short_code_div = $(this).html();
      hidden_field_short_code.val(btoa(short_code_div));
      var hfsc_name = hidden_field_short_code.attr("name");
      var match = hfsc_name.match(/\d+/);

      // Check if match is found and get the first match
      if (match !== null) {
        var number = match[0];
        mappingnodes[number].from = hidden_field_short_code.val();
      }
    });
    $(document).on("click", ".clear_content", function (e) {
      var short_code_div = $(this)
        .closest(".form-group")
        .find(".short_code_div");
      short_code_div.empty();
      var hidden_field_short_code = $(this)
        .closest(".form-group")
        .find(".short_code_hidden_field");
      hidden_field_short_code.val("");
      var mappinItem = $(this).parents(".mapping-item");
      var hfsc_name = hidden_field_short_code.attr("name");
      var match = hfsc_name.match(/\d+/);

      // Check if match is found and get the first match
      if (match !== null) {
        var number = match[0];
        mappingnodes[number].from = "";
      }
    });
  });
  const handleSelectAssign = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleInputsplit = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, with: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleSelectSplit = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          const toArray = Array.isArray(node.to) ? node.to : [node.to];
          // Update the array with the new value at index 0
          toArray[0] = value;
          // Update the node with the new 'to' array
          return { ...node, to: toArray };
        } else {
          return node;
        }
      });
    });
  };
  const handleSelectSplit2 = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          const toArray = Array.isArray(node.to) ? node.to : [node.to];
          // Update the array with the new value at index 0
          toArray[1] = value;
          // Update the node with the new 'to' array
          return { ...node, to: toArray };
        } else {
          return node;
        }
      });
    });
  };
  const handleMergeInput = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, with: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleMergeSelect = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      return prevNodes.map((node, i) => {
        if (i === index) {
          const fromArray = Array.isArray(node.from) ? node.from : [node.from];
          fromArray[1] = value;
          return { ...node, from: fromArray };
        } else {
          return node;
        }
      });
    });
  };
  const handleMergeSelect2 = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleSelectShortCode = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleReplaceInputSearch = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, replace: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleReplaceInputWith = (e, index) => {
    const value = e.target.value;

    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, with: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleReplaceSelect = (e, index) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleAttributeClick = (index) => {
    setAttrModalIndex(index);
  };

  const handleSelectSlug = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleVLookupSelect = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleAddVlookup = (e) => {
    setAddVLookupModal(1);
  };
  const handleVLookupInput = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, with: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleCalculateSelect = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const isValidInput = (input) => {
    // Regular expression to match valid input: one arithmetic operator followed by numbers
    const regex = /^[+\-*/]?\d*(\.\d+)?%?$/;
    return regex.test(input);
  };
  const handleCalculateInput = (e, index) => {
    const value = e.target.value;
    if (!isValidInput(value)) {
      // You can handle invalid input here, like showing an error message
      return;
    }

    // Update the state with the valid input
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, with: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleExpandPositionSelect = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, replace: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleExpandInput = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, with: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleExpandSelect = (e) => {
    const value = e.target.value;
    //setAssignValue(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, to: value };
        } else {
          return node;
        }
      });
    });
  };
  const handleContentEditableChange = (e) => {
    let value = e.target.textContent;

    const decodedvalue = btoa(value);
    setMappingNodes((prevNodes) => {
      // Create a new array with the updated node at the specified index
      return prevNodes.map((node, i) => {
        if (i === index) {
          return { ...node, from: decodedvalue };
        } else {
          return node;
        }
      });
    });
    setTimeout(function () {
      var rowCount = $(".warning-msg:not(.hidden)").length;
      $("#invalid_row_count").text(rowCount);
      var totalRowCount = $(".mapping-item").length;
      $("#total_row_count").text(totalRowCount);
      var anyDefaultHandle = false;
      if (data.data_required.template_method_type === "import") {
        if (rowCount < 1) {
          if ($("#handle_warning").hasClass("hidden")) {
            $("#pro_imp_btn").removeAttr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").attr("disabled", "disabled");
          }
          $(".proceed-data-switch").addClass("hidden");
          $(".proceed-data-switch").removeClass("d-flex");
          $(".row-count").addClass("hidden");
          $(".all-mapped").removeClass("hidden");
        } else {
          $(".row-count").removeClass("hidden");
          $(".all-mapped").addClass("hidden");
          $("#pro_imp_btn").attr("disabled", "disabled");
          $(".proceed-data-switch").removeClass("hidden");
          $(".proceed-data-switch").addClass("d-flex");
        }
      } else {
        if (rowCount < 1) {
          $("#pro_imp_btn").removeAttr("disabled", "disabled");

          $(".proceed-data-switch").addClass("hidden");
          $(".proceed-data-switch").removeClass("d-flex");
          $(".row-count").addClass("hidden");
          $(".all-mapped").removeClass("hidden");
        } else {
          $(".row-count").removeClass("hidden");
          $(".all-mapped").addClass("hidden");
          $("#pro_imp_btn").attr("disabled", "disabled");
          $(".proceed-data-switch").removeClass("hidden");
          $(".proceed-data-switch").addClass("d-flex");
        }
      }
    }, 50);
  };
  const renderToSelect = () => {
    // Conditionally render additional select(s) based on the selected formula
    if (formulaValues[index] === "assign") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleSelectAssign(e, index)}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;
                                if (output_array) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleSelectAssign(e, index)}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "split") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">Separator</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={(e) => handleInputsplit(e, index)}
                    value={
                      mappingnodes[index].with ? mappingnodes[index].with : ""
                    }
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute (1)
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select1"
                      onChange={(e) => handleSelectSplit(e, index)}
                      value={mappingnodes[index]?.to[0]}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;
                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select1"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to[0]}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleSelectSplit(e, index)}
                      value={mappingnodes[index]?.to[0]}
                    />
                  )}
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute (2)
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select2"
                      onChange={(e) => handleSelectSplit2(e, index)}
                      value={mappingnodes[index]?.to[1]}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select2"
                      onChange={(e) => handleSelectSplit2(e, index)}
                      value={mappingnodes[index]?.to[1]}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleSelectSplit2(e, index)}
                      value={mappingnodes[index]?.to[1]}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "merge") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">Glue</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={(e) => handleMergeInput(e, index)}
                    value={
                      mappingnodes[index].with ? mappingnodes[index].with : ""
                    }
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">
                    {input_array.array_name || "Input"} Attribute (2)
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select2"
                      name={`nodes[data][${index}][from][]`}
                      onChange={(e) => handleMergeSelect(e, index)}
                      value={mappingnodes[index]?.from[1]}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      <optgroup label={defaultNodeName || "Others"}>
                        {Object.keys(input_array.nodes.Default).map(
                          (inputKey, optionIndex) => (
                            <option
                              value={
                                defaultNodeName +
                                "," +
                                input_array.nodes.Default[inputKey]
                              }
                              key={inputKey}
                            >
                              {input_array.nodes.Default[inputKey]}
                            </option>
                          )
                        )}
                      </optgroup>
                    </select>
                  ) : (
                    <select
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select2"
                      name={`nodes[data][${index}][from][]`}
                      onChange={(e) => handleMergeSelect(e, index)}
                      value={mappingnodes[index]?.from[1]}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {input_array.nodes.map((familyNode, familyIndex) =>
                        // Check if familyNode.attributes exists and has entries
                        familyNode.attributes &&
                        Object.entries(familyNode.attributes).length > 0 ? (
                          <optgroup label={familyNode.name} key={familyIndex}>
                            {Object.entries(familyNode.attributes).map(
                              ([attributeName, attributeValue], inputIndex) => {
                                return (
                                  <option
                                    value={`${familyNode.name},${attributeName}`}
                                    key={inputIndex}
                                  >
                                    {attributeValue}
                                  </option>
                                );
                              }
                            )}
                          </optgroup>
                        ) : (
                          <optgroup
                            label={familyNode.name}
                            key={familyIndex}
                          ></optgroup>
                        )
                      )}
                    </select>
                  )}
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleMergeSelect2(e, index)}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  const isSelected =
                                    output_array[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleMergeSelect2}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleMergeSelect2(e, index)}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "short_code") {
      return (
        <div className="sm:w-4/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <label className="block">
                {output_array.array_name ? output_array.array_name : "Output"}{" "}
                Attribute
              </label>
              {data.data_required.template_method_type === "import" ? (
                <select
                  name={`nodes[data][${index}][to][]`}
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                  onChange={(e) => handleSelectShortCode(e, index)}
                  value={mappingnodes[index]?.to}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>
                  {output_array.nodes.map((attr_families, groupIndex) => (
                    <optgroup
                      label={attr_families.name ? attr_families.name : "Others"}
                      key={groupIndex}
                    >
                      {attr_families.attributes &&
                        Object.entries(attr_families.attributes).map(
                          ([attr_key, attr_value]) => {
                            const family_output_value = attr_families.name
                              ? `${attr_families.name},${attr_key}`
                              : attr_key;

                            return (
                              <option
                                value={family_output_value}
                                key={attr_key}
                              >
                                {attr_value}
                              </option>
                            );
                          }
                        )}
                    </optgroup>
                  ))}
                </select>
              ) : data.data_required.template_method_type === "shopify" ? (
                <select
                  name={`nodes[data][${index}][to][]`}
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                  onChange={handleExpandSelect}
                  value={mappingnodes[index]?.to}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>
                  {output_array.nodes.map((attr_families, groupIndex) => (
                    <optgroup
                      label={attr_families.name ? attr_families.name : "Others"}
                      key={groupIndex}
                    >
                      {attr_families.attributes &&
                        Object.entries(attr_families.attributes).map(
                          ([attr_key, attr_value]) => {
                            const family_output_value = attr_families.name
                              ? `${attr_families.name},${attr_key}`
                              : attr_key;

                            if (output_array.length > 0) {
                              return (
                                <option
                                  value={family_output_value}
                                  key={attr_key}
                                >
                                  {attr_value}
                                </option>
                              );
                            } else {
                              const isSelected = false;
                              return (
                                <option
                                  value={family_output_value}
                                  key={attr_key}
                                >
                                  {attr_value}
                                </option>
                              );
                            }
                          }
                        )}
                    </optgroup>
                  ))}
                </select>
              ) : (
                <input
                  type="text"
                  name={`nodes[data][${index}][to]`}
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                  onChange={(e) => handleSelectShortCode(e, index)}
                  value={mappingnodes[index]?.to}
                />
              )}
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "replace") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">Search</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][replace]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-replace-input"
                    onChange={(e) => handleReplaceInputSearch(e, index)}
                    value={mappingnodes[index]?.replace}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">With</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={(e) => handleReplaceInputWith(e, index)}
                    value={mappingnodes[index]?.with}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleReplaceSelect(e, index)}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleReplaceSelect}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "slug") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleSelectSlug(e, index)}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleSelectSlug(e, index)}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "vlookup") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    V-Lookup
                    <button
                      className="ml-2 text-primary text-sm"
                      onClick={handleAddVlookup}
                    >
                      ( Add )
                    </button>
                  </label>

                  <select
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={(e) => handleVLookupInput(e, index)}
                    value={mappingnodes[index]?.with}
                  >
                    <option value="">Choose</option>
                    {vLookupData.map((item) => (
                      <option key={item.id} value={item.id} id={item.id}>
                        {item.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleVLookupSelect(e, index)}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={(e) => handleVLookupSelect(e, index)}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "calculate") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    With
                    <span className="relative tooltip-custom text-black ml-2">
                      <div className="text-left tooltip-content absolute bottom-0 -right-52 bg-white p-2 shadow-xl border border-gray-300">
                        <p className="font-bold mb-2">Example values:</p>
                        <span className="block my-1 pl-2">+50 Addition</span>
                        <span className="block my-1 pl-2">-50 Subtraction</span>
                        <span className="block my-1 pl-2">50% percentage</span>
                        <span className="block my-1 pl-2">
                          +50% add percentage
                        </span>
                        <span className="block my-1 pl-2">
                          -50% sub percentage
                        </span>
                        <span className="block my-1 pl-2">
                          For multiplication dont use any sign before value
                        </span>
                      </div>
                      ( e.g )
                    </span>
                  </label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={(e) => handleCalculateInput(e, index)}
                    value={mappingnodes[index]?.with}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleCalculateSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleCalculateSelect}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "expand") {
      return (
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">Position</label>
                  <select
                    name={`nodes[data][${index}][replace]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-replace-input"
                    onChange={handleExpandPositionSelect}
                    value={
                      mappingnodes[index].replace
                        ? mappingnodes[index].replace
                        : "start"
                    }
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>
                    <option value="start">Start</option>
                    <option value="end">End</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">With</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with][]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={handleExpandInput}
                    value={
                      mappingnodes[index].with ? mappingnodes[index].with : ""
                    }
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-3/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-11/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : data.data_required.template_method_type === "shopify" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={mappingnodes[index]?.to}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderFromSelect = () => {
    // Conditionally render additional select(s) based on the selected formula
    if (formulaValues[index] === "short_code") {
      function isValidBase64(str) {
        try {
          return btoa(atob(str)) === str;
        } catch (err) {
          return false;
        }
      }

      const valueToDecode = mappingnodes[index]?.from;
      if (isValidBase64(valueToDecode)) {
        const decodedValue = atob(valueToDecode);
        // Proceed with using decodedValue
      } else {
      }
      return (
        <div className="sm:w-4/12 w-6/12 from merge-advance from">
          <div className="flex flex-wrap">
            <div className="w-10/12">
              <div className="form-group">
                <label htmlFor="name_in_import">
                  {" "}
                  {input_array.array_name || "Input"} Content
                </label>
                <input
                  type="hidden"
                  className="left_array apimio-column short_code_hidden_field mapping-from-select"
                  name={`nodes[data][${index}][from][]`}
                  value={mappingnodes[index]?.from}
                />

                <div
                  contentEditable="true"
                  className="form-control short_code_div bg-white-smoke"
                  data-placeholder="Enter your content with multiple elements"
                  onInput={handleContentEditableChange}
                ></div>

                <div className="d-flex justify-content-between mt-1">
                  <div>
                    <button
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title="Clear Content"
                      type="button"
                      className="bg-transparent border-0 d-block clear_content"
                    >
                      <i className="fa fa-refresh" aria-hidden="true"></i>
                    </button>
                  </div>
                  <div>
                    <div className="dropdown">
                      <button
                        className="btn dropdown-toggle insert_element_title"
                        type="button"
                        id="dropdownMenuButton"
                        data-bs-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="false"
                      >
                        {"{ }"} Insert Element
                      </button>

                      <div
                        className="dropdown-menu insert_element_main bg-white-smoke"
                        aria-labelledby="dropdownMenuButton"
                      >
                        {data.data_required.template_method_type ===
                        "import" ? (
                          <div>
                            <h6 className="dropdown-header insert_element_head">
                              {defaultNodeName || "Others"}
                            </h6>
                            <div className="insert_element_content"></div>
                            {Object.keys(input_array.nodes.Default).map(
                              (inputKey, optionIndex) => {
                                const fullValue =
                                  defaultNodeName +
                                  "," +
                                  input_array.nodes.Default[inputKey];

                                return (
                                  <button
                                    key={optionIndex}
                                    className="dropdown-item insert_element"
                                    value={"{{" + fullValue + "}}"}
                                    onClick={handleContentEditableChange}
                                  >
                                    {input_array.nodes.Default[inputKey]}
                                  </button>
                                );
                              }
                            )}
                          </div>
                        ) : (
                          <div>
                            <div className="insert_element_content"></div>
                            {input_array.nodes.map(
                              (familyNode, familyIndex) => (
                                <>
                                  <h6
                                    className="dropdown-header insert_element_head"
                                    key={familyIndex}
                                  >
                                    {familyNode.attributes &&
                                    Object.keys(familyNode.attributes).length >
                                      0
                                      ? familyNode.name || "Others"
                                      : familyNode.name}
                                  </h6>
                                  {familyNode.attributes &&
                                    Object.entries(familyNode.attributes).map(
                                      (
                                        [attributeName, attributeValue],
                                        inputIndex
                                      ) => {
                                        let familyInputValue;
                                        familyInputValue = `${familyNode.name},${attributeName}`;

                                        return (
                                          <button
                                            value={
                                              "{{" + familyInputValue + "}}"
                                            }
                                            className="dropdown-item insert_element"
                                            key={inputIndex}
                                            onClick={
                                              handleContentEditableChange
                                            }
                                          >
                                            {attributeValue}
                                          </button>
                                        );
                                      }
                                    )}
                                </>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (formulaValues[index] === "merge") {
      return (
        <div className="sm:w-3/12 w-6/12 from">
          <div className="flex flex-wrap">
            <div className="w-8/12">
              <label className="block">
                {input_array.array_name || "Input"} Attribute
              </label>
              {data.data_required.template_method_type === "import" ? (
                <select
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select1"
                  name={`nodes[data][${index}][from][]`}
                  value={
                    mappingnodes[index].from[0]
                      ? mappingnodes[index]?.from[0]
                      : ""
                  }
                  onChange={handleSelectChange}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>

                  <optgroup label={defaultNodeName}>
                    {Object.keys(input_array.nodes.Default).map(
                      (inputKey, optionIndex) => {
                        const fullValue =
                          defaultNodeName +
                          "," +
                          input_array.nodes.Default[inputKey];

                        return (
                          <option value={fullValue} key={inputKey}>
                            {input_array.nodes.Default[inputKey]}
                          </option>
                        );
                      }
                    )}
                  </optgroup>
                </select>
              ) : (
                <select
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select1"
                  name={`nodes[data][${index}][from][]`}
                  value={mappingnodes[index]?.from[0]}
                  onChange={handleSelectChange}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>
                  {input_array.nodes.map((familyNode, familyIndex) =>
                    // Check if familyNode.attributes exists and has entries
                    familyNode.attributes &&
                    Object.entries(familyNode.attributes).length > 0 ? (
                      <optgroup label={familyNode.name} key={familyIndex}>
                        {Object.entries(familyNode.attributes).map(
                          ([attributeName, attributeValue], inputIndex) => {
                            return (
                              <option
                                value={`${familyNode.name},${attributeName}`}
                                key={inputIndex}
                              >
                                {attributeValue}
                              </option>
                            );
                          }
                        )}
                      </optgroup>
                    ) : (
                      <optgroup
                        label={familyNode.name}
                        key={familyIndex}
                      ></optgroup>
                    )
                  )}
                </select>
              )}
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="sm:w-3/12 w-6/12 from">
          <div className="flex flex-wrap">
            <div className="w-8/12">
              <label className="block">
                {input_array.array_name || "Input"} Attribute
              </label>
              {data.data_required.template_method_type === "import" ? (
                <select
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
                  name={`nodes[data][${index}][from][]`}
                  value={mappingnodes[index]?.from}
                  onChange={handleSelectChange}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>

                  <optgroup label={defaultNodeName}>
                    {Object.keys(input_array.nodes.Default).map(
                      (inputKey, optionIndex) => {
                        const fullValue =
                          defaultNodeName +
                          "," +
                          input_array.nodes.Default[inputKey];

                        return (
                          <option value={fullValue} key={inputKey}>
                            {input_array.nodes.Default[inputKey]}
                          </option>
                        );
                      }
                    )}
                  </optgroup>
                </select>
              ) : (
                <select
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
                  name={`nodes[data][${index}][from][]`}
                  value={mappingnodes[index].from}
                  onChange={handleSelectChange}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>
                  {input_array.nodes.map((familyNode, familyIndex) =>
                    // Check if familyNode.attributes exists and has entries
                    familyNode.attributes &&
                    Object.entries(familyNode.attributes).length > 0 ? (
                      <optgroup label={familyNode.name} key={familyIndex}>
                        {Object.entries(familyNode.attributes).map(
                          ([attributeName, attributeValue], inputIndex) => {
                            return (
                              <option
                                value={`${familyNode.name},${attributeName}`}
                                key={inputIndex}
                              >
                                {attributeValue}
                              </option>
                            );
                          }
                        )}
                      </optgroup>
                    ) : (
                      <optgroup
                        label={familyNode.name}
                        key={familyIndex}
                      ></optgroup>
                    )
                  )}
                </select>
              )}
            </div>
          </div>
        </div>
      );
    }
    return null;
  };
  return (
    <>
      <div
        key={index}
        dataid={index}
        className={`mapping-item relative mt-2 mb-3 px-4 pt-2 pb-2 justify-content-between border-l-8 mapping-${index}
              ${
                formulaValues[index] === "merge" &&
                (!mappingnodes[index]?.from[0] ||
                  !mappingnodes[index]?.from[1] ||
                  !mappingnodes[index]?.to)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "split" &&
                    (!mappingnodes[index]?.from ||
                      !mappingnodes[index]?.to[0] ||
                      !mappingnodes[index]?.to[1] ||
                      !mappingnodes[index]?.with)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "calculate" &&
                    (!mappingnodes[index]?.from ||
                      !mappingnodes[index]?.to ||
                      !mappingnodes[index]?.with)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "expand" &&
                    (!mappingnodes[index]?.from ||
                      !mappingnodes[index]?.to ||
                      !mappingnodes[index]?.with)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "replace" &&
                    (!mappingnodes[index]?.from ||
                      !mappingnodes[index]?.to ||
                      !mappingnodes[index]?.replace)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "assign" &&
                    (!mappingnodes[index]?.from || !mappingnodes[index]?.to)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "slug" &&
                    (!mappingnodes[index]?.from || !mappingnodes[index]?.to)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "short_code" &&
                    (!mappingnodes[index]?.from || !mappingnodes[index]?.to)
                  ? "border-t border-r border-b  border-yellow-500"
                  : formulaValues[index] === "vlookup" &&
                    (!mappingnodes[index]?.from ||
                      !mappingnodes[index]?.to ||
                      !mappingnodes[index]?.with)
                  ? "border-t border-r border-b  border-yellow-500"
                  : "border-green-500"
              }
              rounded-md shadow-md`}
      >
        <div className="flex flex-wrap main-row">
          {renderFromSelect()}
          <div className="sm:w-3/12 w-6/12 formula">
            <div className="flex flex-wrap align-items-center h-full">
              <div className="w-8/12">
                <label className="block">Formula</label>
                <select
                  name={`nodes[data][${index}][with_formula]`}
                  className="formula_field w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent"
                  id={`formula-${index}`} // Unique ID for each select element
                  value={mappingnodes[index]?.with_formula}
                  onChange={(e) => handleFormulaChange(e, index)}
                >
                  {formulaOptions.map((option, optionIndex) => (
                    <option
                      key={optionIndex}
                      value={option.value}
                      className="Poppins regular text-color"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="w-2/12 mx-auto mt-8">
                <i className="icon-arrow-right"></i>
              </div>
            </div>
          </div>
          {renderToSelect()}
        </div>
        <div
          className="text-red-500 absolute right-2 top-2 close_row"
          onClick={() => handleRemove(index)}
        >
          <i className="fa-solid fa-times cursor-pointer"></i>
        </div>
        {data.data_required.template_method_type === "import" ? (
          <div className="absolute right-2 bottom-2">
            {formulaValues[index] === "merge" &&
            (!mappingnodes[index]?.from || !mappingnodes[index]?.to) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : formulaValues[index] === "split" &&
              (!mappingnodes[index]?.from ||
                !mappingnodes[index]?.to[0] ||
                !mappingnodes[index]?.to[1] ||
                !mappingnodes[index]?.with) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : formulaValues[index] === "calculate" &&
              (!mappingnodes[index]?.from ||
                !mappingnodes[index]?.to ||
                !mappingnodes[index]?.with) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : formulaValues[index] === "expand" &&
              (!mappingnodes[index]?.from ||
                !mappingnodes[index]?.to ||
                !mappingnodes[index]?.with) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : formulaValues[index] === "replace" &&
              (!mappingnodes[index]?.from ||
                !mappingnodes[index]?.to ||
                !mappingnodes[index]?.replace) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : formulaValues[index] === "assign" &&
              (!mappingnodes[index]?.from || !mappingnodes[index]?.to) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : formulaValues[index] === "short_code" &&
              (!mappingnodes[index]?.from || !mappingnodes[index]?.to) ? (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            ) : null}
          </div>
        ) : (
          ""
        )}
        {formulaValues[index] === "merge" &&
        (!mappingnodes[index]?.from[0] ||
          !mappingnodes[index]?.from[1] ||
          !mappingnodes[index]?.to) ? (
          <div className="flex my-2 warning-msg merge">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "split" &&
          (!mappingnodes[index]?.from ||
            !mappingnodes[index]?.to[0] ||
            !mappingnodes[index]?.to[1] ||
            !mappingnodes[index]?.with) ? (
          <div className="flex my-2 warning-msg split">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "calculate" &&
          (!mappingnodes[index]?.from ||
            !mappingnodes[index]?.to ||
            !mappingnodes[index]?.with) ? (
          <div className="flex my-2 warning-msg calc">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "expand" &&
          (!mappingnodes[index]?.from ||
            !mappingnodes[index]?.to ||
            !mappingnodes[index]?.with) ? (
          <div className="flex my-2 warning-msg expand">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "replace" &&
          (!mappingnodes[index]?.from ||
            !mappingnodes[index]?.to ||
            !mappingnodes[index]?.replace) ? (
          <div className="flex my-2 warning-msg replace">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "assign" &&
          (!mappingnodes[index]?.from || !mappingnodes[index]?.to) ? (
          <div className="flex my-2 warning-msg assign">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "slug" &&
          (!mappingnodes[index]?.from || !mappingnodes[index]?.to) ? (
          <div className="flex my-2 warning-msg assign">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "short_code" &&
          (!mappingnodes[index]?.from || !mappingnodes[index]?.to) ? (
          <div className="flex my-2 warning-msg assign">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : formulaValues[index] === "vlookup" &&
          (!mappingnodes[index]?.from ||
            !mappingnodes[index]?.to ||
            !mappingnodes[index]?.with) ? (
          <div className="flex my-2 warning-msg assign">
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">
              Please map all required fields for this row.
            </span>
          </div>
        ) : null}
      </div>
      {addVLookupModal === 0 ? (
        ""
      ) : (
        <AddVLookupModal handleCloseModal={handleCloseModal} />
      )}
      {attrModalIndex !== null && (
        <CreateAttributeModal
          allFamilies={allFamilies}
          allAttributes={allAttributes}
          index={attrModalIndex}
          mappingnodes={mappingnodes}
          attrModal={attrModalIndex !== null}
          onattrCloseModal={onattrCloseModal}
          attributeCloseModal={() => setAttrModalIndex(null)}
        />
      )}
    </>
  );
};

export default MappingForm;
