import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchUserData,
    clearError,
    updateUser,
    logout,
    selectUser,
    selectIsAuthenticated,
    selectAuthLoading,
    selectAuthError,
} from "../store/slices/authSlice";

export default function AuthComponent() {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");

    const dispatch = useDispatch();
    const user = useSelector(selectUser);
    const isAuthenticated = useSelector(selectIsAuthenticated);
    const loading = useSelector(selectAuthLoading);
    const error = useSelector(selectAuthError);

    const handleLogin = async (e) => {
        e.preventDefault();

        try {
            // Clear any previous errors
            dispatch(clearError());

            // This is a mock login - in a real app, you would call your API
            if (username === "demo" && password === "password") {
                // Use the fetchUserData thunk to simulate login
                dispatch(fetchUserData());
            } else {
                // For demo purposes, we'll just show an error
                // In a real app, you would handle login failure differently
                console.error("Invalid username or password");
            }
        } catch (err) {
            console.error("Login failed:", err.message);
        }
    };

    const handleLogout = () => {
        dispatch(logout());
    };

    if (isAuthenticated) {
        return (
            <div className="p-4 bg-white rounded-lg shadow-md">
                <h2 className="text-2xl font-bold mb-4">Welcome, {user.name}!</h2>
                <div className="mb-4">
                    <p>
                        <strong>Email:</strong> {user.email}
                    </p>
                    <p>
                        <strong>Username:</strong> {user.username}
                    </p>
                </div>
                <button onClick={handleLogout} className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition">
                    Logout
                </button>
            </div>
        );
    }

    return (
        <div className="p-4 bg-white rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-4">Login</h2>
            {error && <div className="p-2 mb-4 bg-red-100 text-red-700 rounded">{error}</div>}

            <form onSubmit={handleLogin} className="space-y-4">
                <div>
                    <label className="block mb-1">Username</label>
                    <input
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="w-full p-2 border rounded"
                        placeholder="demo"
                        disabled={loading}
                    />
                </div>

                <div>
                    <label className="block mb-1">Password</label>
                    <input
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full p-2 border rounded"
                        placeholder="password"
                        disabled={loading}
                    />
                </div>

                <button
                    type="submit"
                    className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
                    disabled={loading}
                >
                    {loading ? "Logging in..." : "Login"}
                </button>

                <div className="text-sm text-gray-500">
                    <p>Demo credentials:</p>
                    <p>Username: demo</p>
                    <p>Password: password</p>
                </div>
            </form>
        </div>
    );
}
