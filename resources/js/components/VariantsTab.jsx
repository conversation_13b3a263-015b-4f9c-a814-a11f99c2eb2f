import React, { useState, useEffect } from "react";

function VariantsTab({ updateVariantsData }) {
  const { input_array, row_node, input_row_selector, family } = data;
  const [selectedId, setSelectedId] = useState("Default,handle Id");
  const [addedFields, setAddedFields] = useState(0);
  const [variantOptions, setVariantOptions] = useState(["", "", ""]);

  const defaultNodeName = Object.keys(data.input_array.nodes)[0]; // Fetch the node name dynamically

  const handleIdChange = (e) => {
    setSelectedId(e.target.value);
    updateVariantsData({ id: e.target.value, variant_options: variantOptions });
  };
  const handleVariantOptionChange = (index, e) => {
    const updatedOptions = [...variantOptions];
    updatedOptions[index] = e.target.value;
    setVariantOptions(updatedOptions);
    updateVariantsData({ id: selectedId, variant_options: updatedOptions });
  };

  const addNewOption = () => {
    if (addedFields < 3) {
      setAddedFields(addedFields + 1);
    }
  };
  const getSelectedVariants = () => {
    const selectedIds = [];
    selectedId.push(selectedId);
    return selectedId;
  };
  useEffect(() => {
    // Set the selected value based on the index
    if (input_array.nodes.Default) {
      const defaultNodeName = Object.keys(data.input_array.nodes)[0];
      const inputKey = input_array.nodes.Default;
      const familyInputValue = defaultNodeName + "," + inputKey;
      setSelectedId("Default,handle Id");
    } else {
      setSelectedId("Default,handle Id"); // No item at this index
    }
  }, [input_array.nodes.Default]);

  return (
    <div className="w-full variant">
      <div className="flex flex-wrap">
        <div className="w-full">
          <label className="block">Variants Group Identifier</label>
          <select
            className="w-4/12 border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-variant-select"
            name={`nodes[variant][id]`}
            value={selectedId}
            onChange={handleIdChange}
          >
            <option value="" className="Poppins regular text-color">
              Select Field
            </option>
            <optgroup label={defaultNodeName || "Others"}>
              {Object.keys(input_array.nodes.Default).map(
                (inputKey, optionIndex) =>
                  Array.isArray(input_array.nodes.Default[inputKey]) ? (
                    input_array.nodes.Default[inputKey].map(
                      (inputVal, inputIndex) => {
                        const familyInputValue =
                          defaultNodeName +
                          "," +
                          input_array.nodes.Default[inputKey];

                        return (
                          <option value={selectedId} key={inputIndex}>
                            {input_array.nodes.Default[inputKey]}
                          </option>
                        );
                      }
                    )
                  ) : (
                    <option
                      value={
                        defaultNodeName +
                        "," +
                        input_array.nodes.Default[inputKey]
                      }
                      selected={
                        defaultNodeName +
                          "," +
                          input_array.nodes.Default[inputKey] ===
                        selectedId
                      }
                      key={inputKey}
                    >
                      {input_array.nodes.Default[inputKey]}
                    </option>
                  )
              )}
            </optgroup>
          </select>
        </div>

        <div id="variant-options" className="w-full mt-4">
          <label className="block">Variants Options</label>
          <div className="variant-option flex flex-wrap">
            {[...Array(addedFields)].map((_, index) => (
              <div key={index} className="variant-option sm:w-3/12 w-full">
                <select
                  className="border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-variant-options inline-flex w-10/12"
                  name={`nodes[variant][variant_options][]`}
                  value={variantOptions[index]}
                  onChange={(e) => handleVariantOptionChange(index, e)}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>
                  <optgroup label={defaultNodeName || "Others"}>
                    {Object.keys(input_array.nodes.Default).map(
                      (inputKey, optionIndex) =>
                        Array.isArray(input_array.nodes.Default[inputKey]) ? (
                          input_array.nodes.Default[inputKey].map(
                            (inputVal, inputIndex) => (
                              <option value={selectedId} key={inputIndex}>
                                {input_array.nodes.Default[inputKey]}
                              </option>
                            )
                          )
                        ) : (
                          <option
                            value={
                              defaultNodeName +
                              "," +
                              input_array.nodes.Default[inputKey]
                            }
                            key={inputKey}
                          >
                            {input_array.nodes.Default[inputKey]}
                          </option>
                        )
                    )}
                  </optgroup>
                </select>
                <div className="inline-flex w-2/12">
                  <button
                    className="btn-delete  ml-2"
                    onClick={() => setAddedFields(addedFields - 1)}
                  >
                    <i className="fa-regular fa-trash-can fs-20 text-danger"></i>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
        {addedFields < 3 && (
          <button
            id="add-variant-option-btn"
            className="btn btn-primary mt-4"
            onClick={addNewOption}
          >
            Add New Option
          </button>
        )}
      </div>
    </div>
  );
}

export default VariantsTab;
