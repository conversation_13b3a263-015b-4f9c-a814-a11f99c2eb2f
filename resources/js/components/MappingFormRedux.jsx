import React, { useEffect, useState, useCallback, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateNode, removeNode, updateFormula } from "../reduxStore/nodeSlice";
import ContentEditableField from "./contentEditable";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import _ from "lodash";
const isBase64 = (str) => {
  try {
    return btoa(atob(str)) === str;
  } catch (err) {
    return false;
  }
};
const MappingForm = memo(
  ({
    index,
    data,
    handleHeightChange,
    handleAttributeChange,
    handleOpenVlookup,
  }) => {
    const dispatch = useDispatch();
    const node = useSelector((state) => state.nodes.nodes[index]);
    const { totalRowCount } = useSelector((state) => state.nodes);
    const [formula, setFormula] = useState(node.with_formula || "assign");
    const defaultNodeName = Object.keys(data.input_array.nodes);
    const [content, setContent] = useState(() => {
      if (node.with_formula == "short_code") {
      }
      if (isBase64(node?.from)) {
        return atob(node.from);
      }
      return node?.from;
    });

    const isValidBase64 = (str) => {
      try {
        return btoa(atob(str)) === str;
      } catch (err) {
        return false;
      }
    };
    const [initialValue, setInitialValue] = useState("");
    useEffect(() => {
      if (node.with_formula == "short_code") {
        let valueToDecode = node?.from;
        if (
          valueToDecode === null ||
          (Array.isArray(valueToDecode) && valueToDecode[0] === null)
        ) {
          valueToDecode = ""; // Set valueToDecode to an empty string if it's null or [null]
        } else if (Array.isArray(valueToDecode) && valueToDecode.length === 0) {
          // If node.from is an empty array, set it to [""] (an array with an empty string)
          valueToDecode = [""];
        }
        if (Array.isArray(valueToDecode)) {
          valueToDecode = valueToDecode[0]; // Extract the first element if it's an array
        }
        if (Array.isArray(valueToDecode)) {
          valueToDecode = valueToDecode[0]; // Extract the first element if it's an array
        }
        // Now, ensure valueToDecode is a string before applying replace
        if (typeof valueToDecode == "string") {
          valueToDecode = valueToDecode.replace(/[\[\]']/g, ""); // Remove square brackets and single quotes
        } else {
          //console.error("valueToDecode is not a string");
        }
        if (isValidBase64(valueToDecode)) {
          const decoded = atob(valueToDecode);
          setInitialValue(decoded);
          setContent(decoded);
        } else {
          setContent("");
        }
      }
    }, [node]);
    useEffect(() => {
      if (formula == "vlookup") {
        fetchVLookupData();
      }
    }, [formula]);
    const [savedFromValue, setSavedFromValue] = useState("");
    const handleAddVlookup = (index) => {
      handleOpenVlookup(index);
    };
    useEffect(() => {
      setFormula(node.with_formula);
      handleHeightChange(index);
    }, [node, index, handleHeightChange]);

    const handleAttributeClick = (index) => {
      handleAttributeChange(index);
    };

    const handleFormulaChange = useCallback(
      (e) => {
        const value = e.target.value;

        // Generate the updated node based on the formula
        // const updatedNode = generateUpdatedNode(value);

        // // Dispatch the action to update the node in the Redux store
        dispatch(updateFormula({ index, value }));

        // Update the local state for formula
        setFormula(value);
      },
      [dispatch, index, node, savedFromValue, handleHeightChange]
    );

    const handleSelectChange = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { from: value } }));
      },
      [dispatch, index]
    );
    const handleSelectAssign = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-to-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-to-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const handleInputsplit = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { with: value } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-split-${index}`,
          function () {
            $(`#input-export-split-${index}`).focus();
          }
        );
      },
      [dispatch, index]
    );

    const handleSelectSplit = useCallback(
      (e) => {
        const value = e.target.value;
        const updatedTo = Array.isArray(node.to) ? [...node.to] : [node.to];

        // Update the first element of the 'to' array
        updatedTo[0] = value;
        dispatch(updateNode({ index, updatedNode: { to: updatedTo } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-splitselect-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-splitselect-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );

    const handleSelectSplit2 = useCallback(
      (e) => {
        const value = e.target.value;
        const updatedTo = Array.isArray(node.to) ? [...node.to] : [node.to];

        // Update the first element of the 'to' array
        updatedTo[1] = value;
        dispatch(updateNode({ index, updatedNode: { to: updatedTo } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-splitselect2-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-splitselect2-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const handleMergeFrom = useCallback(
      (e) => {
        const value = e.target.value;

        const updatedFrom = Array.isArray(node.from)
          ? [...node.from]
          : [node.from];

        // Update the first element of the 'to' array
        updatedFrom[0] = value;
        dispatch(updateNode({ index, updatedNode: { from: updatedFrom } }));
      },
      [dispatch, index]
    );
    const handleMergeInput = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { with: value } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-merge-${index}`,
          function () {
            $(`#input-export-merge-${index}`).focus();
          }
        );
      },
      [dispatch, index]
    );
    const handleMergeSelect = useCallback(
      (e) => {
        const value = e.target.value;
        const updatedFrom = Array.isArray(node.from)
          ? [...node.from]
          : [node.from];

        // Update the first element of the 'to' array
        updatedFrom[1] = value;
        dispatch(updateNode({ index, updatedNode: { from: updatedFrom } }));
      },
      [dispatch, index]
    );
    const handleMergeSelect2 = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-mergeselect-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-mergeselect-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const handleSelectShortCode = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));

        $(document).on(
          "input keypress paste change",
          `input-export-shorcode-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`input-export-shorcode-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const handleReplaceInputSearch = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { replace: value } }));
        $(document).on(
          "input keypress paste change",
          `#input-export-replace-${index}`,
          function () {
            $(`#input-export-replace-${index}`).focus();
          }
        );
      },
      [dispatch, index]
    );
    const handleReplaceInputWith = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { with: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-replacewith-${index}`,
          function () {
            $(`#input-export-replacewith-${index}`).focus();
          }
        );
      },
      [dispatch, index]
    );
    const handleReplaceSelect = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-replaceselect-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-replaceselect-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const handleSelectSlug = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-slug-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-slug-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const handleVLookupSelect = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-vlookup-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-vlookup-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );

    const handleVLookupInput = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { with: value } }));
      },
      [dispatch, index]
    );
    const handleCalculateSelect = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-calculateselect-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-calculateselect-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const isValidInput = (input) => {
      // Regular expression to match valid input: one arithmetic operator followed by numbers
      const regex = /^[+\-*/]?(\d+(\.\d*)?|\.\d+)?%?$/;

      return regex.test(input);
    };
    const handleCalculateInput = useCallback(
      (e) => {
        const value = e.target.value;
        if (!isValidInput(value)) {
          // You can handle invalid input here, like showing an error message
          return;
        }
        dispatch(updateNode({ index, updatedNode: { with: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-calculate-${index}`,
          function () {
            $(`#input-export-calculate-${index}`).focus();
          }
        );
      },
      [dispatch, index]
    );
    const handleExpandPositionSelect = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { replace: value } }));
      },
      [dispatch, index]
    );
    const handleExpandInput = useCallback(
      (e) => {
        const value = e.target.value;
        dispatch(updateNode({ index, updatedNode: { with: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-expand-${index}`,
          function () {
            $(`#input-export-expand-${index}`).focus();
          }
        );
      },
      [dispatch, index]
    );
    const handleExpandSelect = useCallback(
      (e) => {
        const value = e.target.value;

        dispatch(updateNode({ index, updatedNode: { to: value } }));
        $(document).on(
          "input keypress paste",
          `#input-export-expandselect-${index}`,
          function () {
            if (data.data_required.template_method_type == "export") {
              $(`#input-export-expandselect-${index}`).focus();
            }
          }
        );
      },
      [dispatch, index]
    );
    const [isRemoving, setIsRemoving] = useState(false);
    const handleRemove = useCallback(() => {
      setIsRemoving(true);
      setTimeout(() => {
        dispatch(removeNode(index));
      }, 300); // Match this duration with your CSS transition time
    }, [dispatch, index]);
    const [vLookupData, setVLookupData] = useState([]);
    const fetchVLookupData = useCallback(async () => {
      try {
        const response = await axios.post("/vlookup/fetch");
        const data = response.data.data;
        setVLookupData(data);
      } catch (error) {
        toast.error("Error fetching VLookup data:", error);
      }
    }, []);
    const getBorderClass = () => {
      if (
        (formula === "merge" &&
          (!node?.from?.[0] ||
            !node?.from?.[1] ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "split" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to?.[0] ||
            !node?.to?.[1] ||
            !node?.with)) ||
        (formula === "calculate" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "expand" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "replace" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.replace ||
            !node?.with)) ||
        (formula === "assign" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "slug" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "vlookup" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "short_code" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0))
      ) {
        return "border-t border-r border-b border-yellow-500";
      }
      return "border-green-500";
    };
    const [hiddenFieldValue, setHiddenFieldValue] = useState(node?.from || "");

    const restoreFocus = () => {
      // Get the contenteditable element
      const contentEditableElement = document.getElementById(
        `editable-content-${index}`
      );

      if (contentEditableElement) {
        contentEditableElement.focus(); // Focus on the element

        // Create a range object and set the start and end points to the end of the content
        const range = document.createRange();
        const selection = window.getSelection();

        // Clear any existing selections
        selection.removeAllRanges();

        // Set the range to the end of the content
        range.selectNodeContents(contentEditableElement);
        range.collapse(false); // Collapse to the end
        // Apply the new range
        selection.addRange(range);
      }
    };
    const handleContentEditableInputChange = useCallback(
      (encodedContent) => {
        dispatch(updateNode({ index, updatedNode: { from: encodedContent } }));
      },
      [dispatch, index]
    );
    const [contentEditableModalShow, setContentEditableModalShow] = useState(0);
    const handleContentEditableValue = useCallback(
      (value) => {
        console.log(value, "value");
        let newContent = value;
        if (newContent === "<div><br></div>" || newContent === "<br>") {
          newContent = ""; // Clear out the unwanted elements
        }
        setContent(newContent);

        const encodedContent = btoa(newContent);
        dispatch(updateNode({ index, updatedNode: { from: encodedContent } }));
        setContentEditableModalShow(0);
        $(".list-group").removeClass("editing");
      },
      [dispatch, index]
    );

    const handleContentEditableClose = () => {
      $(".list-group").removeClass("editing");
      setContentEditableModalShow(0);
    };
    const handleContentEditableChange = (e) => {
      $(".list-group").addClass("editing");
      setContentEditableModalShow(1);
      console.log(index, "check", totalRowCount);
      setTimeout(() => {
        const selectedDiv = $(`#editable-content-${index + 1}`);
        if (totalRowCount - index < 4) {
          console.log(selectedDiv, "selectedDiv");
          selectedDiv.addClass("bottom-fullimpo");
        } else {
          selectedDiv.removeClass("bottom-fullimpo");
        }

        restoreFocus();
      }, 10);
    };

    const renderWarningMessage = () => {
      if (
        (formula === "merge" &&
          (!node?.from?.[0] ||
            !node?.from?.[1] ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "split" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to?.[0] ||
            !node?.to?.[1] ||
            !node?.with)) ||
        (formula === "calculate" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "expand" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "replace" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.replace ||
            !node?.with)) ||
        (formula === "assign" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "slug" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "vlookup" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "short_code" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0))
      ) {
        return "Please map all required fields for this row.";
      }
      return null;
    };
    const renderCreateAttributeModal = () => {
      if (
        (formula === "merge" &&
          (!node?.from?.[0] ||
            !node?.from?.[1] ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "split" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to?.[0] ||
            !node?.to?.[1] ||
            !node?.with)) ||
        (formula === "calculate" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "expand" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "replace" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.replace ||
            !node?.with)) ||
        (formula === "assign" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "slug" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0)) ||
        (formula === "vlookup" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0 ||
            !node?.with)) ||
        (formula === "short_code" &&
          (!node?.from ||
            node.from.length === 0 ||
            !node?.to ||
            node.to.length === 0))
      ) {
        return true;
      }
      return false;
    };

    const formulaOptions = [
      { value: "assign", label: "Assign" },
      { value: "split", label: "Split" },
      { value: "merge", label: "Merge (Basic)" },
      { value: "short_code", label: "Merge (Advance)" },
      { value: "replace", label: "Replace" },
      { value: "slug", label: "Slug" },
      { value: "vlookup", label: "Vlookup" },
      { value: "calculate", label: "Calculate" },
      { value: "expand", label: "Expand" },
    ];
    const renderToSelect = () => {
      // Conditionally render additional select(s) based on the selected formula

      if (formula == "assign") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectAssign}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;
                                    if (data.output_array) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectAssign}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-to-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select export"
                        onChange={handleSelectAssign}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "split") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">Separator</label>
                    <input
                      id={`input-export-split-${index}`}
                      type="text"
                      name={`nodes[data][${index}][with]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                      onChange={handleInputsplit}
                      value={node.with ? node.with : ""}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute (1)
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select1"
                        onChange={handleSelectSplit}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;
                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select1"
                        onChange={handleSelectSplit}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-splitselect-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectSplit}
                        value={node?.to[0]}
                      />
                    )}
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute (2)
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select2"
                        onChange={handleSelectSplit2}
                        value={Array.isArray(node.to) ? node.to[1] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select mapping-to-select2"
                        onChange={handleSelectSplit2}
                        value={Array.isArray(node.to) ? node.to[1] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-splitselect2-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectSplit2}
                        value={node?.to[1]}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "merge") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">Glue</label>
                    <input
                      id={`input-export-merge-${index}`}
                      type="text"
                      name={`nodes[data][${index}][with]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                      onChange={handleMergeInput}
                      value={node.with ? node.with : ""}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">
                      {data.input_array.array_name || "Input"} Attribute (2)
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select2"
                        name={`nodes[data][${index}][from][]`}
                        onChange={handleMergeSelect}
                        value={
                          Array.isArray(node.from) ? node.from[1] : node.from
                        }
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        <optgroup label={defaultNodeName || "Others"}>
                          {Object.keys(data.input_array.nodes.Default).map(
                            (inputKey, optionIndex) => (
                              <option
                                value={
                                  defaultNodeName +
                                  "," +
                                  data.input_array.nodes.Default[inputKey]
                                }
                                key={inputKey}
                              >
                                {data.input_array.nodes.Default[inputKey]}
                              </option>
                            )
                          )}
                        </optgroup>
                      </select>
                    ) : (
                      <select
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select2"
                        name={`nodes[data][${index}][from][]`}
                        onChange={handleMergeSelect}
                        value={
                          Array.isArray(node.from) ? node.from[1] : node.from
                        }
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.input_array.nodes.map((familyNode, familyIndex) =>
                          // Check if familyNode.attributes exists and has entries
                          familyNode.attributes &&
                          Object.entries(familyNode.attributes).length > 0 ? (
                            <optgroup label={familyNode.name} key={familyIndex}>
                              {Object.entries(familyNode.attributes).map(
                                (
                                  [attributeName, attributeValue],
                                  inputIndex
                                ) => {
                                  return (
                                    <option
                                      value={`${familyNode.name},${attributeName}`}
                                      key={inputIndex}
                                    >
                                      {attributeValue}
                                    </option>
                                  );
                                }
                              )}
                            </optgroup>
                          ) : (
                            <optgroup
                              label={familyNode.name}
                              key={familyIndex}
                            ></optgroup>
                          )
                        )}
                      </select>
                    )}
                  </div>
                </div>
              </div>
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleMergeSelect2}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      const isSelected =
                                        data.output_array[index]?.to[0] ===
                                        family_output_value;

                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleMergeSelect2}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-mergeselect-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={(e) => handleMergeSelect2(e, index)}
                        value={node?.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "short_code") {
        return (
          <div className="sm:w-4/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-4/12 w-6/12">
                <label className="block">
                  {data.output_array.array_name
                    ? data.output_array.array_name
                    : "Output"}{" "}
                  Attribute
                </label>
                {data.data_required.template_method_type === "import" ? (
                  <select
                    name={`nodes[data][${index}][to][]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                    onChange={(e) => handleSelectShortCode(e, index)}
                    value={Array.isArray(node.to) ? node.to[0] : node.to}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>
                    {data.output_array.nodes.map(
                      (attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                return (
                                  <option
                                    value={family_output_value}
                                    key={attr_key}
                                  >
                                    {attr_value}
                                  </option>
                                );
                              }
                            )}
                        </optgroup>
                      )
                    )}
                  </select>
                ) : data.data_required.template_method_type === "shopify" ? (
                  <select
                    name={`nodes[data][${index}][to][]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                    onChange={handleSelectShortCode}
                    value={Array.isArray(node.to) ? node.to[0] : node.to}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>
                    {data.output_array.nodes.map(
                      (attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (data.output_array.length > 0) {
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      )
                    )}
                  </select>
                ) : (
                  <input
                    id={`input-export-shorcode-${index}`}
                    type="text"
                    name={`nodes[data][${index}][to]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                    onChange={handleSelectShortCode}
                    value={node?.to}
                  />
                )}
              </div>
            </div>
          </div>
        );
      } else if (formula === "replace") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">Search</label>
                    <input
                      id={`input-export-replace-${index}`}
                      type="text"
                      name={`nodes[data][${index}][replace]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-replace-input"
                      onChange={handleReplaceInputSearch}
                      value={node.replace ? node.replace : ""}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">With</label>
                    <input
                      id={`input-export-replacewith-${index}`}
                      type="text"
                      name={`nodes[data][${index}][with]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                      onChange={handleReplaceInputWith}
                      value={node.with ? node.with : ""}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleReplaceSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleExpandSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-replaceselect-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleReplaceSelect}
                        value={node?.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "slug") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectSlug}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectSlug}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-slug-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleSelectSlug}
                        value={node?.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "vlookup") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      V-Lookup
                      <button
                        className="ml-2 text-primary text-sm"
                        onClick={() => handleAddVlookup(index)}
                      >
                        ( Add )
                      </button>
                    </label>

                    <select
                      name={`nodes[data][${index}][with]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                      onChange={(e) => handleVLookupInput(e, index)}
                      value={node.with ? node.with : ""}
                    >
                      <option value="">Choose</option>
                      {vLookupData.map((item) => (
                        <option key={item.id} value={item.id} id={item.id}>
                          {item.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={(e) => handleVLookupSelect(e, index)}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleVLookupSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-vlookup-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleVLookupSelect}
                        value={node?.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "calculate") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      With
                      <span className="relative tooltip-custom text-black ml-2">
                        <div className="text-left tooltip-content absolute bottom-0 -right-52 bg-white p-2 shadow-xl border border-gray-300">
                          <p className="font-bold mb-2">Example values:</p>
                          <span className="block my-1 pl-2">
                            *50 Multiplication
                          </span>
                          <span className="block my-1 pl-2">+50 Addition</span>
                          <span className="block my-1 pl-2">
                            -50 Subtraction
                          </span>
                          <span className="block my-1 pl-2">
                            +50% add percentage
                          </span>
                          <span className="block my-1 pl-2">
                            -50% sub percentage
                          </span>
                        </div>
                        ( e.g )
                      </span>
                    </label>
                    <input
                      id={`input-export-calculate-${index}`}
                      type="text"
                      name={`nodes[data][${index}][with]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                      onChange={(e) => handleCalculateInput(e, index)}
                      value={node.with ? node.with : ""}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:w-4/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-8/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleCalculateSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleCalculateSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-calculateselect-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleCalculateSelect}
                        value={node?.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "expand") {
        return (
          <div className="sm:w-6/12 w-full to align-self-sm-center">
            <div className="flex flex-wrap">
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">Position</label>
                    <select
                      name={`nodes[data][${index}][replace]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-replace-input"
                      onChange={handleExpandPositionSelect}
                      value={node.replace ? node.replace : "start"}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      <option value="start">Start</option>
                      <option value="end">End</option>
                    </select>
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">With</label>
                    <input
                      id={`input-export-expand-${index}`}
                      type="text"
                      name={`nodes[data][${index}][with][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                      onChange={handleExpandInput}
                      value={node.with ? node.with : ""}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:w-3/12 w-6/12">
                <div className="flex flex-wrap">
                  <div className="w-11/12">
                    <label className="block">
                      {data.output_array.array_name
                        ? data.output_array.array_name
                        : "Output"}{" "}
                      Attribute
                    </label>
                    {data.data_required.template_method_type === "import" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleExpandSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : data.data_required.template_method_type ===
                      "shopify" ? (
                      <select
                        name={`nodes[data][${index}][to][]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleExpandSelect}
                        value={Array.isArray(node.to) ? node.to[0] : node.to}
                      >
                        <option value="" className="Poppins regular text-color">
                          Select Field
                        </option>
                        {data.output_array.nodes.map(
                          (attr_families, groupIndex) => (
                            <optgroup
                              label={
                                attr_families.name
                                  ? attr_families.name
                                  : "Others"
                              }
                              key={groupIndex}
                            >
                              {attr_families.attributes &&
                                Object.entries(attr_families.attributes).map(
                                  ([attr_key, attr_value]) => {
                                    const family_output_value =
                                      attr_families.name
                                        ? `${attr_families.name},${attr_key}`
                                        : attr_key;

                                    if (data.output_array.length > 0) {
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    } else {
                                      const isSelected = false;
                                      return (
                                        <option
                                          value={family_output_value}
                                          key={attr_key}
                                        >
                                          {attr_value}
                                        </option>
                                      );
                                    }
                                  }
                                )}
                            </optgroup>
                          )
                        )}
                      </select>
                    ) : (
                      <input
                        id={`input-export-expandselect-${index}`}
                        type="text"
                        name={`nodes[data][${index}][to]`}
                        className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                        onChange={handleExpandSelect}
                        value={node?.to}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      }
      return null;
    };
    const renderFromSelect = () => {
      // Conditionally render additional select(s) based on the selected formula
      if (formula === "short_code") {
        return (
          <div className="sm:w-4/12 w-6/12 from merge-advance from">
            <div className="flex flex-wrap">
              <div className="w-10/12">
                <div className="form-group">
                  <label htmlFor="name_in_import">
                    {" "}
                    {data.input_array.array_name || "Input"} Content
                  </label>
                  <input
                    type="hidden"
                    className="left_array apimio-column short_code_hidden_field mapping-from-select"
                    name={`nodes[data][${index}][from][]`}
                    value={hiddenFieldValue}
                    readOnly
                    onChange={handleContentEditableInputChange}
                  />

                  <div
                    tabIndex="0"
                    contentEditable="true"
                    disabled="disabled"
                    className="form-control short_code_div bg-white-smoke max-h-[100px]"
                    //data-placeholder="Enter your content with multiple elements"
                    onClick={handleContentEditableChange}
                    dangerouslySetInnerHTML={{ __html: content }}
                  ></div>

                  {/* <div className="d-flex justify-content-between mt-1">
                    <div>
                      <button
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="Clear Content"
                        type="button"
                        className="bg-transparent border-0 d-block clear_content"
                        onClick={handleClearContent}
                      >
                        <i className="fa fa-refresh" aria-hidden="true"></i>
                      </button>
                    </div>
                    <div>
                      <div className="dropdown">
                        <button
                          className="btn dropdown-toggle insert_element_title"
                          type="button"
                          id="dropdownMenuButton"
                          data-bs-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          {"{ }"} Insert Element
                        </button>

                        <div
                          className="dropdown-menu insert_element_main bg-white-smoke"
                          aria-labelledby="dropdownMenuButton"
                        >
                          {data.data_required.template_method_type ===
                          "import" ? (
                            <div>
                              <h6 className="dropdown-header insert_element_head">
                                {defaultNodeName || "Others"}
                              </h6>
                              <div className="insert_element_content"></div>
                              {Object.keys(data.input_array.nodes.Default).map(
                                (inputKey, optionIndex) => {
                                  const fullValue =
                                    defaultNodeName +
                                    "," +
                                    data.input_array.nodes.Default[inputKey];
                                  const mergeAdvValue = "{{" + fullValue + "}}";
                                  return (
                                    <button
                                      key={optionIndex}
                                      className="dropdown-item insert_element"
                                      value={"{{" + fullValue + "}}"}
                                      onClick={() =>
                                        handleInsertElement({ mergeAdvValue })
                                      }
                                    >
                                      {data.input_array.nodes.Default[inputKey]}
                                    </button>
                                  );
                                }
                              )}
                            </div>
                          ) : (
                            <div>
                              <div className="insert_element_content"></div>
                              {data.input_array.nodes.map(
                                (familyNode, familyIndex) => (
                                  <>
                                    <h6
                                      className="dropdown-header insert_element_head"
                                      key={familyIndex}
                                    >
                                      {familyNode.attributes &&
                                      Object.keys(familyNode.attributes)
                                        .length > 0
                                        ? familyNode.name || "Others"
                                        : familyNode.name}
                                    </h6>
                                    {familyNode.attributes &&
                                      Object.entries(familyNode.attributes).map(
                                        (
                                          [attributeName, attributeValue],
                                          inputIndex
                                        ) => {
                                          let familyInputValue;
                                          familyInputValue = `${familyNode.name},${attributeName}`;
                                          const mergeAdvValue =
                                            "{{" + familyInputValue + "}}";
                                          return (
                                            <button
                                              value={
                                                "{{" + familyInputValue + "}}"
                                              }
                                              className="dropdown-item insert_element"
                                              key={inputIndex}
                                              onClick={() =>
                                                handleInsertElement({
                                                  mergeAdvValue,
                                                })
                                              }
                                            >
                                              {attributeValue}
                                            </button>
                                          );
                                        }
                                      )}
                                  </>
                                )
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
        );
      } else if (formula === "merge") {
        return (
          <div className="sm:w-3/12 w-6/12 from">
            <div className="flex flex-wrap">
              <div className="w-8/12">
                <label className="block">
                  {data.input_array.array_name || "Input"} Attribute
                </label>
                {data.data_required.template_method_type === "import" ? (
                  <select
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select1"
                    name={`nodes[data][${index}][from][]`}
                    value={Array.isArray(node.from) ? node.from[0] : node.from}
                    onChange={handleMergeFrom}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>

                    <optgroup label={defaultNodeName}>
                      {Object.keys(data.input_array.nodes.Default).map(
                        (inputKey, optionIndex) => {
                          const fullValue =
                            defaultNodeName +
                            "," +
                            data.input_array.nodes.Default[inputKey];

                          return (
                            <option value={fullValue} key={inputKey}>
                              {data.input_array.nodes.Default[inputKey]}
                            </option>
                          );
                        }
                      )}
                    </optgroup>
                  </select>
                ) : (
                  <select
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select mapping-from-select1"
                    name={`nodes[data][${index}][from][]`}
                    value={Array.isArray(node.from) ? node.from[0] : node.from}
                    onChange={handleSelectChange}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>
                    {data.input_array.nodes.map((familyNode, familyIndex) =>
                      // Check if familyNode.attributes exists and has entries
                      familyNode.attributes &&
                      Object.entries(familyNode.attributes).length > 0 ? (
                        <optgroup label={familyNode.name} key={familyIndex}>
                          {Object.entries(familyNode.attributes).map(
                            ([attributeName, attributeValue], inputIndex) => {
                              return (
                                <option
                                  value={`${familyNode.name},${attributeName}`}
                                  key={inputIndex}
                                >
                                  {attributeValue}
                                </option>
                              );
                            }
                          )}
                        </optgroup>
                      ) : (
                        <optgroup
                          label={familyNode.name}
                          key={familyIndex}
                        ></optgroup>
                      )
                    )}
                  </select>
                )}
              </div>
            </div>
          </div>
        );
      } else {
        return (
          <div className="sm:w-3/12 w-6/12 from">
            <div className="flex flex-wrap">
              <div className="w-8/12">
                <label className="block">
                  {data.input_array.array_name || "Input"} Attribute
                </label>
                {data.data_required.template_method_type === "import" ? (
                  <select
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
                    name={`nodes[data][${index}][from][]`}
                    value={Array.isArray(node.from) ? node.from[0] : node.from}
                    onChange={handleSelectChange}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>

                    <optgroup label={defaultNodeName}>
                      {Object.keys(data.input_array.nodes.Default).map(
                        (inputKey, optionIndex) => {
                          const fullValue =
                            defaultNodeName +
                            "," +
                            data.input_array.nodes.Default[inputKey];

                          return (
                            <option value={fullValue} key={inputKey}>
                              {data.input_array.nodes.Default[inputKey]}
                            </option>
                          );
                        }
                      )}
                    </optgroup>
                  </select>
                ) : (
                  <select
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
                    name={`nodes[data][${index}][from][]`}
                    value={Array.isArray(node.from) ? node.from[0] : node.from}
                    onChange={handleSelectChange}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>
                    {data.input_array.nodes.map((familyNode, familyIndex) =>
                      // Check if familyNode.attributes exists and has entries
                      familyNode.attributes &&
                      Object.entries(familyNode.attributes).length > 0 ? (
                        <optgroup label={familyNode.name} key={familyIndex}>
                          {Object.entries(familyNode.attributes).map(
                            ([attributeName, attributeValue], inputIndex) => {
                              return (
                                <option
                                  value={`${familyNode.name},${attributeName}`}
                                  key={inputIndex}
                                >
                                  {attributeValue}
                                </option>
                              );
                            }
                          )}
                        </optgroup>
                      ) : (
                        <optgroup
                          label={familyNode.name}
                          key={familyIndex}
                        ></optgroup>
                      )
                    )}
                  </select>
                )}
              </div>
            </div>
          </div>
        );
      }
      return null;
    };
    return (
      <div
        key={index}
        dataid={index}
        className={`mapping-item relative mt-2 mb-3 px-4 pt-2 pb-2 justify-content-between border-l-8 mapping-${index} ${getBorderClass()} rounded-md shadow-md ${
          isRemoving ? "delete-row" : ""
        }`}
      >
        <div className="flex flex-wrap main-row">
          {renderFromSelect()}
          <div className="sm:w-3/12 w-6/12 formula">
            <div className="flex flex-wrap align-items-center h-full">
              <div className="w-8/12">
                <label className="block">Formula</label>
                <select
                  name={`nodes[data][${index}][with_formula]`}
                  className="formula_field w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent"
                  id={`formula-${index}`}
                  value={formula}
                  onChange={handleFormulaChange}
                >
                  {formulaOptions.map((option, optionIndex) => (
                    <option
                      key={optionIndex}
                      value={option.value}
                      className="Poppins regular text-color"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="w-2/12 mx-auto mt-8">
                <i className="icon-arrow-right"></i>
              </div>
            </div>
          </div>
          {renderToSelect()}
        </div>
        <div
          className="text-red-500 absolute right-2 top-2 close_row"
          onClick={handleRemove}
        >
          <i className="fa-solid fa-times cursor-pointer"></i>
        </div>
        {data.data_required.template_method_type === "import" && (
          <div className="absolute right-2 bottom-2">
            {renderCreateAttributeModal() && (
              <button
                key={index}
                id={`creat-attribute-${index}`}
                type="button"
                className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                onClick={() => handleAttributeClick(index)}
              >
                Create Apimio Attribute
              </button>
            )}
          </div>
        )}
        {renderWarningMessage() && (
          <div className={`flex my-2 warning-msg ${formula}`}>
            <svg
              className="mr-1"
              width="14"
              height="15"
              viewBox="0 0 24 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M12 8.5V13.5"
                stroke="#FFC107"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M11.9922 16.5H12.0012"
                stroke="#FFC107"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            <span className="font-bold text-xs">{renderWarningMessage()}</span>
          </div>
        )}
        {contentEditableModalShow ? (
          <ContentEditableField
            data={data}
            initialValue={initialValue}
            index={index}
            oncloseField={handleContentEditableValue}
            oncloseFieldClose={handleContentEditableClose}
          />
        ) : (
          ""
        )}
      </div>
    );
  }
);

export default MappingForm;
