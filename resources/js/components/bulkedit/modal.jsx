import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import axios from "axios";

export default function Modal({ allAttributes, filterAttributes, open, onUpdateColumns, onCloseModal }) {
    const [columns, setColumns] = useState([]);
    const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);
    const [columnSearchValue, setColumnSearchValue] = useState("");
    const [filteredColumns, setFilteredColumns] = useState([]);

    useEffect(() => {
        const flattenAttributes = (attributes) => {
            const result = [];
            for (const family of attributes) {
                const familyName = family.name;
                const parent = { label: familyName, isParent: true };
                result.push(parent);
                for (const attribute of family.attributes) {
                    const attributeLabel = attribute.name;
                    let isChecked;
                    let child;
                    if (filterAttributes[familyName] && filterAttributes[familyName][attribute.handle]) {
                        isChecked = "checked";
                    } else {
                        isChecked = "";
                    }
                    if (attribute.handle == "sku" || attribute.handle == "product_name") {
                        child = {
                            key: attribute.handle,
                            label: attributeLabel,
                            checked: "checked",
                            isParent: false,
                            hierName: familyName + "," + attribute.handle,
                        };
                    } else {
                        child = {
                            key: attribute.handle,
                            label: attributeLabel,
                            checked: isChecked,
                            isParent: false,
                            hierName: familyName + "," + attribute.handle,
                        };
                    }
                    result.push(child);
                }
            }
            return result;
        };

        if (open) {
            const initialColumns = flattenAttributes(allAttributes);
            setColumns(initialColumns);
            setFilteredColumns(initialColumns);

            setIsInitialDataLoaded(true);
        } else {
            setIsInitialDataLoaded(false);
        }
    }, [allAttributes, filterAttributes, open]);
    const handleSearch = (columnSearchValue) => {
        const searchValue = columnSearchValue.toLowerCase();
        setColumnSearchValue(columnSearchValue);

        // Filter the original columns based on the search value
        const filtered = allAttributes.reduce((result, family) => {
            const filteredAttributes = family.attributes.filter((attribute) => attribute.name.toLowerCase().includes(searchValue));

            if (filteredAttributes.length > 0) {
                result.push({
                    label: family.name,
                    isParent: true,
                });

                filteredAttributes.forEach((attribute) => {
                    const familyName = family.name;
                    const attributeLabel = attribute.name;
                    const isChecked = filterAttributes[familyName] && filterAttributes[familyName][attribute.handle] ? "checked" : "";

                    result.push({
                        key: attribute.handle,
                        label: attributeLabel,
                        checked: isChecked,
                        isParent: false,
                        hierName: familyName + "," + attribute.handle,
                    });
                });
            }
            return result;
        });

        // Update the filtered columns state
        setFilteredColumns(filtered);
    };
    const toggleColumn = (hierName) => {
        const updatedColumns = columns.map((column) => (column.hierName === hierName ? { ...column, checked: !column.checked } : column));
        setColumns(updatedColumns);
        setFilteredColumns(updatedColumns);
    };

    const closeModal = () => {
        setIsInitialDataLoaded(false);

        onCloseModal();
    };

    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            closeModal();
        }
    };

    const handleSaveClick = () => {
        $(".loader").removeClass("hidden");
        var filter_attributes = {};

        // Loop through all the checkboxes
        $('input[type="checkbox"]').each(function () {
            if ($(this).is(":checked")) {
                var values = $(this).val().split(",");
                var familyName = values[0] ? values[0].trim() : "";
                var attributeHandle = values[1] ? values[1].trim() : "";
                var attributeLabel = $(this).next("label").text();
                if (!filter_attributes[familyName]) {
                    filter_attributes[familyName] = {};
                }

                filter_attributes[familyName][attributeHandle] = attributeLabel;
            }
        });

        const structuredObject = {
            payload: filter_attributes,
        };
        onUpdateColumns(filter_attributes);
        axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
        axios
            .post("/products/bulk/edit/filter/save", structuredObject)
            .then((response) => {
                toast.success("Columns Updated Succesfully");
                closeModal();
                setTimeout(function () {
                    $(".loader").addClass("hidden");
                }, 2000);
            })
            .catch((error) => console.log(error));
        closeModal();
    };

    return (
        <div>
            {open && (
                <div className="absolute right-28 left-auto top-3 w-80 z-[1000]" onClick={handleBackdropClick}>
                    <div className="modal-dialog modal-dialog-centered ">
                        <div className="modal-content relative flex flex-column outline-0 rounded-sm w-full border-[#00000033] border pointer-auto">
                            <div className="p-3 border-b border-[#dee2e6] flex items-center justify-between">
                                <h2>Manage Columns</h2>
                            </div>
                            <div className="modal-body h-96 overflow-scroll">
                                <div className="row ">
                                    <div>
                                        <input
                                            placeholder="Search Columns"
                                            value={columnSearchValue}
                                            onChange={(e) => {
                                                const searchValue = e.target.value;
                                                handleSearch(searchValue);
                                            }}
                                            className="h-10 rounded-md  w-full  bg-[#f4f4f4] mb-2 border border-[#ccc] px-4 text-[#222] transition-all duration-200 ease-in-out focus:bg-white focus:drop-shadow-lg"
                                        />
                                    </div>

                                    {filteredColumns.map((column, index) => (
                                        <div key={index} className="column-div">
                                            <div className={`py-2 flex flex-row ${column.isParent ? "font-bold" : ""}`} key={index}>
                                                {!column.isParent && ( // Display checkboxes only for child columns
                                                    <input
                                                        type="checkbox"
                                                        checked={column.checked}
                                                        onChange={() => toggleColumn(column.hierName)}
                                                        className="mr-3 h-5 flex w-5 modal-checkbox"
                                                        name={`headerchecked-${index}`}
                                                        id={`headerchecked-${index}`}
                                                        value={column.hierName}
                                                        disabled={column.hierName === "Default,sku" || column.hierName === "Default,handle"}
                                                    />
                                                )}
                                                <label
                                                    htmlFor={`headerchecked-${index}`}
                                                    className="flex items-center"
                                                    id={column.hierName}
                                                >
                                                    {column.label}
                                                </label>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="p-3 border-t border-[#dee2e6] flex items-center justify-center">
                                <div className="footer-buttons space-x-12 mr-4 py-3">
                                    <button
                                        className=" bg-[#fff] text-primary border-solid border-1 border-[#2C4BFF] py-2 px-4 h-10 w-30 rounded-md text-md font-semibold"
                                        onClick={closeModal}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        className=" bg-[#2C4BFF] border-solid border-1 border-[#2C4BFF] h-10 w-35 py-2 px-4 text-md font-semibold rounded-md text-white"
                                        onClick={handleSaveClick}
                                    >
                                        Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
