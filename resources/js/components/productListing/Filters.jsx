import React, { useState, useEffect } from "react";
import { FilterOutlined, PlusOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import axios from "axios";
import { Button, Drawer, Form, message, Space } from "antd";
import FilterMapping from "./FilterMappng";

const Filter = ({ onFilteredData }) => {
    const [open, setOpen] = useState(false);
    const [filterMapping, setFilterMapping] = useState(false);
    const [finalizedFilters, setFinalizedFilters] = useState([]);
    const [savedFilters, setSavedFilters] = useState(null);
    const storedFilters = localStorage.getItem("filters");
    const organizationId = document.getElementById("listingTable")?.getAttribute("data-orgId");

    useEffect(() => {
        if (storedFilters) {
            setSavedFilters(JSON.parse(storedFilters)); // Set saved filters to state
            setFilterMapping(true);
        }
    }, []);

    const showDrawer = () => {
        setOpen(true);
    };

    const onClose = () => {
        setOpen(false);
        setFilterMapping(!!storedFilters);
    };

    const convertFinalizedFilters = (finalizedFilters) => {
        return finalizedFilters.map((filterGroup) => {
            return filterGroup
                .map((filterRow) => {
                    const attribute = filterRow.selectedAttribute?.value ?? null;
                    const formula = filterRow.selectedFormula ?? null;
                    let relevantValue = "";

                    switch (filterRow.selectedAttribute?.type) {
                        case "multi_select":
                            relevantValue = filterRow.selectedTreeValue ?? [];
                            break;
                        case "short_text":
                        case "number":
                        case "long_text":
                            relevantValue = filterRow.inputValue ?? "";
                            break;
                        case "dropdown":
                            relevantValue = filterRow.selectedDropdownValue ?? "";
                            break;
                        default:
                            relevantValue = "";
                            break;
                    }

                    if (!attribute || !formula) return null;

                    return {
                        attribute: attribute,
                        formula: formula,
                        value: relevantValue,
                        ...(filterRow.json_data && { json_data: filterRow.json_data }),
                        ...(filterRow.family && { family: filterRow.family }),
                    };
                })
                .filter((row) => row !== null);
        });
    };

    const handleSubmit = () => {
        let relevantSelectionData = convertFinalizedFilters(finalizedFilters);

        // Check if any filters are invalid
        const isInvalidFilter = relevantSelectionData.some((filterGroup) => {
            return filterGroup.some((filter) => {
                // Check if value array is empty and the formula is not "is_defined" or "is_not_defined"
                return filter.value.length === 0 && filter.formula !== "is_defined" && filter.formula !== "is_not_defined";
            });
        });

        // If any filter is invalid, notify the user and return
        if (isInvalidFilter) {
            message.warning("Please select attribute, formula, and value for all filters.");
            return;
        }

       if (storedFilters && storedFilters.length > 0) {
    relevantSelectionData = JSON.parse(storedFilters); // Assign stored filters to relevant selection
}

if (!relevantSelectionData || relevantSelectionData.length === 0) {
    message.warning("No filters selected. Please choose at least one filter.");
    return;
}

        setOpen(false);

        axios
            .post(
                `api/${import.meta.env.VITE_API_VERSION}/products/index`,
                {
                    filters: relevantSelectionData, // Send finalizedFilters in the body
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            )
            .then((response) => {

                const { data } = response.data;
                const pagination = {
                    current: response.data.pagination.current_page,
                    pageSize: 30,
                    total: response.data.pagination.total,
                };

                localStorage.setItem("filters", JSON.stringify(relevantSelectionData));
                localStorage.setItem("orgId", organizationId);
                onFilteredData(data, pagination, relevantSelectionData);

                $("#filter_total_product_count").val(pagination.total);
                $("#filter_total_product_array").val(localStorage.getItem("filters"));
                message.success("Filter Applied!");
            })
            .catch((error) => {
                message.error("Server Busy, Try again Later");
            })
            .finally(() => {
                setTimeout(() => {
                    $(".bulk_product_check").prop("checked", false).removeClass("checked");
                    $("#multiselect_products").prop("checked", false);
                    $(".bulk-footer").addClass("d-none");
                }, 0);
            });
    };

    const handleFiltersFinalized = (filters) => {
        setFinalizedFilters(filters);
    };

    const openComponent = () => {
        setFilterMapping(true);
    };

    const goBack = () => {
        setFilterMapping(false);
    };

    return (
        <>
            <Button
                type="primary"
                style={{
                    backgroundColor: "#740898",
                    borderColor: "#740898",
                    color: "#fff",
                }}
                onClick={showDrawer}
                icon={<FilterOutlined />}
            >
                Filters
            </Button>
            <Drawer
                title="FILTERS"
                onClose={onClose}
                width={720}
                open={open}
                styles={{
                    body: {
                        paddingBottom: 80,
                        backgroundColor: "#F5F5F5 ",
                    },
                }}
                extra={
                    <Space>
                        {filterMapping && (
                            <Button
                                onClick={handleSubmit}
                                type="primary"
                                style={{
                                    backgroundColor: "#740898",
                                    borderColor: "#740898",
                                    color: "#fff",
                                }}
                            >
                                APPLY FILTER
                            </Button>
                        )}
                    </Space>
                }
            >
                <Form layout="vertical">
                    <div>
                        {!filterMapping && (
                            <div
                                className="d-flex justify-content-between align-items-center bg-white p-2 rounded-3"
                                style={{
                                    cursor: "pointer",
                                }}
                                onClick={openComponent}
                            >
                                <p className="mb-0">ATTRIBUTES</p>
                                <PlusOutlined />
                            </div>
                        )}

                        {filterMapping && (
                            <div>
                                <div className="mt-0 mb-2">
                                    <ArrowLeftOutlined className="border p-2 rounded ms-1 bg-white" onClick={goBack} />
                                </div>
                                <FilterMapping
                                    onFiltersFinalized={handleFiltersFinalized}
                                    setFilterMapping={setFilterMapping}
                                    savedFilters={savedFilters}
                                />
                            </div>
                        )}
                    </div>
                </Form>
            </Drawer>
        </>
    );
};

export default Filter;
