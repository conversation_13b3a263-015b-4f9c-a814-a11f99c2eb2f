import React, { useState, useEffect } from "react";
import AddVLookupModal from "./AddVLookupModal";

function MappingTo({
  index,
  selectedFormula,
  onSelectedValueChange,
  outputArray,
  newlyReceivedValue,
  inputArray,
  outputArrayToSelect,
  data,
}) {
  const [output_array, setOutputArray] = useState(outputArray);
  const input_array = inputArray;
  const [inputField, setInputField] = useState(null);
  const [assign, setAssignValue] = useState();
  const [slug, setSlugValue] = useState();
  const [short_code, setShortcodeValue] = useState();
  const [splitselect, setSplitSelect] = useState();
  const [splitInput, setSplitInput] = useState("");
  const [splitInputs, setSplitInputs] = useState({});
  const [mergeInput, setMergeInput] = useState("");
  const [splitSelect2, setSplitSelect2] = useState();
  const [mergeSelect, setMergeSelect] = useState();
  const [mergeSelect2, setMergeSelect2] = useState();
  const [replaceInputSearch, setReplaceInputSearch] = useState("");
  const [replaceInputWith, setReplaceInputWith] = useState("");
  const [replaceSelect, setReplaceSelect] = useState();
  const [calculateInput, setCalculateInput] = useState("");
  const [calculateSelect, setCalculateSelect] = useState();
  const [vLookupInput, setVLookupInput] = useState();
  const [vLookupSelect, setVLookupSelect] = useState();
  const [expandPositionSelect, setExpandPositionSelect] = useState();
  const [expandInput, setExpandInput] = useState("");
  const [expandSelect, setExpandSelect] = useState();
  const defaultNodeName = Object.keys(input_array.nodes)[0]; // Fetch the node name dynamically
  const [selectedField, setSelectedField] = useState(null);
  const [withValue, setWithValue] = useState([]);
  const [getInputValue, setGetInputValue] = useState([]);
  const [addVLookupModal, setAddVLookupModal] = useState(0);
  const [vLookupData, setVLookupData] = useState([]);
  const [userInteractedWithInput, setUserInteractedWithInput] = useState(false);
  const handleAddVlookup = () => {
    setAddVLookupModal(1);
  };
  const handleCloseModal = () => {
    setAddVLookupModal(0);
    outputArrayToSelect = outputArray;
  };

  if (data.data_required.template_method_type === "import") {
  } else {
    function displayItemsWithIndex(inputArray) {
      let result = [];
      let currentIndex = 0;

      inputArray.nodes.forEach((familyNode, familyIndex) => {
        for (const attributeName in familyNode.attributes) {
          const attributeValue = familyNode.attributes[attributeName];
          result.push(`${attributeName}`);
          currentIndex++;
        }
      });

      return result;
    }
    const newInputArray = displayItemsWithIndex(input_array);
    // for (let index = 0; index < newInputArray.length; index++) {
    //   var toValue = $(`input[name="nodes[data][${index}][to]"`);
    //   toValue.val(newInputArray[index]);
    // }
  }

  const handleSelectAssign = (e) => {
    const selectedValue = e.target.value;
    setAssignValue(selectedValue);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { assign: selectedValue });
    }
  };
  const handleSelectSlug = (e) => {
    const selectedValue = e.target.value;
    setSlugValue(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { slug: selectedValue });
    }
  };
  const handleSelecShortCode = (e) => {
    const selectedValue = e.target.value;
    setShortcodeValue(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { short_code: selectedValue });
    }
  };

  const handleSelectsplit = (e) => {
    const selectedValue = e.target.value;
    setSplitSelect(selectedValue);

    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { split: selectedValue });
    }
  };
  const handleSelectSplit2 = (e) => {
    const selectedValue = e.target.value;
    setSplitSelect2(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { split2: selectedValue });
    }
  };
  const handleInputsplit = (e, index) => {
    const selectedValue = e.target.value;
    // const newObject = { with: newValue };

    // const updatedWithValue = [...withValue]; // Create a copy of the array
    // updatedWithValue[index] = newObject; // Replace the object at the specified index

    // // Set the state with the updated array
    // setWithValue(updatedWithValue);
    setUserInteractedWithInput(true);

    setSplitInput(selectedValue);
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { splitInput: selectedValue });
    }
  };
  const handleMergeInput = (e) => {
    const selectedValue = e.target.value;
    setMergeInput(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { mergeinput: selectedValue });
    }
  };

  const handleMergeSelect = (e) => {
    const selectedValue = e.target.value;
    setUserInteractedWithInput(true);
    setMergeSelect(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { merge: selectedValue });
    }
  };
  const handleMergeSelect2 = (e) => {
    const selectedValue = e.target.value;
    setUserInteractedWithInput(true);
    setMergeSelect2(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { merge2: selectedValue });
    }
  };
  const handleReplaceInputSearch = (e) => {
    const selectedValue = e.target.value;
    setReplaceInputSearch(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { replaceinput: selectedValue });
    }
  };
  const handleReplaceInputWith = (e) => {
    const selectedValue = e.target.value;
    setReplaceInputWith(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { replaceinputwith: selectedValue });
    }
  };
  const handleReplaceSelect = (e) => {
    const selectedValue = e.target.value;
    setReplaceSelect(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { replace: selectedValue });
    }
  };
  const handleCalculateInput = (e) => {
    const selectedValue = e.target.value;
    setCalculateInput(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { calculateinput: selectedValue });
    }
  };
  const handleCalculateSelect = (e) => {
    const selectedValue = e.target.value;
    setCalculateSelect(selectedValue);
    setUserInteractedWithInput(true);
    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { calculate: selectedValue });
    }
  };
  const handleVLookupInput = (e) => {
    const selectedValue = e.target.value;
    setVLookupInput(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { vlookupinput: selectedValue });
    }
  };
  const handleVLookupSelect = (e) => {
    const selectedValue = e.target.value;
    setVLookupSelect(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { vlookup: selectedValue });
    }
  };
  const handleExpandPositionSelect = (e) => {
    const selectedValue = e.target.value;
    setExpandPositionSelect(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { expandposition: selectedValue });
    }
  };
  const handleExpandInput = (e) => {
    const selectedValue = e.target.value;
    setExpandInput(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { expandinput: selectedValue });
    }
  };
  const handleExpandSelect = (e) => {
    const selectedValue = e.target.value;
    setExpandSelect(selectedValue);

    // Call the callback function with the selected value
    if (onSelectedValueChange) {
      onSelectedValueChange(index, { expand: selectedValue });
    }
  };
  useEffect(() => {
    const replaceValue = {};

    outputArrayToSelect.forEach((item, index) => {
      if (item.to && item.to.length > 0 && item.with) {
        withValue[index] = { with: item.with };
      }
    });
    outputArrayToSelect.forEach((item, index) => {
      if (item.to && item.to.length > 0 && item.replace) {
        replaceValue[index] = { replace: item.replace };
      }
    });

    const newSplitInputs = {};

    Object.keys(withValue).forEach((index) => {
      if (withValue[index].with) {
        newSplitInputs[index] = withValue[index].with;
      }
    });
    setWithValue(newSplitInputs);
    // Update the splitInputs state with the new values
    //setSplitInput(newSplitInputs);
  }, []);

  useEffect(() => {
    const fetchVLookupData = async () => {
      if (selectedFormula === "vlookup") {
        try {
          const response = await axios.post("/vlookup/fetch");
          const data = response.data.data;

          setVLookupData(data);
          setGetInputValue("");
        } catch (error) {
          console.error("Error fetching VLookup data:", error);
        }
      }
    };
    fetchVLookupData();
  }, [selectedFormula]);

  useEffect(() => {
    // Update the input field based on the selected formula
    if (selectedFormula === "assign") {
      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectAssign}
                      value={assign}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;
                                if (outputArrayToSelect) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectAssign}
                      value={assign}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "split") {
      let newSplitInput = "";

      // Check if the user has not interacted with the input
      if (!userInteractedWithInput) {
        if (outputArrayToSelect.length > 0) {
          const selectedOutput = outputArrayToSelect[index]?.with;
          newSplitInput = selectedOutput || "";
          setGetInputValue(selectedOutput || "");
        } else {
          newSplitInput = "";
          setGetInputValue("");
        }

        // Only update splitInput if it hasn't been modified by user input
        if (!userInteractedWithInput) {
          setSplitInput(newSplitInput);
        }
      }
      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">Separator</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={handleInputsplit}
                    // value={withValue[index]?.with || ""}
                    value={splitInput}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute (1)
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectsplit}
                      value={splitselect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;
                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectsplit}
                      value={splitselect}
                    />
                  )}
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute (2)
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectSplit2}
                      value={splitSelect2}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[1] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectSplit2}
                      value={splitSelect2}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "merge") {
      let newMergeInput = "";

      // Check if the user has not interacted with the input
      if (!userInteractedWithInput) {
        if (outputArrayToSelect.length > 0) {
          const selectedOutput = outputArrayToSelect[index]?.with;
          newMergeInput = selectedOutput || "";
          setGetInputValue(selectedOutput || "");
        } else {
          newMergeInput = "";
          setGetInputValue("");
        }

        // Only update splitInput if it hasn't been modified by user input
        if (!userInteractedWithInput) {
          setMergeInput(newMergeInput);
        }
      }

      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">Glue</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    value={mergeInput}
                    onChange={handleMergeInput}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {input_array.array_name || "Input"} Attribute (2)
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select merge-select"
                      name={`nodes[data][${index}][from][]`}
                      value={mergeSelect}
                      onChange={handleMergeSelect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      <optgroup label={defaultNodeName || "Others"}>
                        {Object.keys(input_array.nodes.Default).map(
                          (inputKey, optionIndex) =>
                            Array.isArray(
                              input_array.nodes.Default[inputKey]
                            ) ? (
                              input_array.nodes.Default[inputKey].map(
                                (inputVal, inputIndex) => {
                                  const familyInputValue =
                                    defaultNodeName +
                                    "," +
                                    input_array.nodes.Default[inputKey];
                                  if (outputArrayToSelect.length > 0) {
                                    const isSelected =
                                      data.template_attributes.template.data[
                                        optionIndex
                                      ]?.from[1] === familyInputValue;

                                    return (
                                      <option
                                        value={family_output_value}
                                        key={attr_key}
                                        selected={isSelected ? "selected" : ""}
                                      >
                                        {attr_value}
                                      </option>
                                    );
                                  } else {
                                    const isSelected = false;
                                    return (
                                      <option
                                        value={family_output_value}
                                        key={attr_key}
                                        selected={isSelected ? "selected" : ""}
                                      >
                                        {attr_value}
                                      </option>
                                    );
                                  }
                                }
                              )
                            ) : (
                              <option
                                value={
                                  defaultNodeName +
                                  "," +
                                  input_array.nodes.Default[inputKey]
                                }
                                key={inputKey}
                                selected={
                                  outputArrayToSelect.length > 0 &&
                                  data.template_attributes.template.data[
                                    optionIndex
                                  ]?.from[1] ===
                                    defaultNodeName +
                                      "," +
                                      input_array.nodes.Default[inputKey]
                                    ? "selected"
                                    : ""
                                }
                              >
                                {input_array.nodes.Default[inputKey]}
                              </option>
                            )
                        )}
                      </optgroup>
                    </select>
                  ) : (
                    <select
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
                      name={`nodes[data][${index}][from][]`}
                      onChange={handleMergeSelect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {input_array.nodes.map((familyNode, familyIndex) => (
                        <optgroup label={familyNode.name} key={familyIndex}>
                          {Object.entries(familyNode.attributes).map(
                            ([attributeName, attributeValue], inputIndex) => {
                              return (
                                <option
                                  value={`${familyNode.name},${attributeValue}`}
                                  key={inputIndex}
                                >
                                  {attributeValue}
                                </option>
                              );
                            }
                          )}
                        </optgroup>
                      ))}
                    </select>
                  )}
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      value={mergeSelect2}
                      onChange={handleMergeSelect2}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      value={mergeSelect2}
                      onChange={handleMergeSelect2}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "short_code") {
      setInputField(
        <div className="sm:w-4/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <label className="block">
                {output_array.array_name ? output_array.array_name : "Output"}{" "}
                Attribute
              </label>
              {data.data_required.template_method_type === "import" ? (
                <select
                  name={`nodes[data][${index}][to][]`}
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                  onChange={handleSelecShortCode}
                  value={short_code}
                >
                  <option value="" className="Poppins regular text-color">
                    Select Field
                  </option>
                  {output_array.nodes.map((attr_families, groupIndex) => (
                    <optgroup
                      label={attr_families.name ? attr_families.name : "Others"}
                      key={groupIndex}
                    >
                      {attr_families.attributes &&
                        Object.entries(attr_families.attributes).map(
                          ([attr_key, attr_value]) => {
                            const family_output_value = attr_families.name
                              ? `${attr_families.name},${attr_key}`
                              : attr_key;

                            return (
                              <option
                                value={family_output_value}
                                key={attr_key}
                              >
                                {attr_value}
                              </option>
                            );
                          }
                        )}
                    </optgroup>
                  ))}
                </select>
              ) : (
                <input
                  type="text"
                  name={`nodes[data][${index}][to]`}
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                  onChange={handleSelecShortCode}
                  value={short_code}
                />
              )}
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "replace") {
      let newReplaceInputSearch = "";
      let newReplaceInputWith = "";

      // Check if the user has not interacted with the input
      if (!userInteractedWithInput) {
        if (outputArrayToSelect.length > 0) {
          const selectedOutputWith = outputArrayToSelect[index]?.with;
          const selectedOutputreplace = outputArrayToSelect[index]?.replace;
          newReplaceInputSearch = selectedOutputWith || "";
          newReplaceInputWith = selectedOutputreplace || "";
          setGetInputValue(selectedOutputWith || "");
        } else {
          newReplaceInputSearch = "";
          newReplaceInputWith = "";
          setGetInputValue("");
        }

        // Only update splitInput if it hasn't been modified by user input
        if (!userInteractedWithInput) {
          setReplaceInputSearch(newReplaceInputSearch);
          setReplaceInputWith(newReplaceInputWith);
        }
      }
      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">Search</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][replace]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={handleReplaceInputSearch}
                    value={replaceInputSearch}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">With</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-replace-input"
                    onChange={handleReplaceInputWith}
                    value={replaceInputWith}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleReplaceSelect}
                      value={replaceSelect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleReplaceSelect}
                      value={replaceSelect}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "slug") {
      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectSlug}
                      value={slug}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleSelectSlug}
                      value={slug}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "vlookup") {
      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    V-Lookup
                    <span
                      className="ml-2 text-blue-300"
                      onClick={handleAddVlookup}
                    >
                      ( Add )
                    </span>
                  </label>

                  <select
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={handleVLookupInput}
                    value={vLookupInput}
                  >
                    <option value="">Choose</option>
                    {vLookupData.map((item) => (
                      <option key={item.id} value={item.name} id={item.id}>
                        {item.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleVLookupSelect}
                      value={vLookupSelect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleVLookupSelect}
                      value={vLookupSelect}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "calculate") {
      let newCalculateInput = "";

      // Check if the user has not interacted with the input
      if (!userInteractedWithInput) {
        if (outputArrayToSelect.length > 0) {
          const selectedOutput = outputArrayToSelect[index]?.with;
          newCalculateInput = selectedOutput || "";
          setGetInputValue(selectedOutput || "");
        } else {
          newCalculateInput = "";
          setGetInputValue("");
        }

        // Only update splitInput if it hasn't been modified by user input
        if (!userInteractedWithInput) {
          setCalculateInput(newCalculateInput);
        }
      }
      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    With
                    <span className="relative tooltip-custom text-blue-600 ml-2">
                      <div className="text-left tooltip-content absolute bottom-0 -right-52 bg-white p-2 shadow-xl border border-gray-300">
                        <b>Example values:</b>
                        <li>+50 Addition</li>
                        <li>-50 Subtraction</li>
                        <li>50% percentage</li>
                        <li>+50% add percentage</li>
                        <li>-50% sub percentage</li>
                      </div>
                      ( e.g )
                    </span>
                  </label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={handleCalculateInput}
                    value={calculateInput}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleCalculateSelect}
                      value={calculateSelect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleCalculateSelect}
                      value={calculateSelect}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedFormula === "expand") {
      let newExpandInput = "";

      // Check if the user has not interacted with the input
      if (!userInteractedWithInput) {
        if (outputArrayToSelect.length > 0) {
          const selectedOutput = outputArrayToSelect[index]?.with;
          newExpandInput = selectedOutput || "";
          setGetInputValue(selectedOutput || "");
        } else {
          newExpandInput = "";
          setGetInputValue("");
        }

        // Only update splitInput if it hasn't been modified by user input
        if (!userInteractedWithInput) {
          setExpandInput(newExpandInput);
        }
      }

      setInputField(
        <div className="sm:w-6/12 w-full to align-self-sm-center">
          <div className="flex flex-wrap">
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">Position</label>
                  <select
                    name={`nodes[data][${index}][replace]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-replace-input"
                    onChange={handleExpandPositionSelect}
                    value={expandPositionSelect}
                  >
                    <option value="" className="Poppins regular text-color">
                      Select Field
                    </option>
                    {outputArrayToSelect.length > 0 ? (
                      <>
                        <option
                          value="start"
                          selected={
                            outputArrayToSelect[index]?.replace === "start"
                              ? "selected"
                              : ""
                          }
                        >
                          Start
                        </option>
                        <option
                          value="end"
                          selected={
                            outputArrayToSelect[index]?.replace === "end"
                              ? "selected"
                              : ""
                          }
                        >
                          End
                        </option>
                      </>
                    ) : (
                      <>
                        <option value="start">Start</option>
                        <option value="end">End</option>
                      </>
                    )}
                  </select>
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">With</label>
                  <input
                    type="text"
                    name={`nodes[data][${index}][with][]`}
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-with-input"
                    onChange={handleExpandInput}
                    value={expandInput}
                  />
                </div>
              </div>
            </div>
            <div className="sm:w-4/12 w-6/12">
              <div className="flex flex-wrap">
                <div className="w-8/12">
                  <label className="block">
                    {output_array.array_name
                      ? output_array.array_name
                      : "Output"}{" "}
                    Attribute
                  </label>
                  {data.data_required.template_method_type === "import" ? (
                    <select
                      name={`nodes[data][${index}][to][]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={expandSelect}
                    >
                      <option value="" className="Poppins regular text-color">
                        Select Field
                      </option>
                      {output_array.nodes.map((attr_families, groupIndex) => (
                        <optgroup
                          label={
                            attr_families.name ? attr_families.name : "Others"
                          }
                          key={groupIndex}
                        >
                          {attr_families.attributes &&
                            Object.entries(attr_families.attributes).map(
                              ([attr_key, attr_value]) => {
                                const family_output_value = attr_families.name
                                  ? `${attr_families.name},${attr_key}`
                                  : attr_key;

                                if (outputArrayToSelect.length > 0) {
                                  const isSelected =
                                    outputArrayToSelect[index]?.to[0] ===
                                    family_output_value;

                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                } else {
                                  const isSelected = false;
                                  return (
                                    <option
                                      value={family_output_value}
                                      key={attr_key}
                                      selected={isSelected ? "selected" : ""}
                                    >
                                      {attr_value}
                                    </option>
                                  );
                                }
                              }
                            )}
                        </optgroup>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      name={`nodes[data][${index}][to]`}
                      className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-to-select"
                      onChange={handleExpandSelect}
                      value={expandSelect}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      setInputField(<div className="flex flex-wrap"></div>);
    }
  }, [
    selectedFormula,
    outputArrayToSelect,
    getInputValue,
    userInteractedWithInput,
  ]);

  return (
    <>
      {inputField}
      {addVLookupModal === 0 ? (
        ""
      ) : (
        <AddVLookupModal handleCloseModal={handleCloseModal} />
      )}
    </>
  );
}

export default MappingTo;
