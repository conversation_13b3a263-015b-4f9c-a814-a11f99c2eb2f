import React, { useState, useEffect } from "react";

function MppingFromMergeAdv({ index, onSelectedValueChange }) {
  //console.log(data);
  const { input_array } = data;
  const [selectedValue, setSelectedValue] = useState("");
  const defaultNodeName = Object.keys(data.input_array.nodes)[0]; // Fetch the node name dynamically

  useEffect(() => {
    // Set the selected value based on the index
    if (input_array.nodes.Default[index]) {
      const defaultNodeName = Object.keys(data.input_array.nodes)[0];
      const inputKey = input_array.nodes.Default[index];
      const familyInputValue = defaultNodeName + "," + inputKey;
      setSelectedValue(familyInputValue);
    } else {
      setSelectedValue(""); // No item at this index
    }
  }, [index, input_array.nodes.Default]);
  return (
    <div className="sm:w-4/12 w-6/12 from merge-advance from">
      <div className="flex flex-wrap">
        <div className="w-10/12">
          <div className="form-group">
            <label htmlFor="name_in_import">
              {" "}
              {input_array.array_name || "Input"} Content
            </label>
            <input
              type="text"
              className="left_array apimio-column short_code_hidden_field mapping-from-select opacity-0"
              name={`nodes[data][${index}][from][]`}
              value=""
            />
            <div
              contentEditable="true"
              className="form-control short_code_div bg-white-smoke"
              data-placeholder="Enter your content with multiple elements"
            ></div>

            <div className="d-flex justify-content-between mt-1">
              <div>
                <button
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  title="Clear Content"
                  type="button"
                  className="bg-transparent border-0 d-block clear_content"
                >
                  <i className="fa fa-refresh" aria-hidden="true"></i>
                </button>
              </div>
              <div>
                <div className="dropdown">
                  <button
                    className="btn dropdown-toggle insert_element_title"
                    type="button"
                    id="dropdownMenuButton"
                    data-bs-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    Insert Element
                  </button>
                  <div
                    className="dropdown-menu insert_element_main bg-white-smoke"
                    aria-labelledby="dropdownMenuButton"
                  >
                    {Object.entries(input_array.nodes).map(
                      ([input_key, input_vals]) => (
                        <div key={input_key}>
                          <h6 className="dropdown-header insert_element_head">
                            {input_key || "Others"}
                          </h6>
                          <div className="insert_element_content">
                            {input_vals.map((input_val, index) => (
                              <button
                                key={index}
                                className="dropdown-item insert_element"
                                value={`{{${
                                  defaultNodeName + "," + input_val
                                }}}`}
                              >
                                {input_val}
                              </button>
                            ))}
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MppingFromMergeAdv;
