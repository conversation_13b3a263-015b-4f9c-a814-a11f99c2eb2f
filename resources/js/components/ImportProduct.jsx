import React, { useState, useEffect } from "react";
import MappingTo from "./producttabsfields/mappingTo";
import MappingFormula from "./producttabsfields/mappingFormula";
import MappingFrom from "./producttabsfields/mappingFrom";
import MappingFromMergeAdv from "./producttabsfields/mappingFromMergeAdv";
import CreateAttributeModal from "./producttabsfields/CreateAttributeModal";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import SubmitModal from "./producttabsfields/SubmitModal";
import { set } from "lodash";
function ImportProducts({ data, resetClicked, onResetComplete }) {
  console.log(data);
  const [resetClickedAlert, setResetClickedAlert] = useState(false);
  const handleReset = () => {
    $(".loader").removeClass("hidden");
    toast.warning("Resetting...");
    setResetClickedAlert(true);
    setTimeout(() => {
      $(".loader").addClass("hidden");
    }, 1000);
  };

  const [templateName, setTemplateName] = useState(
    data.template_attributes.name || ""
  );
  const [templateId, setTemplateId] = useState(
    data.template_attributes.id || ""
  );
  const [submitModal, setSubmitModal] = useState(0);

  const handleNextClick = () => {
    $('input[name="temp_status"]').prop("checked", false);
    $(".template-name").addClass("hidden");
    $("#template_checkbox_div").removeClass("hidden");
    $("#pro_imp_start_btn").removeClass("hidden");
    if (data.template_attributes.length === 0) {
    } else {
      setTemplateName(data.template_attributes.name);
      setTemplateId(data.template_attributes.id);
      $("#customSwitch1").prop("checked", true);
      $(".template-name").removeClass("hidden");
      $(".template-name input").attr("disabled");
    }
    setSubmitModal(1);
  };
  $(".close-import-modal").click(function () {
    setSubmitModal(0);
  });
  const handleSaveTemplateClick = () => {
    $('input[name="temp_status"]').prop("checked", true);
    $(".template-name").removeClass("hidden");
    setTemplateName(data.template_attributes.name);
    $("#template_checkbox_div").addClass("hidden");
    $("#pro_imp_start_btn").addClass("hidden");
    setSubmitModal(1);
  };
  const [ignoreUnmapped, setIgnoreUnmapped] = useState("off");
  const hideModal = () => {
    setSubmitModal(0);
  };
  // Function to handle checkbox changes
  const handleMappedCheckbox = (event) => {
    if (event.target.checked) {
      setIgnoreUnmapped("on"); // Set state to "yes" if checkbox is checked
    } else {
      setIgnoreUnmapped("off"); // Set state to "no" if checkbox is unchecked
    }
  };

  const [variantsData, setVariantsData] = useState([]);
  const updateVariantsData = (selectedVariants) => {
    setVariantsData(selectedVariants);
  };
  const [reset, setReset] = useState();
  var _token = $('meta[name="csrf-token"]').attr("content");
  const file_path = data.file_path;
  const template_method_type = data.data_required.template_method_type;
  const organization_id = data.data_required.organization_id;
  const catalogsValue = data.data_required.catalogs;
  const catalog = Object.keys(catalogsValue)[0];
  const versionValue = data.data_required.versions;
  const version = Object.keys(versionValue)[0];

  $(document).ready(function () {
    var fromoptionValues = [];
    var tooptionValues = [];
    var $fromselect = $(".mapping-from-select").eq(0);

    // Get the values of the options within the selected select element
    $fromselect.find("option").each(function () {
      fromoptionValues.push({
        value: $(this).val(),
        text: $(this).text(),
      });
    });

    var $toselect = $(".mapping-to-select").eq(0);

    // Get the values of the options within the selected select element
    $toselect.find("option").each(function () {
      tooptionValues.push({
        value: $(this).val(),
        text: $(this).text(),
      });
    });

    setTimeout(function () {
      var rowCount = $(".warning-msg:not(.hidden)").length;
      if (rowCount == 0) {
        $("#pro_imp_btn").removeAttr("disabled");
      }
      $("#invalid_row_count").text(rowCount);
    }, 1000);
  });
  const [newlyReceivedValue, setNewlyReceivedValue] = useState("");
  let inputArrayToUse;
  let outputArrayToUse;
  let outputArrayToSelect = [];
  let formulaArrayToUse;

  if (data.template_attributes.length === 0) {
    inputArrayToUse = data.input_array;
    outputArrayToUse = data.output_array;

    const defaultFormulas = Array(inputArrayToUse.nodes.Default.length).fill(
      "assign"
    );

    formulaArrayToUse = defaultFormulas;
  } else {
    // Your templated data transformation logic here
    const templated_data = data.template_attributes.template.data;
    outputArrayToUse = data.output_array;
    const templated_inputArray = {
      array_name: "CSV",
      nodes: {
        Default: [],
      },
    };
    console.log("outputArrayToUse", outputArrayToUse);
    templated_data.forEach((item) => {
      if (item.from && item.from[0] && typeof item.from[0] === "string") {
        const fromParts = item.from[0].split(",");
        const fromfamily = fromParts[0];
        const csvattribute = fromParts[1];

        if (!templated_inputArray.nodes[fromfamily]) {
          templated_inputArray.nodes[fromfamily] = [];
        }

        templated_inputArray.nodes[fromfamily].push(csvattribute);
      }
    });

    inputArrayToUse = templated_inputArray;
    const extractedValues = [];
    templated_data.forEach((obj, index) => {
      const extractedObj = {};

      if (Array.isArray(obj.to)) {
        extractedObj.to = obj.to;
      }

      if (obj.hasOwnProperty("with")) {
        extractedObj.with = obj.with;
      }

      if (obj.hasOwnProperty("replace")) {
        extractedObj.replace = obj.replace;
      }

      extractedValues[index] = extractedObj;
    });

    // Call the function with your initial array
    outputArrayToSelect = extractedValues;
    const withFormulaArray = templated_data.map((item) => item.with_formula);
    const newoutpurArray = templated_data.map((item) => item.to);
    console.log("newoutpurArray", outputArrayToSelect);
    formulaArrayToUse = withFormulaArray;
  }
  const [outputArray, setOutputArray] = useState(outputArrayToUse);
  // State to hold the newly received value
  const [input_array, setInputArray] = useState(inputArrayToUse);
  const [selectedValueFrom, setSelectedValueFrom] = useState(
    Array(input_array.nodes.Default.length).fill("")
  );
  const [selectedValuesTo, setSelectedValuesTo] = useState(
    Array(input_array.nodes.Default.length).fill("")
  );
  const [selectedFormulas, setSelectedFormulas] = useState(formulaArrayToUse);
  const [selectedFieldsTo, setSelectedFieldsTo] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(
    input_array.nodes.Default.length - 1
  );
  const handleAddRow = () => {
    const newIndex = currentIndex + 1;

    setInputArray((prevArray) => {
      const newArray = { ...prevArray }; // Create a shallow copy of the input_array
      const defaultNodes = [...newArray.nodes.Default]; // Create a shallow copy of the Default nodes array

      // Insert the new row at the desired index
      defaultNodes.splice(newIndex, 0, input_array.nodes.Default[0]);

      // Update the nodes.Default property with the modified array
      newArray.nodes.Default = defaultNodes;
      return newArray; // Return the updated input_array
    });
    const updatedFormulas = [...selectedFormulas];
    // Update the value at the newIndex
    updatedFormulas[newIndex] = "assign";

    // Set the state with the updated array
    setSelectedFormulas(updatedFormulas);
    setCurrentIndex(newIndex);
  };
  const handleAttributeClick = (e) => {
    $(".createattributemodal ").removeClass("hidden");
    var createAttributeId = e.target.id;

    $("#mapping_create_new_attribute_btn").attr("data-id", createAttributeId);
  };
  const handleSelectChangeFrom = (index, selectedValueFrom) => {
    setSelectedValueFrom((prevSelectedValues) => {
      const updatedSelectedValues = [...prevSelectedValues];
      updatedSelectedValues[index] = selectedValueFrom;
      return updatedSelectedValues;
    });
  };
  const handleSelectChangeTo = (index, selectedData) => {
    setSelectedFieldsTo((prevSelectedFields) => {
      const updatedSelectedFieldsTo = [...prevSelectedFields];

      if (selectedData.assign) {
        updatedSelectedFieldsTo[index] = selectedData.assign;
      } else if (selectedData.slug) {
        updatedSelectedFieldsTo[index] = selectedData.slug;
      }

      return updatedSelectedFieldsTo;
    });
  };

  const [selectedFormula, setSelectedFormula] = useState("assign"); // Initialize with a default value

  const handleFormulaChange = (index, formula) => {
    // Update the selected formula for a specific row
    const newFormulas = [...selectedFormulas];
    newFormulas[index] = formula;
    setSelectedFormulas(newFormulas);
  };
  function Validation() {
    $(".mapping-from-select").each(function (index) {
      var selectedValue = $(this).val();

      const parts = selectedValue.split(",");
      // Get the second part (index 1) which is "title"
      selectedValue = parts[1];

      var tofeild = $(this)
        .closest(".from")
        .next()
        .next()
        .find(".mapping-to-select");
      var parentdiv = $(this).closest(".mapping-item")[0];
      tofeild.find("option").each(function () {
        // Get the option element
        var option = $(this);
        // Get the option's value and text
        var value = option.val();
        var text = option.text().toLowerCase();
        const parts = value.split(",");
        // Get the second part (index 1) which is "title"
        value = parts[1];
        if (selectedValue == undefined) {
          selectedValue = "";
        } else {
          selectedValue = selectedValue.toLowerCase();
        }
        if (selectedValue == value || selectedValue == text) {
          option.prop("selected", true);
        }
      });
      if (tofeild.val() !== "" && selectedValue !== "") {
        $(parentdiv).removeClass("border-yellow-500");
        $(parentdiv).addClass("border-l-green-500");
        $(parentdiv).removeClass("border-t");
        $(parentdiv).removeClass("border-r");
        $(parentdiv).removeClass("border-b");
        $(parentdiv).removeClass("pb-2");
        $(parentdiv).addClass("pb-2");
        $(parentdiv).find(".warning-msg").addClass("hidden");
      }
      if ($(this).val() !== "" && tofeild.val() == "Default,handle") {
        $("#handle_warning").addClass("hidden");
      }
    });
  }
  $(document).on("change", ".mapping-from-select", function () {
    var thisSelectValue = $(this).val();
    var toSelectValue = $(this)
      .closest(".from")
      .siblings(".to")
      .find(".mapping-to-select")
      .val();
    var warningMsg = $(this)
      .closest(".from")
      .parent(".main-row")
      .siblings(".warning-msg");
    var parentdiv = $(this).closest(".mapping-item");
    if (toSelectValue == "Default,handle" && thisSelectValue !== "") {
      $("#handle_warning").addClass("hidden");
      parentdiv.removeClass("border-yellow-500");
      parentdiv.addClass("border-l-green-500");
      parentdiv.removeClass("border-t");
      parentdiv.removeClass("border-r");
      parentdiv.removeClass("border-b");
      warningMsg.addClass("hidden");
      parentdiv.addClass("pb-2");
      parentdiv.closest(".mapping-item");
      parentdiv.removeClass("pb-2");
    } else if (toSelectValue !== "" && thisSelectValue !== "") {
      parentdiv.addClass("border-yellow-500");
      parentdiv.removeClass("border-l-green-500");
      parentdiv.addClass("border-t");
      parentdiv.addClass("border-r");
      parentdiv.addClass("border-b");
      warningMsg.removeClass("hidden");
      parentdiv.addClass("pb-2");
      parentdiv.closest(".mapping-item");
      parentdiv.removeClass("pb-2");
    } else if (toSelectValue !== "" && thisSelectValue == "") {
      $("#handle_warning").removeClass("hidden");
      parentdiv.addClass("border-yellow-500");
      parentdiv.removeClass("border-l-green-500");
      parentdiv.addClass("border-t");
      parentdiv.addClass("border-r");
      parentdiv.addClass("border-b");
      warningMsg.removeClass("hidden");
      parentdiv.addClass("pb-2");
      parentdiv.closest(".mapping-item");
      parentdiv.removeClass("pb-2");
    } else if (toSelectValue == "" && thisSelectValue !== "") {
      parentdiv.addClass("border-yellow-500");
      parentdiv.removeClass("border-l-green-500");
      parentdiv.addClass("border-t");
      parentdiv.addClass("border-r");
      parentdiv.addClass("border-b");
      warningMsg.removeClass("hidden");
      parentdiv.addClass("pb-2");
      parentdiv.closest(".mapping-item");
      parentdiv.removeClass("pb-2");
    } else {
      $("#handle_warning").removeClass("hidden");
      parentdiv.addClass("border-yellow-500");
      parentdiv.removeClass("border-l-green-500");
      parentdiv.addClass("border-t");
      parentdiv.addClass("border-r");
      parentdiv.addClass("border-b");
      warningMsg.removeClass("hidden");
      parentdiv.addClass("pb-2");
      parentdiv.closest(".mapping-item");
      parentdiv.removeClass("pb-2");
    }
    // Update the rowCount and enable/disable button logic
    var rowCount = $(".warning-msg:not(.hidden)").length;
    $("#invalid_row_count").text(rowCount);

    if (rowCount === 0) {
      $("#pro_imp_btn").removeAttr("disabled");
    }
  });
  $(".mapping-to-select").on("change", function () {
    var thisSelectValue = $(this).val();
    var fromSelectValue = $(this)
      .closest(".to")
      .siblings(".from")
      .find(".mapping-from-select")
      .val();
    var formulaValue = $(this)
      .closest(".to")
      .siblings(".formula")
      .find(".formula_field")
      .val();
    var warningMsg = $(this)
      .closest(".to")
      .parent(".main-row")
      .siblings(".warning-msg");
    var parentdiv = $(this).closest(".mapping-item");
    setTimeout(function () {
      if (formulaValue == "short_code") {
        var mergeAdvance = $(this)
          .closest(".to")
          .siblings(".from")
          .find(".short_code_div ");
        if (thisSelectValue !== "" && !mergeAdvance.is(":empty")) {
          parentdiv.removeClass("border-yellow-500");
          parentdiv.addClass("border-l-green-500");
          parentdiv.removeClass("border-t");
          parentdiv.removeClass("border-r");
          parentdiv.removeClass("border-b");
          warningMsg.addClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        } else {
          parentdiv.addClass("border-yellow-500");
          parentdiv.removeClass("border-l-green-500");
          parentdiv.addClass("border-t");
          parentdiv.addClass("border-r");
          parentdiv.addClass("border-b");
          warningMsg.removeClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        }
      } else {
        if (fromSelectValue !== "" && thisSelectValue == "Default,handle") {
          $("#handle_warning").addClass("hidden");
          parentdiv.removeClass("border-yellow-500");
          parentdiv.addClass("border-l-green-500");
          parentdiv.removeClass("border-t");
          parentdiv.removeClass("border-r");
          parentdiv.removeClass("border-b");
          warningMsg.addClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        } else if (fromSelectValue !== "" && thisSelectValue !== "") {
          parentdiv.removeClass("border-yellow-500");
          parentdiv.addClass("border-l-green-500");
          parentdiv.removeClass("border-t");
          parentdiv.removeClass("border-r");
          parentdiv.removeClass("border-b");
          warningMsg.addClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        } else if (fromSelectValue !== "" && thisSelectValue == "") {
          parentdiv.addClass("border-yellow-500");
          parentdiv.removeClass("border-l-green-500");
          parentdiv.addClass("border-t");
          parentdiv.addClass("border-r");
          parentdiv.addClass("border-b");
          warningMsg.removeClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        } else if (fromSelectValue == "" && thisSelectValue !== "") {
          parentdiv.addClass("border-yellow-500");
          parentdiv.removeClass("border-l-green-500");
          parentdiv.addClass("border-t");
          parentdiv.addClass("border-r");
          parentdiv.addClass("border-b");
          warningMsg.removeClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        } else {
          parentdiv.addClass("border-yellow-500");
          parentdiv.removeClass("border-l-green-500");
          parentdiv.addClass("border-t");
          parentdiv.addClass("border-r");
          parentdiv.addClass("border-b");
          warningMsg.removeClass("hidden");
          parentdiv.addClass("pb-2");
          parentdiv.closest(".mapping-item");
          parentdiv.removeClass("pb-2");
        }
      }
    }, 500);
    // Update the rowCount and enable/disable button logic
    var rowCount = $(".warning-msg:not(.hidden)").length;
    $("#invalid_row_count").text(rowCount);

    if (rowCount === 0) {
      $("#pro_imp_btn").removeAttr("disabled");
    }
  });

  useEffect(() => {
    // Assuming you have jQuery available
    $(document).ready(function () {
      var checkbox = $("#ignore_unmapped");
      function check_valid() {
        var initialStatus = checkbox.prop("checked");

        if (initialStatus) {
          $("#pro_imp_btn").removeAttr("disabled");
        } else {
          var isValid = false;
          $(".mapping-item").each(function (index) {
            if ($(this).hasClass("border-yellow-500")) {
              isValid = true;
              return false; // Exit the loop early if we found a matching value
            }
          });

          if (isValid) {
            $("#pro_imp_btn").attr("disabled", "disabled");
          } else {
            $("#pro_imp_btn").removeAttr("disabled");
          }
        }
      }
      setTimeout(function () {
        Validation();
      }, 100);
      check_valid();
      $(document).scroll(function () {
        if ($(this).scrollTop() >= $(".import_tabs ").offset().top - 50) {
          $(".import_tabs").addClass("fixed-header");
        } else {
          $(".import_tabs").removeClass("fixed-header");
        }
      });
      $(".close-attribute-modal").click(function () {
        $(".createattributemodal ").addClass("hidden");
      });
      $(document).on("click", ".close_row", function () {
        var $mappingItem = $(this).parent(".mapping-item"); // Store a reference to the mapping-item

        $mappingItem.addClass("mapping-item-isremoving");

        setTimeout(function () {
          $mappingItem.remove();
          var rowCount = $(".warning-msg:not(.hidden)").length;
          $("#invalid_row_count").text(rowCount);
        }, 225);
      });

      function checkValues(divElement) {
        const fromSelect = divElement.querySelector(".mapping-from-select");
        const toSelect = divElement.querySelectorAll(".mapping-to-select");

        // Check if both "from" and at least one "to" select have values selected
        if (fromSelect.value !== "" && toSelect[0].value !== "") {
          return true;
        } else {
          return false;
        }
      }
      $(document).on("change", ".formula_field", function () {
        if ($(this).val() === "short_code") {
          var assignFormula = $(this)
            .parents(".main-row")
            .find(".assign_formula");
          assignFormula.removeClass("sm:w-6/12");
          assignFormula.addClass("sm:w-4/12");
          var innerChild = assignFormula.find("sm\\:w-4\\/12, .w-6\\/12");
          innerChild.addClass("sm:w-8/12");
          innerChild.removeClass("sm:w-4/12");
        } else {
          var assignFormula = $(this)
            .parents(".main-row")
            .find(".assign_formula");
          assignFormula.addClass("sm:w-6/12");
          assignFormula.removeClass("sm:w-4/12");
          var innerChild = assignFormula.find("sm\\:w-4\\/12, .w-6\\/12");
          innerChild.removeClass("sm:w-8/12");
          innerChild.addClass("sm:w-4/12");
        }
      });

      // Function to handle the checking and alert

      // Add a change event handler to monitor changes
      checkbox.change(function () {
        if ($(this).prop("checked")) {
          check_valid();
        } else {
          check_valid();
        }
      });
      // Add event listeners to "from" and "to" select elements

      // Loop through all MappingFrom and MappingTo elements
    });
  }, [selectedFormulas, selectedValueFrom, selectedValuesTo]);

  return (
    <div id="add_row" className="w-full formStyle">
      <div className="loader fixed w-full h-full bg-[rgba(255,255,255,0.5)] left-0 top-0 hidden">
        <div role="status" className="flex justify-center items-center h-full">
          <svg
            aria-hidden="true"
            className="w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
          <span className="sr-only">Loading...</span>
        </div>
      </div>
      <div className="flex flex import_tabs items-center product-header">
        <ul className="md:w-4/12 w-full  tab-list flex justify-start items-center">
          <li className="">
            <button
              className="px-5 btn btn-outline-primary mr-4 reset_feilds"
              onClick={handleReset}
            >
              Reset
            </button>
            <button
              className="px-5 btn btn-primary mr-4"
              onClick={handleSaveTemplateClick}
            >
              Save Template
            </button>
          </li>
        </ul>
        <div className="md:w-4/12 flex w-full justify-center items-center">
          <div>
            <p className="mb-0 clr-grey">
              You have <span id="invalid_row_count"></span> unmapped columns
            </p>
            <p className="mb-0 d-flex align-items-center clr-grey">
              <input
                type="checkbox"
                name="ignore_unmapped"
                id="ignore_unmapped"
                className="me-2"
                onChange={handleMappedCheckbox}
              />{" "}
              <label htmlFor="ignore_unmapped">
                Don’t proceed data in unmapped columns
              </label>
            </p>
          </div>
        </div>
        <div className="md:w-4/12 flex w-full justify-end items-center">
          <div className="p-2  items-center ">
            <div className="p-2 mr-4">
              <p id="handle_warning" className="text-danger mb-0">
                <i className="bi bi-exclamation-triangle"></i> Product
                Identifier column not identified
              </p>
            </div>
            <button
              disabled=""
              id="pro_imp_btn"
              className="form-control ripplelink px-5 btn btn-primary mr-4 "
              onClick={handleNextClick}
              title="If this button is disabled then please make sure all your unmapped fields are mapped or you can mark the side check box."
              data-bs-toggle="tooltip"
              data-bs-placement="top"
            >
              Next
            </button>
          </div>
        </div>
      </div>
      {resetClickedAlert === false ? (
        <div className="mapping-container">
          {input_array.nodes.Default.map((item, index) => (
            <div
              key={item}
              className="mapping-item relative mt-2 mb-4 px-4 pt-2 pb-2 justify-content-between border-t border-r border-b border-l-8 rounded-md border-yellow-500 shadow-md"
            >
              <div className="flex flex-wrap main-row">
                {selectedFormulas[index] === "short_code" ? (
                  <MappingFromMergeAdv
                    index={index}
                    selectedValueFrom={selectedValueFrom}
                    onSelectedValueChange={handleSelectChangeFrom}
                    selectedFormula={selectedFormulas[index]}
                    data={data}
                  />
                ) : (
                  <MappingFrom
                    index={index}
                    selectedValueFrom={selectedValueFrom}
                    onSelectedValueChange={handleSelectChangeFrom}
                    selectedFormula={selectedFormulas[index]}
                    inputArray={input_array}
                  />
                )}
                <MappingFormula
                  index={index}
                  selectedFormula={selectedFormulas}
                  onFormulaChange={handleFormulaChange}
                />
                <MappingTo
                  index={index}
                  selectedValue={selectedValuesTo}
                  outputArray={outputArray}
                  onSelectedValueChange={handleSelectChangeTo}
                  selectedFormula={selectedFormulas[index]}
                  newlyReceivedValue={newlyReceivedValue}
                  inputArray={input_array}
                  outputArrayToSelect={outputArrayToSelect}
                  data={data}
                />
              </div>
              <div className="text-red-500 absolute right-2 top-2 close_row">
                <i className="fa-solid fa-times cursor-pointer"></i>
              </div>
              <div className="flex flex-wrap w-full">
                <div
                  className="flex my-2 warning-msg"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  aria-label="Please map this row fields."
                  data-bs-original-title="Please map this row fields."
                >
                  <svg
                    className="mr-1"
                    width="14"
                    height="15"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                      stroke="#FFC107"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    ></path>
                    <path
                      d="M12 8.5V13.5"
                      stroke="#FFC107"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    ></path>
                    <path
                      d="M11.9922 16.5H12.0012"
                      stroke="#FFC107"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    ></path>
                  </svg>
                  <span className="font-bold text-xs">
                    Please map this row fields.
                  </span>
                </div>
                <div className="flex justify-end">
                  <button
                    id={`creat-attribute-${index}`}
                    type="button"
                    className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                    onClick={handleAttributeClick}
                  >
                    Create Apimio Attribute
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="mapping-container">
          {input_array.nodes.Default.slice(0, 4).map((item, index) => (
            <div
              key={item}
              className="mapping-item relative mt-2 mb-4 px-4 pt-2 pb-2 justify-content-between border-t border-r border-b border-l-8 rounded-md border-yellow-500 shadow-md"
            >
              <div className="flex flex-wrap main-row">
                {selectedFormulas[index] === "short_code" ? (
                  <MappingFromMergeAdv
                    index={index}
                    selectedValueFrom={selectedValueFrom}
                    onSelectedValueChange={handleSelectChangeFrom}
                    selectedFormula={selectedFormulas[index]}
                    data={data}
                  />
                ) : (
                  <MappingFrom
                    index={index}
                    selectedValueFrom={selectedValueFrom}
                    onSelectedValueChange={handleSelectChangeFrom}
                    selectedFormula={selectedFormulas[index]}
                    inputArray={input_array}
                  />
                )}
                <MappingFormula
                  index={index}
                  selectedFormula={selectedFormulas}
                  onFormulaChange={handleFormulaChange}
                />
                <MappingTo
                  index={index}
                  selectedValue={selectedValuesTo}
                  outputArray={outputArray}
                  onSelectedValueChange={handleSelectChangeTo}
                  selectedFormula={selectedFormulas[index]}
                  newlyReceivedValue={newlyReceivedValue}
                  inputArray={input_array}
                  outputArrayToSelect={outputArrayToSelect}
                  data={data}
                />
              </div>
              <div className="text-red-500 absolute right-2 top-2 close_row">
                <i className="fa-solid fa-times cursor-pointer"></i>
              </div>
              <div className="absolute right-2 bottom-2">
                <button
                  id={`creat-attribute-${index}`}
                  type="button"
                  className="btn-outline-primary border-0 text-sm hover:bg-transparent hover:text-black"
                  onClick={handleAttributeClick}
                >
                  Create Apimio Attribute
                </button>
              </div>
              <div
                className="flex my-2 warning-msg"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                aria-label="Please map this row fields."
                data-bs-original-title="Please map this row fields."
              >
                <svg
                  className="mr-1"
                  width="14"
                  height="15"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z"
                    stroke="#FFC107"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                  <path
                    d="M12 8.5V13.5"
                    stroke="#FFC107"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                  <path
                    d="M11.9922 16.5H12.0012"
                    stroke="#FFC107"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                </svg>
                <span className="font-bold text-xs">
                  Please map this row fields.
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
      <div
        className="add_new_row cursor-pointer px-5 btn btn-outline-primary"
        onClick={handleAddRow}
      >
        <i className="fa fa-plus-circle mr-2"></i>
        Add New Row
      </div>
      <div className="createattributemodal hidden">
        <CreateAttributeModal data={data} />
      </div>
      <div className={`submitmodal ${submitModal === 0 ? "hidden" : ""}`}>
        <SubmitModal
          ignoreUnmapped={ignoreUnmapped}
          variantsData={variantsData}
          hideModal={hideModal}
          templateName={templateName}
          templateId={templateId}
        />
      </div>
      {addVLookupModal === 0 ? (
        ""
      ) : (
        <AddVLookupModal handleCloseModal={handleCloseModal} />
      )}
    </div>
  );
}

export default ImportProducts;
