import ReactDOM from 'react-dom/client';

export default function TestwithArgument(props) {
    console.log(props);
    return "TestwithArgument component is alive!";
}
const rootElement = document.getElementById('TestwithArgument659fe77b28bad');
const propsToPass = {};
propsToPass.name = rootElement.getAttribute('data-name');

ReactDOM.createRoot(rootElement).render(
    <TestwithArgument {...propsToPass} />
);
