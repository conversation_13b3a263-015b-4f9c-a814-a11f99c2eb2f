import React, { useState, useEffect } from "react";
import axios from "axios";
import ReactDOM from "react-dom/client";

export default function NoVariants({ initialData }) {
    const urlString = window.location.href;
    const url = new URL(urlString);
    const pathSegments = url.pathname.split("/").filter(Boolean);
    const prodId = pathSegments[1];
    const versionId = pathSegments[3];
    const [variantData, setVariantData] = useState(initialData);
    const [currency, setCurrency] = useState("");
    const [required, setRequired] = useState({});
    const [isUserModified, setIsUserModified] = useState(false);
    const [variantDataJson, setVariantDataJson] = useState(JSON.stringify(initialData));

    useEffect(() => {
        axios
            .get(`/api/product/${prodId}/versions/${versionId}/variants`)
            .then((response) => {
                console.log(response);
                const { currency, variantsData, variant_settings } = response.data.data;
                setCurrency(currency);
                const variantsArray = Array.isArray(variantsData) ? variantsData : [];
                setVariantData(variantsArray);
                setRequired(variant_settings); // Set the required state
                setVariantDataJson(JSON.stringify(variantsArray)); // Initially set the JSON to fetched data
            })
            .catch((error) => {
                console.error("Failed to fetch variant data:", error);
            });
    }, [prodId, versionId]);

    useEffect(() => {
        const handleBeforeUnload = (event) => {
            if (isUserModified && !window.isSaving) {
                event.preventDefault();
                event.returnValue = "";
            }
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [isUserModified]);


    const handleInputChange = (e, index, field) => {
        setIsUserModified(true);
        const { value } = e.target;
        const fieldsToValidate = ["price", "compare_at_price", "weight", "cost_price"];

        // Check if the field needs to be validated as a number
        if (fieldsToValidate.includes(field)) {
            // Regular expression to validate the input as a decimal or integer number
            
                // Only update if the input is a valid number or empty (to allow clearing the input)
                var updatedVariants = variantData.map((variant, variantIndex) => {
                    if (index === variantIndex) {
                        return { ...variant, [field]: value };
                    }
                    return variant;
                });
                setVariantData(updatedVariants);
                setVariantDataJson(JSON.stringify(updatedVariants));
           
        } else {
            // For all other inputs that do not need number validation
            var updatedVariants = variantData.map((variant, variantIndex) => {
                if (index === variantIndex) {
                    return { ...variant, [field]: value };
                }
                return variant;
            });
            setVariantData(updatedVariants);
            setVariantDataJson(JSON.stringify(updatedVariants));
        }
    };

    const shouldShowAsterisk = (key) => required[key] === "1";

    const getInputClass = (key, value) => {
        // Add 'border-danger' only if required and unfilled
        return `form-control ${required[key] === "1" && !value ? "border-danger" : ""}`;
    };

    
    const getPopoverContent = (key, value) => {
        // Check if the field is required and the value is empty
        if (required[key] === "1" && !value) {
            return "This field is required."; // Required and empty
        }

        // Specific rules for SKU and barcode
        if (key === "sku" || key === "barcode") {
            // No additional checks needed beyond required
        }
        // Specific rules for weight, price, cost price, and compare at price
        else if (key === "weight" || key === "price" || key === "cost_price" || key === "compare_at_price") {
            if (value && isNaN(value)) {
                return "Please enter a numeric value."; // Non-numeric input
            }
            if (value < 0) {
                return "Value cannot be less than zero."; // Negative values
            }
        }

        return "✔"; // All checks passed or not required
    };


    const getCircleClass = (key, value) => {
        // Determine class based on whether the field is required and whether it has a value
        const message = getPopoverContent(key, value);
        if (message !== "✔") {
            return "circle-sm align-text-top cursor-pointer circle-error";
        }
        if (required[key] === "1") {
            return value
                ? "circle-sm align-text-top cursor-pointer circle-success"
                : "circle-sm align-text-top cursor-pointer circle-error";
        } else {
            return "circle-sm align-text-top cursor-pointer circle-success";
        }
    };

    $(document).ready(function () {
        $('[data-bs-toggle="popover"]').popover();
    });
    return (
        <div>
            {variantData.map((variant, index) => (
                <div key={variant.id || index}>
                    {/* <h3>Variant {index + 1}</h3> */}
                    <div className="form-group d-flex justify-content-between formStyle mb-3 mb-lg-2 mb-xl-3 col-12">
                        <div className="col-4 px-2">
                            <label>
                                SKU {shouldShowAsterisk("sku") && <span className="text-danger">*</span>}
                                <span
                                    id="dot_45345345345345345"
                                    className={getCircleClass("sku", variant.sku)}
                                    data-bs-toggle="popover"
                                    data-bs-trigger="hover focus"
                                    data-bs-content={getPopoverContent("sku", variant.sku)}
                                    data-bs-placement="right"
                                    data-bs-html="true"
                                    data-bs-original-title=""
                                    title=""
                                ></span>
                            </label>
                            <input
                                type="text"
                                value={variant.sku || ""}
                                className={getInputClass("sku", variant.sku)}
                                name="variant_sku[]"
                                onChange={(e) => handleInputChange(e, index, "sku")}
                            />
                        </div>
                        <div className="col-4 px-2">
                            <label>
                                UPC/Barcode{shouldShowAsterisk("barcode") && <span className="text-danger">*</span>}
                                <span
                                    id="dot_45345345345345345"
                                    className={getCircleClass("barcode", variant.barcode)}
                                    data-bs-toggle="popover"
                                    data-bs-trigger="hover focus"
                                    data-bs-content={getPopoverContent("barcode", variant.barcode)}
                                    data-bs-placement="right"
                                    data-bs-html="true"
                                    data-bs-original-title=""
                                    title=""
                                ></span>
                            </label>
                            <input
                                type="text"
                                value={variant.barcode || ""}
                                className={getInputClass("barcode", variant.barcode)}
                                name="barcode[]"
                                onChange={(e) => handleInputChange(e, index, "barcode")}
                            />
                        </div>
                        <div className=" col-4 px-2">
                            <label>
                                Weight {shouldShowAsterisk("weight") && <span className="text-danger">*</span>}
                                <span
                                    id="dot_45345345345345345"
                                    className={getCircleClass("weight", variant.weight)}
                                    data-bs-toggle="popover"
                                    data-bs-trigger="hover focus"
                                    data-bs-content={getPopoverContent("weight", variant.weight)}
                                    data-bs-placement="right"
                                    data-bs-html="true"
                                    data-bs-original-title=""
                                    title=""
                                ></span>
                            </label>
                            <div className="d-flex">
                                <select
                                    className="form-control w-25 pt-2"
                                    name="weight_unit[]"
                                    defaultValue={variant.weight_unit || ""}
                                    onChange={(e) => handleInputChange(e, index, "weight_unit")}
                                >
                                    {/* Placeholder option */}

                                    {Object.keys(units.weight).map((keyName, i) => (
                                        <option key={i} value={keyName} className="Poppins regular text-color">
                                            {keyName}
                                        </option>
                                    ))}
                                </select>

                                <input
                                    type="text"
                                    value={variant.weight || ""}
                                    className={getInputClass("weight", variant.weight)}
                                    name="weight[]"
                                    onChange={(e) => handleInputChange(e, index, "weight")}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="form-group d-flex justify-content-between formStyle mb-3 mb-lg-2 mb-xl-3 col-12">
                        <div className="col-4 px-2">
                            <label>
                                Price{shouldShowAsterisk("price") && <span className="text-danger">*</span>}
                                <span
                                    id="dot_45345345345345345"
                                    className={getCircleClass("price", variant.price)}
                                    data-bs-toggle="popover"
                                    data-bs-trigger="hover focus"
                                    data-bs-content={getPopoverContent("price", variant.price)}
                                    data-bs-placement="right"
                                    data-bs-html="true"
                                    data-bs-original-title=""
                                    title=""
                                ></span>
                            </label>
                            <div className="d-flex">
                                <span className="w-25 d-flex align-items-center border rounded justify-content-center fw-normal">
                                    {currency}
                                </span>
                                <input
                                    type="text"
                                    value={variant.price || ""}
                                    className={getInputClass("price", variant.price)}
                                    name="price[]"
                                    onChange={(e) => handleInputChange(e, index, "price")}
                                />
                            </div>
                        </div>
                        <div className="col-4 px-2">
                            <label>
                                Compare at Price{shouldShowAsterisk("compare_at_price") && <span className="text-danger">*</span>}
                                <span
                                    id="dot_45345345345345345"
                                    className={getCircleClass("compare_at_price", variant.compare_at_price)}
                                    data-bs-toggle="popover"
                                    data-bs-trigger="hover focus"
                                    data-bs-content={getPopoverContent("compare_at_price", variant.compare_at_price)}
                                    data-bs-placement="right"
                                    data-bs-html="true"
                                    data-bs-original-title=""
                                    title=""
                                ></span>
                            </label>
                            <div className="d-flex">
                                <span className="w-25 d-flex align-items-center border rounded justify-content-center fw-normal">
                                    {currency}
                                </span>

                                <input
                                    type="text"
                                    value={variant.compare_at_price || ""}
                                    className={getInputClass("compare_at_price", variant.compare_at_price)}
                                    name="compare_at_price[]"
                                    onChange={(e) => handleInputChange(e, index, "compare_at_price")}
                                />
                            </div>
                        </div>

                        <div className="col-4 px-2">
                            <label>
                                Cost Price{shouldShowAsterisk("cost_price") && <span className="text-danger">*</span>}
                                <span
                                    id="dot_45345345345345345"
                                    className={getCircleClass("cost_price", variant.cost_price)}
                                    data-bs-toggle="popover"
                                    data-bs-trigger="hover focus"
                                    data-bs-content={getPopoverContent("cost_price", variant.cost_price)}
                                    data-bs-placement="right"
                                    data-bs-html="true"
                                    data-bs-original-title=""
                                    title=""
                                ></span>
                            </label>
                            <div className="d-flex">
                                <span className="w-25 d-flex align-items-center border rounded justify-content-center fw-normal">
                                    {currency}
                                </span>
                                <input
                                    type="text"
                                    value={variant.cost_price || ""}
                                    className={getInputClass("cost_price", variant.cost_price)}
                                    name="cost_price[]"
                                    onChange={(e) => handleInputChange(e, index, "cost_price")}
                                />
                            </div>
                        </div>
                    </div>
                    <input type="hidden" value={variantDataJson} name="variants" />
                </div>
            ))}
        </div>
    );
}

// Ensure you have the initialData available or set it to an empty array if not
const initialData = []; // This should be your default or initial data
const rootElement = document.getElementById("novariants");
ReactDOM.createRoot(rootElement).render(<NoVariants initialData={initialData} />);
