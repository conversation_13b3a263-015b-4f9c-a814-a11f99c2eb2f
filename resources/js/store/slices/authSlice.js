import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Async thunk for fetching user data
export const fetchUserData = createAsyncThunk(
    'auth/fetchUserData',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get("/api/2024-12/dashboard/1");
            return response.data;
        } catch (error) {
            console.error("Error fetching user data:", error);
            return rejectWithValue(error.message || 'Failed to fetch user data');
        }
    }
);

const initialState = {
    user: null,
    isAuthenticated: false,
    loading: false,
    error: null,
};

export const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
        updateUser: (state, action) => {
            state.user = { ...state.user, ...action.payload };
        },
        logout: (state) => {
            state.user = null;
            state.isAuthenticated = false;
            state.error = null;
            state.loading = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchUserData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchUserData.fulfilled, (state, action) => {
                state.loading = false;
                state.isAuthenticated = true;
                // Extract user data from the API response
                state.user = {
                    id: action.payload.user?.id || 1,
                    name: `${action.payload.user?.fname || ''} ${action.payload.user?.lname || ''}`.trim(),
                    fname: action.payload.user?.fname,
                    lname: action.payload.user?.lname,
                    email: action.payload.user?.email,
                    username: action.payload.user?.username || action.payload.user?.fname,
                    // Include any other user properties you need
                };
                state.error = null;
            })
            .addCase(fetchUserData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    }
});

// Export actions
export const { clearError, updateUser, logout } = authSlice.actions;

// Selectors
export const selectUser = (state) => state.auth.user;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthLoading = (state) => state.auth.loading;
export const selectAuthError = (state) => state.auth.error;

export default authSlice.reducer;
