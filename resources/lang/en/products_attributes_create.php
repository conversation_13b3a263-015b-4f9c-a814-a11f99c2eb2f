<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Products Attributes Create Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during products attributes for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'color' => 'Color',
    'date_and_time' => 'Date and Time',
    'file' => 'File',
    'json' => 'JSON',
    'measurement' => 'Measurement',
    'number' => 'Number',
    'rating' => 'Rating',
    'single_line_text' => 'Single line text',
    'multi_line_text' => 'Multi line text',
    'true_or_false' => 'True or false',
    'url' => 'URL',
    'select' => 'List',

    'attribute_family' => 'Attribute Set',
    'attribute_title' => 'Attribute Title',
    'description' => 'Description',
    'select_date_and_time' => 'Select Date and time',
    'start_date' => 'Minimum Date',
    'end_date' => 'Maximum Date',

    'start_date_and_time' => 'Minimum date and time',
    'end_date_and_time' => 'Maximum date and time',

    'file_type' => 'File Type',
    'file_requirement' => 'File Requirement',
    'height' => 'Height',
    'width' => 'Width',
    'size' => 'Size',
    'max_value' => 'Maximum Value',
    'min_value' => 'Minimum Value',

    'min_weight' => 'Minimum Weight',
    'min_volume' => 'Minimum Volume',
    'min_dimension' => 'Minimum Dimension',
    'max_weight' => 'Maximum Weight',
    'max_volume' => 'Maximum Volume',
    'max_dimension' => 'Maximum Dimension',
    'all_unit' => 'Unit',

    'min_number' => 'Minimum value',
    'max_number' => 'Maximum value',
    'max_number_precision' => 'Maximum precision',

    'min_rating' => 'Rating scale minimum',
    'max_rating' => 'Rating scale maximum',

    'regular_expression' => 'Regular Expression',
    'min_length' => 'Minimum Character Length',
    'max_length' => 'Maximum Character Length',

    'manage_option' => 'Manage Options (Values of your attribute)',
    'add_option_btn' => 'Add New Option',

    'is_required' => 'Is Required?',
    'no' => 'No',
    'yes' => 'Yes',

    'cancel_btn' => 'Cancel',
    'save_btn' => 'Save',
];
