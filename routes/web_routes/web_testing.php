<?php

use App\Events\Product\ManageFamilyAttributes;
use App\Models\Product\Product;
use App\Events\Product\ManageVersion;
use App\Events\Product\ManageCategory;
use App\Events\Product\ManageChannels;
use App\Events\Product\ManageVariants;
use App\Events\Product\ManageSeoFields;
use App\Events\Product\ManageAttributes;
use Illuminate\Support\Facades\Route;
use Apimio\Gallery\Http\Controllers\FileController;
use App\Http\Controllers\Api\BrandsPortal\BrandsPortalController;
use App\Models\BrandsPortal;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Inertia\Inertia;
use App\Jobs\PreviousData\MultiStoreOrganizationMetafields;
use App\Jobs\PreviousData\SingleStoreOrganizationMetafields;
use Illuminate\Http\Request;

/*Route::get('previous-images',function(){
    \App\Jobs\PreviousData\ImagesData::dispatch();
});
Route::get('previous-single-attributes',function(){
    dispatch(new SingleStoreOrganizationMetafields());
});
Route::get('previous-multi-attributes',function(){
    MultiStoreOrganizationMetafields::dispatch();
});*/

if (App::environment(["local", "staging"])) {
    Route::get('/test-users', 'Auth\LoginController@test_users')->name('test-users');
}

Route::get('/test-manage-variants', function () {
    // Create an instance of App\Models\Product\Product
    $product = new Product();
    $product->id = 1; // Set the ID as needed
    $product->sku = 'SKU001'; // Set the SKU as needed
    $product->organization_id = 1; // Set the organization ID as needed
    // $product->save(); // Save the product
    $variants = [
        'data' => [
            // Define your variants here with options
            [
                'name' => 'Variant_6',
                'sku' => '',
                'options' => [
                    'Color' => 'Red',
                    'Size' => 'xl'

                ]
            ],
            // Event will skip this array as it doesnt have options along with name and sku
            [
                'name' => 'Variant_622',
                'sku' => 'SKU061',
                'options' => [
                    'Color' => 'Red',
                    'Size' => 'sm'
                ]
            ]
        ],
        // Add attributes data if required if there are no varaints an empty array will be send to the event,
        'attributes' => [
            [
                //"id"=>81,
                "name" => 'Color',
                "options" => ["Red", "green", "yellow"]
            ],
            [
                //"id"=>91,
                "name" => 'Size',
                "options" => ["sm", "md", "lg", "xl"]
            ],
        ],
    ];

    $data = ['versions' => [['id' => 1]]];  // giving custom version_id as it is needed for saving options thourgh event it will be present  in  Table: variants
    event(new ManageVariants($product, $variants, $data));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageVariants event triggered successfully.';
});
Route::get('/token-generate', function (Request $request) {
    // return $request->user();
    return dd(Auth::user()->createToken('api-token')->plainTextToken);
});

// Route::namespace('Api')->group(function () {
//     Route::prefix('product')->namespace('Product')->group(function() {
//         Route::middleware(['auth'])->group(function () {
//             Route::post('versions/variants/create', 'VariantController@triggerVariantEvent')->name('variants.create');
//             Route::get('attributes/variants/options', 'VariantController@getAttributeVariantsOptions');
//             Route::get('{product_id}/versions/{version_id}/variants', 'VariantController@getAttributeVariants');
//         });

//     });

// });
Route::get('/test-manage-versions', function () {
    // Create an instance of App\Models\Product\Product
    $product = new Product();
    $product->id = 1; // Set the ID as needed
    $product->organization_id = 1; // Set the organization ID as needed
    //if you don't send version id it will attach default version
    $versions =
        [
            // Define your variants here
            [
                'id' => 1,
                'name' => 'version_1',

            ],
            [
                'id' => 1,
                'name' => 'version_2',

            ],
            [
                'id' => 1,
                'name' => 'version_3',

            ],
            [
                'id' => 1,
                'name' => 'version_4',

            ]


        ];

    event(new ManageVersion($product, $versions));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageVersions event triggered successfully.';
});

Route::get('/test-manage-channels', function () {
    // Create an instance of App\Models\Product\Product
    $product = new Product();
    $product->id = 1; // Set the ID as needed
    $product->organization_id = 1; // Set the organization ID as needed
    $channels =  [
        // Define your variants here
        [
            'id' => 1,
            'name' => 'channel_1',
            // 'version_id' => 'version_1',

        ],
        [
            'id' => 1,
            'name' => 'channel_2',
            // 'version_id' => 'version_2',
        ],
        [
            'id' => 1,
            'name' => 'channel_3',
            // 'version_id' => 'version_3',
        ],
        [
            'id' => 1,
            'name' => 'channel_4',
            // 'version_id' => 'version_4',
        ]


    ];

    event(new ManageChannels($product, $channels));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageChannels event triggered successfully.';
});

Route::get('/test-manage-categories', function () {
    // Create an instance of App\Models\Product\Product
    $product = new Product();
    $product->id = 1; // Set the ID as needed
    $product->organization_id = 1; // Set the organization ID as needed
    $categories = [
        // Define your variants here
        [
            'id' => 1,
            'name' => 'category_1',

        ],
        [
            'id' => 1,
            'name' => 'category_2',

        ],
        [
            'id' => 1,
            'name' => 'category_3',

        ],
        [
            'id' => 1,
            'name' => 'category_4',

        ]


    ];

    event(new ManageCategory($product, $categories));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageCategory event triggered successfully.';
});

Route::get('/test-manage-attributes', function () {
    // Create an instance of App\Models\Product\Product
    $product = new Product();
    $product->id = 1; // Set the ID as needed
    $product->organization_id = 1; // Set the organization ID as needed
    $this->data = ['version_id' => 1, 'attribute' => [81 => ['value' => 'gh', 'measurement' => 'cm'], 91 => ['value' => 'zz', 'measurement' => 'cm']]]; // Set the keys to be attribute_family_id and values to be attribute values
    $version = $product->versions()->find($this->data["version_id"]);
    event(new ManageAttributes($product, $this->data, $version));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageCategory event triggered successfully.';
});

Route::get('/test-manage-family-attributes', function () {
    $product_obj = new Product();
    $product_obj->id = 4; // Set the ID as needed
    $product_obj->organization_id = 1; // Set the organization ID as needed
    $product_obj->status = 1;

    $families = [
        [
            "name" => "General",
            "attributes" => [
                [
                    "name" => "Product Name",
                    'handle' => "product_name",
                    'attribute_type_id' => 1,
                    "value" => "Paradise 21-404",
                ],
                [
                    "name" => "Product Name",
                    'handle' => "description",
                    'attribute_type_id' => 1,
                    "value" => "<p>SFSD FA&nbsp; SDAGD FSA FSDF DDAF SDA FDA G</p>",
                ],
                [
                    "name" => "Select Name",
                    'handle' => "select-name",
                    'attribute_type_id' => 4,
                    "value" => [
                        "value" => "11",
                        "unit" => 'kg'
                    ],
                ]
            ]
        ],
        [
            "name" => "Custom",
            "attributes" => [
                [
                    'handle' => "t1",
                    'name' => 't1',
                    'attribute_type_id' => 1,
                    'value' => [
                        [
                            "value" => "A",
                            "unit" => null
                        ],
                        [
                            "value" => "b",
                            "unit" => null
                        ],
                        [
                            "value" => "c",
                            "unit" => null
                        ]
                    ]
                ],
                [
                    'handle' => "t2",
                    'attribute_type_id' => 1,
                    "value" => "B no name",
                ]
            ]
        ]
    ];

    $testing = ['id' => 1];

    dump($testing);

    event(new ManageFamilyAttributes($product_obj, $families, [\App\Models\Product\Version::first()]));
    dd($testing);
});

Route::get('/test-manage-seo_fields', function () {
    // Create an instance of App\Models\Product\Product
    $product = new Product();
    $product->id = 1; // Set the ID as needed
    $product->organization_id = 1; // Set the organization ID as needed
    $this->data = ['version_id' => 1];
    event(new ManageSeoFields($product, $this->data["version_id"]));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageCategory event triggered successfully.';
});

Route::get('/test-new-variant', function () {
    //$product = new Product();
    //  $product->id = 1; // Set the ID as needed
    // $product->sku = 'SKU001'; // Set the SKU as needed
    // $product->organization_id = 1; // Set the organization ID as needed
    // $product->save(); // Save the product
    $variants = [
        'data' => [
            // Define your variants here with options
            [
                'name' => 'Variant_655',
                'sku' => 'SKUG-1',
                'price' => 60100,
                'options' => [
                    //                    'Small',"black"
                ]
            ],
            // Event will skip this array as it doesnt have options along with name and sku
            /*[
                'name' => 'Variant_62255',
                'sku' => 'SKUG-10',
                'price'=> 11990,
                /*'options'=>[
                    'Size'=>'Medium'
                ]*/
            // ]
        ],
        // Add attributes data if required if there are no varaints an empty array will be send to the event,
        'attributes' => [
            [
                //"id"=>81,
                "name" => 'Color',
                "options" => ["Red", "green", "yellow"]
            ],
            [
                //"id"=>91,
                "name" => 'Size',
                "options" => ["Small", "Medium"]
            ],
        ],
    ];

    $data = ['versions' => [['id' => 1]], 'organization_id' => 1];  // giving custom version_id as it is needed for saving options thourgh event it will be present  in  Table: variants
    event(new ManageVariants(null, $variants, $data));
    // event(new ManageVariants($product, $variants, $data));
    //event(new ManageVariants($product, $variants));
    // Return a response for testing purposes
    return 'ManageVariants event triggered successfully.';
});

// Test route to detach multiple files
Route::get('/test-detach-files', function () {

    $product_id = 4;
    // $ids = [285, 284, 283, 281];
    // $ids = [288];
    $ids = [265];

    $controller = new FileController();
    return $controller->detach($product_id, $ids);
});


route::get('/test-queue', function () {
    \App\Jobs\TestJob::dispatch();
});



Route::get('/billing-config', function () {
    \Illuminate\Support\Facades\Artisan::call('migrate:fresh --seed');
    \Illuminate\Support\Facades\Artisan::call('schedule:work');
});

Route::get('/install/shopfyV1', function () {
    return view("channel.shopify.install.create");
});
Route::get('/install/shopfyV1', function () {
    return view("channel.shopify.install.welcome");
});
Route::get('/brand-portal', function () {
    return view("brandportal.brandportal");
});
Route::get('public/products/download/', 'Api\BrandsPortal\BrandsPortalController@downloadProducts')->name('public.download');

// Route::get('/csvMapping', function () {
//     return Inertia::render('pages/CSVMapping');
// })->name('csvMapping');

Route::post('/upload-csv', function (Request $request) {
    // dd($request->file('csv_file'));
    $request->validate([
        'csv_file' => 'required|file|mimes:csv,txt',
    ]);

 
   

    // Render the CSVMapping page with the stored file path.
    return Inertia::render('CSVMapping');
})->name('upload-csv');
