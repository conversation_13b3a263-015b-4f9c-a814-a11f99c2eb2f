<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use App\Models\Organization\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::create([
            'name'=>'Add and Edit Product',
            'handle'=>'add_and_edit_product',
        ]);

        Permission::create([
            'name'=>'Connect Shopify',
            'handle'=>'add_shopify',
        ]);
        Permission::create([
            'name'=>'Sync Product to Shopify Channel',
            'handle'=>'sync_product_to_shopify',
        ]);
        Permission::create([
            'name'=>'Perform Billing',
            'handle'=>'perform_billing',
        ]);
        Permission::create([
            'name'=>'Invite Team',
            'handle'=>'invite_team',
        ]);

    }
}
