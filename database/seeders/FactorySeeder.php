<?php

use Illuminate\Database\Seeder;

class FactorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        factory(\App\User::class, 3)->create();
        factory(\App\Models\Organization\Organization::class, 5)->create();
        factory(\App\Models\Organization\OrganizationUser::class, 30)->create();
        factory(\App\Models\Product\Brand::class, 50)->create();
        factory(\App\Models\Product\Category::class, 50)->create();
        factory(\App\Models\Product\Vendor::class, 50)->create();
        factory(\App\Models\Product\Family::class, 60)->create();
        factory(\App\Models\Product\Attribute::class, 50)->create();
        factory(\App\Models\Product\AttributeFamily::class, 500)->create();
        factory(\App\Models\Product\AttributeOption::class, 20)->create();
        factory(\App\Models\Product\Version::class, 30)->create();
        factory(\App\Models\Product\Product::class, 500)->create();
        //factory(\App\Models\Product\FamilyProductVersion::class, 20)->create();
        factory(\App\Models\Product\ProductVersion::class, 100)->create();
        factory(\App\Models\Product\AttributeFamilyProductVersion::class, 1000)->create();
        factory(\App\Models\Product\ProductVendor::class, 100)->create();
        factory(\App\Models\Product\CategoryProduct::class, 100)->create();
        factory(\App\Models\Product\BrandProduct::class, 100)->create();
        factory(\App\Models\Organization\File::class, 100)->create();
        factory(\App\Models\Product\FileProduct::class, 1000)->create();
        factory(\App\Models\Channel\Channel::class, 100)->create();
        factory(\App\Models\Channel\ChannelProduct::class, 100)->create();

        // some custom users for development
        \Illuminate\Support\Facades\DB::table('users')->insert([
            'fname' => 'Ghazni',
            'lname' => 'Ali',
            'email' => '<EMAIL>',
            'email_verified_at' => \Carbon\Carbon::now(),
            'password' => \Illuminate\Support\Facades\Hash::make('alialiali'),
        ]);

        \Illuminate\Support\Facades\DB::table('users')->insert([
            'fname' => 'Hashir',
            'lname' => 'Masood',
            'email' => '<EMAIL>',
            'email_verified_at' => \Carbon\Carbon::now(),
            'password' => \Illuminate\Support\Facades\Hash::make('alialiali'),
        ]);

        \Illuminate\Support\Facades\DB::table('users')->insert([
            'fname' => 'Mubashir',
            'lname' => 'Hussain',
            'email' => '<EMAIL>',
            'email_verified_at' => \Carbon\Carbon::now(),
            'password' => \Illuminate\Support\Facades\Hash::make('12341234'),
        ]);

    }
}
