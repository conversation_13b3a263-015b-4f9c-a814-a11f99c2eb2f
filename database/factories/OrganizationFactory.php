<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Models\Organization\Organization;
use Faker\Generator as Faker;
use App\User;

$factory->define(Organization::class, function (Faker $faker) {
    $organizations = [
        'name' => $faker->company,

        "block_status" =>  $faker->boolean
    ];

//    if(rand(0, 1)) {
//        $organizations = array_merge($organizations, [
//            "region" => $faker->
//            "unites"
//            "currency"
//        ]);
//    }
    return $organizations;
});
