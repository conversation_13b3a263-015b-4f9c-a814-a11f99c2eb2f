<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Models\Product\Attribute;
use Faker\Generator as Faker;
use App\Models\Organization\Organization;
use Illuminate\Support\Facades\DB;

$factory->define(Attribute::class, function (Faker $faker) {
    return [
        'name' => $faker->word,
        'organization_id' => App\Models\Organization\Organization::all()->random()->id,
        'attribute_type_id' => rand(1, 4),
    ];
});
