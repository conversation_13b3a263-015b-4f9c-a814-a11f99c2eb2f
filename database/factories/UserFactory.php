<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\User;
use Faker\Generator as Faker;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(User::class, function (Faker $faker) {
     $users = [
         'fname' => $faker->firstName,
         'lname' => $faker->lastName,
         'email' => $faker->unique()->safeEmail,
         'email_verified_at' => now(),
         'password' => "$2a$12\$XBkPUtqh25BbE928Kdky9OC6KxXUR4Pb6RrQx2kxrYmr.doIPCgM6", // password
         "last_login" => $faker->dateTime($max = 'now'),
         "block_status" => $faker->boolean,
    ];

    if(rand(0, 1)) {
        $users =array_merge($users, [
            "phone" => $faker->e164PhoneNumber,
        ]);
    }

     return $users;
});
