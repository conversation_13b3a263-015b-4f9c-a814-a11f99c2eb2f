<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('error_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->cascadeOnDelete();
            $table->text('description')->nullable();
            $table->string('type')->nullable();
            $table->string('link')->nullable();
            $table->string('link_text')->nullable();
            $table->string('status')->comment('warning,success,error,info')->nullable();
            $table->boolean('is_solved')->default('0')->comment('1=>solved,0=un-solve');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('error_logs');
    }
};
