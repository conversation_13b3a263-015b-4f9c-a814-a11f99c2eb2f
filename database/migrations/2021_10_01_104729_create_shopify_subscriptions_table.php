<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShopifySubscriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shopify_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('shopify_channel_id')->constrained('shopify_channels')->onDelete('cascade');
            $table->string('recurring_application_charge_id', 15)->unique();
            $table->decimal('price', 10, 2);
            $table->enum('status', ['active', 'pending', 'declined', 'expired', 'frozen', 'cancelled']);
            $table->timestamp('billing_on')->comment("when client is billed");
            $table->unsignedInteger('test')->comment("whether it is a test or not.");
            $table->timestamp("activated_on")->nullable();
            $table->timestamp("cancelled_on")->nullable();
            $table->unsignedInteger('trial_days');
            $table->decimal('capped_amount', 10, 2);
            $table->timestamp('trial_ends_on')->nullable();
            $table->decimal('balance_used', 10, 2);
            $table->decimal('balance_remaining', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shopify_subscriptions');
    }
}
