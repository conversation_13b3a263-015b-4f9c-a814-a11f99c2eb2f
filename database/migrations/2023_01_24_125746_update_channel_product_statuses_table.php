<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('channel_product_statuses', function (Blueprint $table) {
            $table->boolean('status')->default(0)->comment('0=>not synced, 1=>synced')->after('type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('channel_product_statuses', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
