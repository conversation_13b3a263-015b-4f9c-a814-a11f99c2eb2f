<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Organization\OrganizationUser;

class CreateOrganizationUserPermissionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('organization_user_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_user_id')->nullable()->constrained('organization_user')->cascadeOnDelete();
            $table->foreignId('team_invite_id')->nullable()->constrained('team_invites')->cascadeOnDelete();
            $table->foreignId('permission_id')->constrained('permissions')->cascadeOnDelete();
            $table->timestamps();
        });
        $user_organization = OrganizationUser::all();
        $permissions = \App\Models\Organization\Permission::all();
        foreach($user_organization as $org)
        {
            foreach($permissions as $permission){
                $o = new \App\Models\Organization\OrganizationUserPermission();
                $o->organization_user_id  = $org->id;
                $o->permission_id         = $permission->id;
                $o->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('organization_user_permissions');
    }
}
