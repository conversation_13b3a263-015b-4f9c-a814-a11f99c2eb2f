<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_items', function (Blueprint $table) {
            // Remove old indexes
            $table->dropUnique('subscription_items_subscription_id_stripe_plan_unique');
            $table->dropIndex('subscription_items_stripe_id_index');

            // Check if the column exists before renaming
            if (Schema::hasColumn('subscription_items', 'stripe_plan')) {
                $table->renameColumn('stripe_plan', 'stripe_product');
            }

            // Add new column for stripe_price
            $table->string('stripe_price')->after('stripe_plan');

            // Add new composite index
            $table->index(['subscription_id', 'stripe_price']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_items', function (Blueprint $table) {
            // Remove newly added index
            $table->dropIndex(['subscription_id', 'stripe_price']);

            // Rename back if necessary
            if (Schema::hasColumn('subscription_items', 'stripe_product')) {
                $table->renameColumn('stripe_product', 'stripe_plan');
            }

            // Drop the stripe_price column
            if (Schema::hasColumn('subscription_items', 'stripe_price')) {
                $table->dropColumn('stripe_price');
            }

            // Add back the old indexes
            $table->unique(['subscription_id', 'stripe_plan'], 'subscription_items_subscription_id_stripe_plan_unique');
            $table->index('stripe_id', 'subscription_items_stripe_id_index');
        });
    }
};
