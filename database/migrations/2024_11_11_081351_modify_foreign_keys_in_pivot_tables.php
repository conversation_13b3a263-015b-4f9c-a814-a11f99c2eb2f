<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify the 'brands_portal_channel' table
        Schema::table('brands_portal_channel', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['brands_portal_id']);
            $table->dropForeign(['channel_id']);

            // Drop columns
            $table->dropColumn('brands_portal_id');
            $table->dropColumn('channel_id');
        });

        // Modify the 'brands_portal_template' table
        Schema::table('brands_portal_template', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['brands_portal_id']);
            $table->dropForeign(['template_id']);

            // Drop columns
            $table->dropColumn('brands_portal_id');
            $table->dropColumn('template_id');
        });

        // Recreate columns with the new constraints in 'brands_portal_channel'
        Schema::table('brands_portal_channel', function (Blueprint $table) {
            $table->unsignedBigInteger('brands_portal_id')->nullable();
            $table->unsignedBigInteger('channel_id')->nullable();

            $table->foreign('brands_portal_id')->references('id')->on('brands_portals')->onDelete('restrict');
            $table->foreign('channel_id')->references('id')->on('channels')->onDelete('restrict');
        });

        // Recreate columns with the new constraints in 'brands_portal_template'
        Schema::table('brands_portal_template', function (Blueprint $table) {
            $table->unsignedBigInteger('brands_portal_id')->nullable();
            $table->unsignedBigInteger('template_id')->nullable();

            $table->foreign('brands_portal_id')->references('id')->on('brands_portals')->onDelete('restrict');
            $table->foreign('template_id')->references('id')->on('templates')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Logic to reverse the operation
        // It would essentially involve dropping and recreating with the original constraints
        // Note: The exact reverse logic should ideally revert to the exact previous state
        Schema::table('brands_portal_channel', function (Blueprint $table) {
            $table->dropForeign(['brands_portal_id']);
            $table->dropForeign(['channel_id']);
            $table->dropColumn('brands_portal_id');
            $table->dropColumn('channel_id');

            $table->unsignedBigInteger('brands_portal_id')->nullable(false); // Adjust based on original schema
            $table->unsignedBigInteger('channel_id')->nullable(false); // Adjust based on original schema
            $table->foreign('brands_portal_id')->references('id')->on('brands_portals')->onDelete('cascade');
            $table->foreign('channel_id')->references('id')->on('channels')->onDelete('cascade');
        });

        Schema::table('brands_portal_template', function (Blueprint $table) {
            $table->dropForeign(['brands_portal_id']);
            $table->dropForeign(['template_id']);
            $table->dropColumn('brands_portal_id');
            $table->dropColumn('template_id');

            $table->unsignedBigInteger('brands_portal_id')->nullable(false); // Adjust based on original schema
            $table->unsignedBigInteger('template_id')->nullable(false); // Adjust based on original schema
            $table->foreign('brands_portal_id')->references('id')->on('brands_portals')->onDelete('cascade');
            $table->foreign('template_id')->references('id')->on('templates')->onDelete('cascade');
        });
    }
};
