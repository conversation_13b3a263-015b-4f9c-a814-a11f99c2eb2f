<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOrganizationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->string('stripe_id')->nullable()->index()->after('block_status');
            $table->string('card_brand')->nullable()->after('stripe_id');
            $table->string('card_last_four', 4)->nullable()->after("card_brand");
            $table->timestamp('trial_ends_at')->nullable()->after("card_last_four");
        });
        $users = \App\User::all();
        foreach ($users as $user) {
            $user_organizations = \App\Models\Organization\OrganizationUser::where('user_id',$user->id)->get();
            if ($user_organizations) {
                $organization = \App\Models\Organization\Organization::whereIn('id',$user_organizations)->first();
                if ($organization) {
                    $organization->stripe_id = $user->stripe_id;
                    $organization->card_brand = $user->card_brand;
                    $organization->card_last_four = $user->card_last_four;
                    $organization->trial_ends_at = $user->trial_ends_at;
                    $organization->save();
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn([
                'stripe_id',
                'card_brand',
                'card_last_four',
                'trial_ends_at',
            ]);
        });
    }
}
