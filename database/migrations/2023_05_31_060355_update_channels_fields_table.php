<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('channels', function (Blueprint $table) {
            DB::statement("ALTER TABLE channels CHANGE COLUMN syncing_method syncing_method ENUM('skip', 'update', 'new') NOT NULL DEFAULT 'update'");
            $table->boolean('is_create_product_webhook_enabled')->default(1)->comment('1=>enable, 0=>disable')->after('syncing_method');
            $table->boolean('is_product_update_webhook_enabled')->default(1)->comment('1=>enable, 0=>disable')->after('is_create_product_webhook_enabled');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('channels', function (Blueprint $table) {
            DB::statement("ALTER TABLE channels CHANGE COLUMN syncing_method syncing_method ENUM('skip', 'update', 'new') NOT NULL DEFAULT 'skip'");
            $table->dropColumn('is_create_product_webhook_enabled');
            $table->dropColumn('is_product_update_webhook_enabled');
        });
    }
};
