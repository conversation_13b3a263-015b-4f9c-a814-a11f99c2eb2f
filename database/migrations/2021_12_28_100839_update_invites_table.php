<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateInvitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('invites', function (Blueprint $table) {
            $table->string('fname')->after("token");
            $table->string('lname')->after("fname")->nullable();
            $table->string('designation')->after("lname")->nullable();
            $table->string('phone', 16)->after("designation")->nullable();
            $table->boolean('is_declined')->after("is_accepted")->default(0)->comment("0 => not declined , 1 => declined");
            $table->enum('type', ["vendor", "retailer"])->after("is_declined");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
