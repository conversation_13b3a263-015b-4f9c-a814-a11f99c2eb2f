<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Product\AttributeType;
use Illuminate\Support\Facades\DB;
class EditAttributesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('attributes',function (Blueprint $table) {
            $table->string('description',255)->nullable()->after('name');
        });

        if (\Illuminate\Support\Facades\App::environment('production')) {
            AttributeType::where('id',1)->update(['name'=>'single line text']);
            AttributeType::where('id',2)->update(['name'=>'number']);
            AttributeType::where('id',3)->update(['name'=>'multi line text']);
            AttributeType::where('id',4)->update(['name'=>'multiselect']);
            DB::table('attribute_types')->insert([
                'name' => 'date and time',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'file',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'measurement',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'rating',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'json',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'true or false',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'url',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'color',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'variants',
            ]);
        } else {
            DB::table('attribute_types')->insert([
                'name' => 'single line text',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'number',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'multi line text',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'multiselect',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'date and time',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'file',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'measurement',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'rating',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'json',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'true or false',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'url',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'color',
            ]);
            DB::table('attribute_types')->insert([
                'name' => 'variants',
            ]);
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
