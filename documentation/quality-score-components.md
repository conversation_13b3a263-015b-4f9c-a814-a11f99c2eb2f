# Quality Score Components Architecture

This document outlines the architecture of the reusable quality score components developed for the product and image quality score features.

## Components Overview

We've built a set of modular, reusable components to display quality scores consistently throughout the application:

1. `QualityScoreIndicator` - A progress bar with completion percentage
2. `QualityScoreCard` - A card displaying a metric with value, label, description, and action button
3. `DoughnutChart` - A circular chart displaying quality metrics
4. `ProductQualityScore` - The main product quality score component
5. `ImageQualityScores` - The main image quality score component (reused from dashboard)
6. `QualityScoresDashboard` - A parent component that displays multiple quality score cards

## Component Hierarchy

```
QualityScoresDashboard
├── First Row (two columns)
│   ├── Left Column
│   │   └── ProductQualityScore
│   │       ├── QualityScoreIndicator
│   │       ├── DoughnutChart
│   │       ├── QualityScoreCard (Valid Field)
│   │       └── QualityScoreCard (Invalid Field)
│   └── Right Column
│       └── ImageQualityScores
│           ├── DoughnutChart
│           ├── QualityScoreCard (Good)
│           ├── QualityScoreCard (Fair)
│           └── QualityScoreCard (Bad)
└── Second Row
    └── ProductQualityScore
        ├── QualityScoreIndicator
        ├── DoughnutChart
        ├── QualityScoreCard (Valid Field)
        └── QualityScoreCard (Invalid Field)
```

## Component Details

### QualityScoreIndicator

A reusable component for displaying a progress bar with completion percentage.

**Props:**

-   `percentage` (number): The completion percentage (0-100)
-   `title` (string): The title text
-   `description` (string): The description text
-   `className` (string): Additional CSS classes
-   `showHeader` (boolean): Whether to show the title and description header

### QualityScoreCard

A reusable card component for displaying quality metrics.

**Props:**

-   `value` (number): The numeric value to display (e.g., 36, 50, 2)
-   `label` (string): The label for this card (e.g., "Valid Field", "Good", "Bad")
-   `description` (string): Description text for the card
-   `color` (string): The color for the label and button (e.g., "#15D476")
-   `buttonLabel` (string): The text for the button (e.g., "Update", "Improve")
-   `onButtonClick` (function): Function to call when button is clicked
-   `className` (string): Additional CSS classes
-   `height` (number): Optional height for the card (default: 168px)

### DoughnutChart

A reusable doughnut chart component based on Recharts.

**Props:**

-   `qualityType` (string): Type of quality (e.g., "Image", "Product")
-   `scores` (object): Score data with good, fair, bad, approve, warning, error values
-   `colors` (array): Optional array of colors for the chart
-   `dataKeys` (object): Optional mapping of data keys
-   `centerLabel` (object): Optional text to display in the center
-   `startFromFirst` (boolean): Optional flag to control the order of segments

### ProductQualityScore

The main product quality score component that displays valid and invalid fields.

**Props:**

-   `product` (object): Product data
-   `onDataChange` (function): Function to call when data changes

### ImageQualityScores

The main image quality score component (reused from dashboard) that displays good, fair, and bad metrics.

**Props:**

-   `title` (string): Title text for the header
-   `description` (string): Description text for the header
-   `showHeader` (boolean): Whether to show the header
-   `className` (string): Additional CSS classes
-   `scores` (object): Score data with good, fair, bad values

### QualityScoresDashboard

A parent component that displays multiple quality score cards in a layout matching the design.

**Props:**

-   `product` (object): Product data

## Layout Structure

The QualityScoresDashboard component organizes the quality score components in a responsive layout:

1. First row displays two columns side by side:
    - Left column: ProductQualityScore (EN-US PRODUCT QUALITY SCORE)
    - Right column: ImageQualityScores (Images Quality Score with Good/Fair/Bad cards)
2. Second row displays a full-width column:
    - ProductQualityScore (EN-US PRODUCT QUALITY SCORE again)

The layout is responsive and will stack vertically on smaller screens.

## Color Scheme

-   **Good/Valid**: `#15D476` (Green)
-   **Average/Warning/Fair**: `#FF9C3E` (Orange)
-   **Bad/Invalid**: `#FE1F23` (Red)

## Usage Examples

### Complete Quality Scores Dashboard

```jsx
<QualityScoresDashboard product={product} />
```

### Individual Product Quality Score

```jsx
<ProductQualityScore product={product} onDataChange={handleDataChange} />
```

### Image Quality Scores

```jsx
<ImageQualityScores
    showHeader={true}
    scores={imageQualityScores}
    title="Images Quality Score"
    description="Track Image Quality Metrics for Your Products."
/>
```

### Using Smaller Components Individually

```jsx
// Progress indicator
<QualityScoreIndicator
  percentage={80}
  title="Product Quality"
  description="Track product quality metrics"
/>

// Quality metric card
<QualityScoreCard
  value={36}
  label="Valid Field"
  description="These fields don't require immediate attention"
  color="#15D476"
  buttonLabel="Update"
  onButtonClick={handleUpdate}
/>

// Doughnut chart
<DoughnutChart
  qualityType="Image"
  scores={{
    good: 50,
    fair: 30,
    bad: 20
  }}
/>
```

## Future Improvements

-   Add animation effects for better user experience
-   Implement responsive design for mobile views
-   Add unit tests for all components
-   Implement data fetching with loading states
-   Add tooltips for additional information
