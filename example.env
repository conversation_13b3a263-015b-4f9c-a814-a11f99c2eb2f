APP_NAME="Apimio Local"
APP_ENV=local
APP_KEY=base64:KmxW1oYg1WTGkQMsy9egtJRGUWyoebUZxObAD3zFW0I=
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_VERSION=10.0

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST1=127.0.0.1
DB_HOST=apimio.clsqeeg2qimy.us-east-2.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=vapor
DB_DATABASE1=apimio
DB_USERNAME=vapor
DB_USERNAME1=root
DB_PASSWORD=dda5mUvyVk0WPHmSthSNAFSZQlaxLGYZUWj3f8dj

BROADCAST_DRIVER=pusher
CACHE_DRIVER=file
QUEUE_CONNECTION=sqs
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=*********************************************************************
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID1=********************
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY1=yMkTmItzjguVMuBhoj+McViWtF6BhHFlhKR01jHv
AWS_SECRET_ACCESS_KEY=x2nJRAXpi/gyYcIUUP/q7NAJIa56y/wOGSzlN9os
AWS_DEFAULT_REGION=us-east-2
AWS_BUCKET=apimio-staging
AWS_MAIN_URL=https://apimio-staging.s3.amazonaws.com/
SQS_PREFIX=https://sqs.us-east-2.amazonaws.com/574295680953/

PUSHER_APP_ID=*******
PUSHER_APP_KEY=e55f61295ab31a355055
PUSHER_APP_SECRET=353aab4192d2b3edff6d
PUSHER_APP_CLUSTER=us2

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

LOG_SLACK_WEBHOOK_URL=*******************************************************************************
LOG_SLACK_WEBHOOK_URL_STAGING=*******************************************************************************

STRIPE_KEY=pk_test_aXkxuuqQz8k881VU3C1Jvehg
STRIPE_SECRET=sk_test_bW7sPjB0LmgvOZkXaxFftHSm

TEST_MONTHLY_ID=price_1NL1krJKtHOhwx4oRXBin9di
TEST_YEARLY_ID=price_1NL1krJKtHOhwx4oRXBin9di

STARTUP_MONTHLY_PRICE_ID=price_1MBcqGJKtHOhwx4od4nzmP0S
STARTUP_YEARLY_PRICE_ID=price_1MBcqGJKtHOhwx4oPlvfHYNu
PLUS_MONTHLY_PRICE_ID=price_1M6rZXJKtHOhwx4oXsFJwlYs
PLUS_YEARLY_PRICE_ID=price_1M6rZXJKtHOhwx4ol6J3P0LO
PRO_MONTHLY_PRICE_ID=price_1M6rbWJKtHOhwx4oq4Ddh4X2
PRO_YEARLY_PRICE_ID=price_1M6rbWJKtHOhwx4oLa58CfkQ

GOOGLE_CLIENT_ID=875143810217-cvsil8bcvajiiq05653n8b5ifr0mv2c4.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=dXAoKDotGurtVJ0yP8dlZ7ro
GOOGLE_REDIRECT=http://localhost:8000/auth/google/callback
CASHIER_MODEL=App\Models\Organization\Organization

SHOPIFY_API_KEY=2506ddcfac5003bd1a8d871c1b2d4f09
SHOPIFY_SHARED_SECRET=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
SHOPIFY_SCOPES=write_products,read_inventory,write_inventory,read_locations
SHOPIFY_REDIRECT_URI=http://localhost:8000/channel/shopify/redirect
SHOPIFY_TEST_ENV=true
SHOPIFY_TEST_STORE_TOKEN=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

MAILCHIMP_APIKEY=*************************************
MAILCHIMP_APIMIO_LIST_ID=9f47ff5275

RECAPTCHAV3_SITEKEY=6LcEc74dAAAAAP4EkAsRdt9rar9XgEEdEu2MdpH6
RECAPTCHAV3_SECRET=6LcEc74dAAAAAMcGyFKBEhrTn_8Fk_zMj2GXUjiM

SHOPIFY_WEBHOOK_PRODUCT_CREATE=${APP_URL}/webhook/shopify/create-product
SHOPIFY_WEBHOOK_PRODUCT_DELETE=${APP_URL}/webhook/shopify/delete-product
SHOPIFY_WEBHOOK_UNINSTALL_APP=${APP_URL}/webhook/shopify/uninstall-app
SHOPIFY_WEBHOOK_CREATE_COLLECTION=${APP_URL}/webhook/shopify/create-collection
SHOPIFY_WEBHOOK_UPDATE_COLLECTION=${APP_URL}/webhook/shopify/update-collection
SHOPIFY_WEBHOOK_PRODUCT_UPDATE=${APP_URL}/webhook/shopify/update-product
SHOPIFY_WEBHOOK_CREATE_LOCATION=${APP_URL}/webhook/shopify/location/create
SHOPIFY_WEBHOOK_UPDATE_LOCATION=${APP_URL}/webhook/shopify/location/update
SHOPIFY_WEBHOOK_DELETE_LOCATION=${APP_URL}/webhook/shopify/location/delete
SHOPIFY_WEBHOOK_CREATE_INVENTORY=${APP_URL}/webhook/shopify/inventory/create
SHOPIFY_WEBHOOK_UPDATE_INVENTORY=${APP_URL}/webhook/shopify/inventory/update
SHOPIFY_WEBHOOK_DELETE_INVENTORY=${APP_URL}/webhook/shopify/inventory/delete
SHOPIFY_WEBHOOK_INVENTORY_LEVELS=${APP_URL}/webhook/shopify/inventory/levels

TERM_OF_SERVICE_URL=https://apimio.com/terms/
PRIVACY_POLICY_URL=https://apimio.com/privacypolicy/
USER_ACCOUNT_URL=https://support.apimio.com/index.php/knowledge-base/starting-your-14-days-free-trial/
GET_IN_TOUCH_URL=https://apimio.com/contact-us/

HUBSPOT_ACCESS_TOKEN=********************************************
