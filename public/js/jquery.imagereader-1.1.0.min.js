"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};/**
*  jQuery Image Reader v1.1.0
*  (c) 2016 Ipan Ardian
*
*  A jQuery plugin that previews image very fast without needing to upload to your server
*  For details, see the web site: https://github.com/ipanardian/jquery-image-reader
*  The MIT License
*/
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"===("undefined"==typeof module?"undefined":_typeof(module))&&module.exports?module.exports=function(n,t){return void 0===t&&(t="undefined"!=typeof window?require("jquery"):require("jquery")(n)),e(t),t}:e(jQuery)}(function(e){e.fn.imageReader=function(n){var t={destination:"#image-preview",renderType:"image",onload:function(){}},o=Object.assign(t,n);if(!("FileReader"in window))return void console.error("Your browser does not support FileReader API");var i=new Set(["image/jpeg","image/jpg","image/png","image/gif","image/svg+xml","image/bmp","image/x-icon","image/vnd.microsoft.icon"]),a={container:e(o.destination),clearContainer:function(){this.container.html("")},validateMimeType:function(e){return i.has(e)},processRead:function(e){var n=this;if(!this.validateMimeType(e.type))return void console.warn("Invalid file type");try{var t=new FileReader;t.onload=function(t){switch(o.renderType){case"canvas":n.renderObjectToCanvas(e,t);break;default:n.renderObjectToImage(e,t)}},t.readAsDataURL(e)}catch(i){throw new Error(i.message)}},createImage:function(e,n){var t=new Image;"function"==typeof n&&(t.onload=function(){n(t)}),t.src=e.target.result},renderObjectToImage:function(e,n){var t=this;this.createImage(n,function(n){t.container.append(n),t.callback(e,n)})},renderObjectToCanvas:function(e,n){var t=this,o=document.createElement("canvas"),i=o.getContext("2d");this.createImage(n,function(n){o.width=n.width,o.height=n.height,i.drawImage(n,0,0),t.container.append(o),t.callback(e,o)})},callback:function(e,n){o.onload.call(e,n)}};return this.each(function(){var n=this;e(this).on("change",function(){a.clearContainer();for(var e=0,t=n.files.length;t>e;e++)a.processRead(n.files[e])})})}});