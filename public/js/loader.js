//
// window.loader_show = loader_show;
// function loader_show(ID, id1, id2){
//     $(ID).click(function () {
//         $(ID).hide();
//         $(id1).show();
//         $(id2).show();
//     });
// }
//
// function loader_hide(id1, id2)
// {
//     $(id1).hide();
//     $(id2).hide();
// }
let load = {
    loadershow: function (ID, id1, id2){
        $(ID).click(function () {
            $(ID).hide();
            $(id1).show();
            $(id2).show();
        });

        // $('#button1').click(function () {
        //     $('#icon').hide();
        //     $('#load').show();
        // });
    },

    loaderhide:function (id1, id2){
        $(id1).hide();
        $(id2).hide();
        // $(function () {
        //     $("#load").hide();
        // });
    }
}

// loadwithicon = {
//     loadershow: function (ID, id1, id2){
//         $(ID).click(function () {
//             $(id1).hide();
//             $(id2).show();
//         });
//
//         // $('#button1').click(function () {
//         //     $('#icon').hide();
//         //     $('#load').show();
//         // });
//     },
//
//     loaderhide:function (id2){
//         $(id2).hide();
//         // $(function () {
//         //     $("#load").hide();
//         // });
//     }
// }
