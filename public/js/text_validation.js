function input_status(obj) {
    if ($(obj).attr('data-rules')) {

        let data_rule = $(obj).attr('data-rules');
        let data_id = $(obj).attr('data-status-id');

        let data_rule_array = data_rule.split("|");

        let self = $(obj);
        let flag = true; // true = success, false = error

        data_rule_array.forEach(function (item) {

            if (item === "required") {
                if (self.val().length < 1) {
                    $(data_id).attr("data-content",
                        $(data_id).attr("data-content") +
                        "• This is a required field.<br>"
                    )
                    flag = false;
                    return 0;
                }
            }
            if (item.search('max') === 0) {
                if (self.val().length > parseInt(item.split(":")[1])) {
                    $(data_id).attr("data-content",
                        $(data_id).attr("data-content") +
                        "• Character length should not be greater than " + item.split(":")[1] + ".<br>"
                    )
                    flag = false;
                    return 0;
                }
            }
            if (item.search('min') === 0 || self.val() < 0) {
                if (self.val().length < parseInt(item.split(":")[1])) {
                    $(data_id).attr("data-content",
                        $(data_id).attr("data-content") +
                        "• Character length should be greater than " + item.split(":")[1] + ".<br>"
                    )
                    flag = false;
                    return 0;
                }
                if(self.val()<0){
                    flag = false;
                    return 0;
                }
            }
            if (item === "number") {
                if (self.val().search(/^[0-9.]+$/) === -1) {
                    $(data_id).attr("data-content",
                        $(data_id).attr("data-content") +
                        "• This field can only contain numbers with decimals.<br>"
                    )
                    flag = false;
                    return 0;
                }
            }
            if (item === "slug") {
                if (self.val().search(/^[a-zA-Z0-9-_]+$/) === -1) {
                    $(data_id).attr("data-content",
                        $(data_id).attr("data-content") +
                        "• This field should only contain alphanumerics and dashes.<br>"
                    )
                    flag = false;
                    return 0;
                }
            }
        });

        if (flag) {
            $(data_id).removeClass("circle-error", 1000);
            $(data_id).addClass("circle-success", 1000);
        } else {
            $(data_id).removeClass("circle-success", 1000);
            $(data_id).addClass("circle-error", 1000);
        }
    }
}
