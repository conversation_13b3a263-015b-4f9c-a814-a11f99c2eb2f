/**@license
 *  _____
 * |_   _|___ ___ ___ ___ ___
 *   | | | .'| . | . | -_|  _|
 *   |_| |__,|_  |_  |___|_|
 *           |___|___|   version 0.4.3
 *
 * Tagger - Zero dependency, Vanilla JavaScript Tag Editor
 *
 * Copyright (c) 2018-2022 J<PERSON><PERSON> <PERSON><PERSON> <https://jcubic.pl/me>
 * Released under the MIT license
 */
.tagger {
    background-color: #F8F8F8;
    border: 1px solid #E5E5E5;
    border-radius: 4px;
}
.tagger input[type="hidden"] {
  /* fix for bootsrap */
  display: none;
}
.tagger > ul {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    align-items: center;
    padding: 4px 4px 0px 0px;
    /*justify-content: space-between;*/
    box-sizing: border-box;
    height: auto;
}
.tagger ul {
    margin: 0;
    list-style: none;
}
.tagger > ul > li {
    margin: 0.4rem 0;
    padding-left: 10px;
}
.tagger > ul > li:not(.tagger-new) a,
.tagger > ul > li:not(.tagger-new) a:visited,
.tagger-new ul a,
.tagger-new ul a:visited {
    color: black;
    font-weight: 400;
}
.tagger > ul > li:not(.tagger-new) > a,
.tagger li:not(.tagger-new) > span,
.tagger .tagger-new ul {
    padding: 4px 4px 4px 8px;
    background: #e2e4e7;
    border: 1px solid #c5cddb;
    word-break: break-all;
    border-radius: 3px;
}
.tagger li a.close {
    padding: 4px;
    margin-left: 4px;
    /* for bootsrap */
    float: none;
    filter: alpha(opacity=100);
    opacity: 1;
    font-size: 16px;
    line-height: 16px;
}
.tagger li a.close:hover {
    color: white;
}
.tagger li:not(.tagger-new) a {
    text-decoration: none;
}
.tagger .tagger-new input {
    border: none;
    outline: none;
    box-shadow: none;
    height: 20px !important;
    width: 100%;
    padding-left: 0;
    background: transparent;
}
.tagger .tagger-new {
    flex-grow: 1;
    position: relative;
}
.tagger .tagger-new ul {
    padding: 5px;
}
.tagger .tagger-completion {
    position: absolute;
    z-index: 100;
}
.tagger.wrap > ul {
    flex-wrap: wrap;
    justify-content: start;
}
