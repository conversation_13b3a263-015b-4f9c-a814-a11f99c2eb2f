$( document ).ready(function() {
    const product_link=document.querySelector('.product-link');
    const main_sidebar=document.querySelector('.main-sidebar');
    const submenu= document.querySelector('.submenu');
    const logo_Image= document.querySelector('.mobile-logo');
    const planTime=document.querySelector('.billing-custom-css')
    const screen_width1 = document.documentElement.clientWidth;

    if(screen_width1<=768){
        logo_Image.classList.remove('w-19');
    }
    product_link.addEventListener('click',function (e) {
        e.preventDefault();
        main_sidebar.classList.toggle("mobile-menu");
        submenu.classList.toggle("mobile-sub-menu");
        planTime.classList.toggle('d-none');
    })
    window.addEventListener("resize", function () {
        const screen_width = document.documentElement.clientWidth;
        if(screen_width<=768){
            logo_Image.classList.remove('w-19');
        }
    });

    $('.right-side').on('click', function () {
            if(main_sidebar.classList.contains("mobile-menu")){
                main_sidebar.classList.remove('mobile-menu');
            }
    });

});
