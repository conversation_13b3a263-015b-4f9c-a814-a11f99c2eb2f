$(document).ready(function () {
    $('#list-of-attributes').SumoSelect({triggerChangeCombined: false});
    $(".SumoSelect li").bind('click.check', function(event) {
        let trimcheck = $.trim($(this).text());
        let is_checked = $(this).hasClass('selected');
        $(".SumoSelect li").each(function() {
            const loopval = $.trim($(this).text());
            if(trimcheck===loopval) {
                if(is_checked) {
                    if (!$(this).hasClass("selected")) {
                        $('#list-of-attributes')[0].sumo.disableItem($(this).index());
                    }
                }
                else {
                    $('#list-of-attributes')[0].sumo.enableItem($(this).index());
                }
            }
        });
    });



    $('#list-of-attributes').on('sumo:opened', function(sumo) {
        let trimcheck = '';
        $("#list-of-attributes option:selected").each(function () {
            var $this = $(this);
            if ($this.length) {
                trimcheck =$.trim($this.text())
                let is_checked = this.hasAttributes('selected');
                $(".SumoSelect li").each(function() {
                    console.log($.trim($(this).text()))
                    if(trimcheck===$.trim($(this).text())) {
                        if(is_checked) {
                            if (!$(this).hasClass("selected")) {
                                $('#list-of-attributes')[0].sumo.disableItem($(this).index());
                            }
                        }
                        else {
                            $('#list-of-attributes')[0].sumo.enableItem($(this).index());
                        }
                    }
                });
            }
        });

    });
});
