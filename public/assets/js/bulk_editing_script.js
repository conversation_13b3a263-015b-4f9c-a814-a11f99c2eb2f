function multiselectAttribute() {
    if ($("#multiselect_products").is(":checked")) {
        // $(".filter_bulk_productIds").val("all");
        $(".bulk_product_check").prop("checked", true);

        const selectedCheckboxes = $(".bulk_product_check:checkbox:checked");
        $("#totalCount").text(selectedCheckboxes.length);
        $(".bulk_product_check").addClass("checked");
        $(".bulk-footer").removeClass("d-none");
    } else {
        $(".filter_bulk_productIds").val("");
        $(".bulk_product_check").prop("checked", false);
        $(".bulk_product_check").removeClass("checked");
        $(".bulk-footer").addClass("d-none");
    }
}

$(document).ready(function () {
    $(document).on("change", '#listingTable input[type="checkbox"]', function () {
        if ($(this).data("id")) {
            toggle_class_Attribute($(this).data("id"));
        } else {
            multiselectAttribute();
        }
    });
});

function toggle_class_Attribute(id) {
    const totalCount = document.querySelectorAll(".bulk_product_check").length;
    const selectedCheckboxes = $(".bulk_product_check:checkbox:checked");
    if (selectedCheckboxes.length < totalCount) {
        $(".bulk-checkbox").prop("checked", false);
    } else {
        $(".bulk-checkbox").prop("checked", true);
    }

    $("#totalCount").text($(".bulk_product_check:checkbox:checked").length);
    if ($(".Bulk_product_" + id).hasClass("checked") && $(".bulk_product_check:checkbox:checked").length === 0) {
        $(".Bulk_product_" + id).removeClass("checked");
        $(".bulk-footer").addClass("d-none");
    } else {
        $(".Bulk_product_" + id).addClass("checked");
        $(".bulk-footer").removeClass("d-none");
    }
}

function reset() {
    $("#section1").removeClass("d-none");
    $("#section2").addClass("d-none");
    $("#attribute-back-button").addClass("d-none");
    $("#attribute-save-button").addClass("d-none");
    $("#attribute-cancel-button").removeClass("d-none");
    $("#attribute-next-button").removeClass("d-none");
}
function next() {
    $("#section1").addClass("d-none");
    $("#section2").removeClass("d-none");
    $("#attribute-back-button").removeClass("d-none");
    $("#attribute-save-button").removeClass("d-none");
    $("#attribute-cancel-button").addClass("d-none");
    $("#attribute-next-button").addClass("d-none");
}

$(document).on("click", '#section1 input[type="checkbox"]', () => {
    const section1Ids = $('#section1 input[type="checkbox"]:checked')
        .map(function () {
            return this.id;
        })
        .get();
    if (section1Ids.length === 0) {
        $("#attribute-next-button").addClass("disabled");
    } else {
        $("#attribute-next-button").removeClass("disabled");
    }
});

$("#bulk_assign_form").on("submit", function (event) {
    // Check if the checkboxes are checked
    if (!$("#customSwitch_track_quantity").is(":checked")) {
        $('input[name="attributes[track_quantity]"]').remove();
    }
    if (!$("#customSwitch_continue_selling").is(":checked")) {
        $('input[name="attributes[continue_selling]"]').remove();
    }
});
$(document).on("click", "#attribute-next-button", () => {
    next();
    // section1 ids
    const section1Id = $('#section1 input[type="checkbox"]:checked')
        .map(function () {
            return $(this).data("slug");
        })
        .get();

    if (section1Id.includes("attribute_sets")) {
        $(".attribute-set-js").removeClass("d-none");
    } else {
        $(".attribute-set-js").addClass("d-none");
    }
    if (section1Id.includes("categories")) {
        $(".category-js").removeClass("d-none");
    } else {
        $(".category-js").addClass("d-none");
    }
    if (section1Id.includes("brands")) {
        $(".brands-js").removeClass("d-none");
    } else {
        $(".brands-js").addClass("d-none");
    }
    if (section1Id.includes("weight")) {
        $(".weight-js").removeClass("d-none");
    } else {
        $(".weight-js").addClass("d-none");
    }
    if (section1Id.includes("continue_selling")) {
        $(".continue_selling-js").removeClass("d-none");
        $('input[name="attributes[continue_selling]"]').val("1");
    } else {
        $(".continue_selling-js").addClass("d-none");
        $('input[name="attributes[continue_selling]"]').val("");
    }
    if (section1Id.includes("track_quantity")) {
        $(".track_quantity-js").removeClass("d-none");

        $('input[name="attributes[track_quantity]"]').val("1");
    } else {
        $(".track_quantity-js").addClass("d-none");
        $('input[name="attributes[track_quantity]"]').val("");
    }
    if (section1Id.includes("quantity")) {
        $(".quantity-js").removeClass("d-none");
    } else {
        $(".quantity-js").addClass("d-none");
    }
    if (section1Id.includes("vendors")) {
        $(".vendors-js").removeClass("d-none");
    } else {
        $(".vendors-js").addClass("d-none");
    }
    if (section1Id.includes("tags")) {
        $(".tags-js").removeClass("d-none");
    } else {
        $(".tags-js").addClass("d-none");
    }
    if (section1Id.includes("stores")) {
        $(".store-js").removeClass("d-none");
    } else {
        $(".store-js").addClass("d-none");
    }
    $("#selected_options").val(section1Id);
});
$(document).on("change", "input.assign_flag", function () {
    var $continueSellingInput = $('input[name="attributes[continue_selling]"]');
    var $trackQuantityInput = $('input[name="attributes[track_quantity]"]');
    const section1Id = $('#section1 input[type="checkbox"]:checked')
        .map(function () {
            return $(this).data("slug");
        })
        .get();
    if ($(this).is(":checked")) {
        $continueSellingInput.val("1").prop("checked", true);
        $trackQuantityInput.val("1").prop("checked", true);
    } else {
        if (section1Id.includes("track_quantity") && section1Id.includes("continue_selling")) {
            $continueSellingInput.val("0").prop("checked", false);
            $trackQuantityInput.val("0").prop("checked", false);
        } else if (section1Id.includes("continue_selling")) {
            $continueSellingInput.val("0").prop("checked", false);
            $trackQuantityInput.val("").prop("checked", false);
        } else if (section1Id.includes("track_quantity")) {
            $trackQuantityInput.val("0").prop("checked", false);
            $continueSellingInput.val("").prop("checked", false);
        }
    }
});

$(document).on("click", "#attribute-back-button", () => {
    reset();
});
$(document).on("click", ".cancel-button-js", () => reset());

$(document).ready(function () {
    $(document).on("submit", "#bulk_assign_form", function (event) {
        $("#bulk_assign_form").find("button[type='submit']").prop("disabled", true);

        if ($(this).data("actiontype") == "assign") {
            $("#bulk_assign_form")
                .find("button[type='submit']")
                .html(`<div class="spinner-border text-primary spinner-border-sm" role="status"></div>`);
            event.target.submit();
            return false;
        }


        // Get input values
        var priceValue = $(".bulk_price").val(); // Price field
        var salePrice = $(".sale_price_input").val(); // Sale price (%)
        var isSchedule = $("#is_schedule").is(":checked"); // Is scheduled?
        var scheduledAt = $("#scheduled_at").val(); // Scheduled date and time
        var timezone = $("#timezone").val(); // Scheduled timezone

        var selected_options = $("#selected_options").val(); // Selected options

        var isReverse = $("#sale_reverse").is(":checked"); // Is reverse schedule?
        var reverseScheduledAt = $("#reverse_scheduled_at").val(); // Reverse schedule date and time
        var reverseTimezone = $("#reverse_timezone").val(); // Reverse timezone

        var edit_task = $("#edit_task").val(); // Edit task

        var confirmationMessage = "";

        // Prevent submission if both price and sale price are empty
        if (!priceValue && !salePrice && selected_options == 'price') {
            Swal.fire({
                title: "Missing Input!",
                text: "Please input a value for either Price or Sale before proceeding.",
                icon: "error",
                confirmButtonText: "OK",
            });
            $("#bulk_assign_form").find("button[type='submit']").prop("disabled", false);
            event.preventDefault();
            return;
        }

        if (selected_options == "price" && edit_task === "update" && (!isSchedule || (isSchedule && !scheduledAt) || (isReverse && !reverseScheduledAt))) {
            Swal.fire({
                title: "Missing Input!",
                text: "Please add schedule date and time for update.",
                icon: "error",
                confirmButtonText: "OK",
            });
            $("#bulk_assign_form").find("button[type='submit']").prop("disabled", false);
            event.preventDefault();
            return;
        }

        // Helper function to format date and time
        function formatDateTime(dateTime) {
            if (!dateTime) return "";
            const options = {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
                hour12: true
            };
            return new Date(dateTime).toLocaleString("en-US", options);
        }

        // Construct confirmation message for price
        if (priceValue) {
            // if (priceValue == "0" || priceValue == "+0%" || priceValue == "-0") {
            //     confirmationMessage += "You are offering products for free.<br><br>";
            // }
            // else if (priceValue.startsWith("+")) {
            //     confirmationMessage += "You are adding <b>" + priceValue.substr(1) + "</b> to the actual price.<br><br>";
            // } else if (priceValue.startsWith("-")) {
            //     confirmationMessage += "You are subtracting <b>" + priceValue.substr(1) + "</b> from the actual price.<br><br>";
            // } else if (priceValue.endsWith("%")) {
            //     confirmationMessage += "You are applying <b>" + priceValue + "</b> to the actual price.<br><br>";
            // }
            // else {
                confirmationMessage += "You are applying the following changes in Price:  <b>" + priceValue + "</b>.<br><br>";
            // }
        }

        // Construct confirmation message for sale price
        if (salePrice) {
            confirmationMessage += "You are applying a sale of <b>" + salePrice + "%</b> on selected products.<br><br>";
        }

        // Scheduled update message
        if(isSchedule && scheduledAt && timezone && isReverse && reverseScheduledAt && reverseTimezone){
            confirmationMessage += "Your price updates are scheduled to run on <b>"
                + formatDateTime(scheduledAt)
                + "</b> ("
                + timezone
                + ") and will revert on <b>"
                + formatDateTime(reverseScheduledAt)
                + "</b> ("
                + reverseTimezone
                + ").<br><br>";
        }
        else if (isSchedule && scheduledAt && timezone) {
            confirmationMessage += "Your price updates are scheduled to run on <b>" + formatDateTime(scheduledAt) + "</b> (" + timezone + ").<br><br>";
        }

        // Show confirmation dialog
        Swal.fire({
            title: "Confirm Price Update",
            html: confirmationMessage, // Use 'html' to allow formatted text and line breaks
            icon: "info",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, proceed!",
        }).then((result) => {
            if (result.isConfirmed) {
                $("#bulk_assign_form")
                    .find("button[type='submit']")
                    .html(`<div class="spinner-border text-primary spinner-border-sm" role="status"></div>`);
                event.target.submit();
            } else {
                $("#bulk_assign_form").find("button[type='submit']").prop("disabled", false);
            }
        });

        event.preventDefault();
    });




    $(document).on("keyup", ".apply_calculate_validation_js", function () {
        calculate_input_validate($(this));
    });

    $(document).on("keydown", ".apply_calculate_validation_js", function (event) {
        var keyCode = event.keyCode || event.which;
        // Allow backspace, delete, tab, enter, and special keys (e.g., arrow keys)
        if (keyCode === 8 || keyCode === 46 || keyCode === 9 || keyCode === 13 || event.ctrlKey || event.altKey || event.metaKey) {
            return;
        }

        // Allow numbers, ".", "+", "-", and "%" special characters
        if (
            (keyCode >= 48 && keyCode <= 57) ||
            keyCode === 190 ||
            keyCode === 187 ||
            keyCode === 189 ||
            keyCode === 53 ||
            keyCode === 109 ||
            keyCode === 107 ||
            keyCode === 110 ||
            (keyCode >= 37 && keyCode <= 40) ||
            (keyCode >= 96 && keyCode <= 105)
        ) {
            return;
        }
        // Prevent all other characters
        event.preventDefault();
    });
});

function calculate_input_validate(m_this , message = 'Please match your values according to the given examples') {
    m_this = $(m_this);

    var value = m_this.val().trim().toLowerCase();

    // Define the valid patterns using regular expressions
    var validPatterns = [
    /^[+-]\d+$/,       // Matches whole numbers with mandatory + or - (e.g., +50, -50)
    /^[+-]\d+%$/,      // Matches percentage values with mandatory + or - (e.g., +50%, -50%)
];

    var isValid = validPatterns.some(function (pattern) {
        return pattern.test(value);
    });

    if (!isValid) {
        m_this.css("border", "2px solid red");
        m_this.next(".text-danger").remove();
        m_this.after('<span class="text-danger small">'+message+'</span>');
        $("#attribute-save-button").prop("disabled", true);
    } else {
        m_this.css("border", "1px solid #ccc");
        m_this.next(".text-danger").remove();
        $("#attribute-save-button").prop("disabled", false);
    }
}
