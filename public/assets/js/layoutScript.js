$(function () {
    $("body").tooltip({ selector: "[data-bs-toggle=tooltip]" });
});

/**
 * Ask for confirmation if any thing is changed by user in current page.
 * */
$("[data-confirm-before-leave]").change(function () {
    $(window).on("beforeunload", function () {
        return confirm("You save some unsaved data, Do you want to leave?");
    });

    $(this)
        .parents("form")
        .submit(function () {
            $(window).unbind("beforeunload");
        });
});

// sumo select script
$(document).ready(function () {
    $(".sumoselect").SumoSelect({
        csvDispCount: 1,
        captionFormat: "({0}) Selected",
        captionFormatAllSelected: "({0}) Selected",
        okCancelInMulti: true,
        isClickAwayOk: true,
        selectAll: true,
        search: false,
        searchText: "Search here",
        noMatch: "No matches for {0}",
    });
});

//Prevent form submitting multiple times
$(document).on('click', "button[type='submit']", function (event) {
    if (!$(this).hasClass("search") && !$(this).hasClass("bulk_module_js")) {
        event.preventDefault();
        var form = $(this).closest("form");
        let passValidation = true;
        $(form.find("select[required],input[required]").get().reverse()).each(function (index, obj) {
            if (!obj.checkValidity()) {
                obj.reportValidity();
                passValidation = false;
                return;
            }
        })
        if (passValidation){
            var previous_text = $(this).html();
            $(this).attr("disabled", true);
            form.submit();
            if ($(this).hasClass('onclick-disabled-with-loader')) {
                $(this).css("opacity", "0.5");
                $(this).css("pointer-events", "none");
                $(this).html(`<div class="spinner-border text-primary spinner-border-sm" role="status"></div>`);
            }else{
                $(this).html(`<div class="spinner-border text-primary spinner-border-sm" role="status"></div>`);
                let _this = this;
                setTimeout(function () {
                    $(_this).prop("disabled", false);
                    $(_this).html(previous_text);
                }, 5000);
            }
        }
    }
});

//Prevent form submitting multiple times
$(".type_button").click(function () {
    if (!$(this).hasClass("search")) {
        var previous_text = $(this).html();
        $(this).attr("disabled", true);
        $(this).html("Please wait..");
    }
});

$(".disabled-with-text").click(function () {
    if (!$(this).hasClass("search")) {
        var previous_text = $(this).html();
        $(this).attr("disabled", true);
        $(this).css("pointer-events", "none");
        $(this).html("Please wait..");
        let _this = this;
        setTimeout(function () {
            $(_this).prop("disabled", false);
            $(_this).html(previous_text);
        }, 5000);
    }
});
$(".only-disabled").click(function () {
    if (!$(this).hasClass("search")) {
        var previous_text = $(this).html();
        $(this).attr("disabled", true);
        $(this).css("opacity", "0.5");
        $(this).css("pointer-events", "none");
        let _this = this;
        setTimeout(function () {
            $(_this).prop("disabled", false);
            $(_this).html(previous_text);
        }, 5000);
    }
});

let links = document.querySelectorAll(".ripplelink");
for (let i = 0, len = links.length; i < len; i++) {
    links[i].addEventListener(
        "click",
        function (e) {
            let targetEl = e.target;
            let inkEl = targetEl.querySelector(".ink");

            if (inkEl) {
                inkEl.classList.remove("animate");
            } else {
                inkEl = document.createElement("span");
                inkEl.classList.add("ink");
                inkEl.style.width = inkEl.style.height = Math.max(targetEl.offsetWidth, targetEl.offsetHeight) + "px";
                targetEl.appendChild(inkEl);
            }

            inkEl.style.left = e.offsetX - inkEl.offsetWidth / 2 + "px";
            inkEl.style.top = e.offsetY - inkEl.offsetHeight / 2 + "px";
            inkEl.classList.add("animate");
        },
        false
    );
}

// check duplications
function checkDuplicate(arr) {
    // empty object
    let map = {};
    let result = false;
    for (let i = 0; i < arr.length; i++) {
        if (map[arr[i]]) {
            result = true;
            break;
        }
        map[arr[i]] = true;
    }
    if (result) {
        return true;
    } else {
        return false;
    }
}

// validation for duplicates
$(document).on("keyup", ".duplicate-validation", function () {
    var values = [];

    var form_button = $(this).parents("form:first").find("button[type='submit']");
    $(".duplicate-validation").each(function () {
        if (values.indexOf(this.value) >= 0) {
            $(this).css("border-color", "red");
            form_button.prop("disabled", true);
            form_button.css("pointer-events", "none");
        } else {
            $(this).css("border-color", "");
            form_button.prop("disabled", false);
            form_button.css("pointer-events", ""); //clears since last check
        }
        values.push(this.value);

        if (checkDuplicate(values)) {
            form_button.prop("disabled", true);
        }
    });
});
